#!/usr/bin/env python3
"""
Test LM Studio Integration
Quick test to verify LM Studio integration is working
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all imports work"""
    print("🧪 Testing LM Studio Integration")
    print("=" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        print("✅ LMStudioClient imported successfully")
    except Exception as e:
        print(f"❌ LMStudioClient import failed: {e}")
        return False
    
    try:
        from story.story_generator import StoryGenerator
        print("✅ StoryGenerator imported successfully")
    except Exception as e:
        print(f"❌ StoryGenerator import failed: {e}")
        return False
    
    return True

def test_client_creation():
    """Test LM Studio client creation"""
    print("\n📡 Testing LM Studio Client Creation")
    print("-" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://localhost:1234",
                "default_model": "local-model",
                "timeout": 120
            }
        }
        
        client = LMStudioClient(config)
        print("✅ LMStudioClient created successfully")
        
        # Test availability (this will fail if LM Studio isn't running, but that's OK)
        is_available = client.is_available()
        if is_available:
            print("✅ LM Studio server is running and available")
            
            # Test getting models
            models = client.get_available_models()
            print(f"✅ Available models: {len(models)} found")
            
            current_model = client.get_current_model()
            if current_model:
                print(f"✅ Current model: {current_model}")
            else:
                print("⚠️  No model currently loaded")
                
        else:
            print("⚠️  LM Studio server not available (this is OK if not running)")
            print("   To test fully, start LM Studio and load a model")
        
        return True
        
    except Exception as e:
        print(f"❌ LM Studio client creation failed: {e}")
        return False

def test_story_generator_creation():
    """Test story generator creation with LM Studio"""
    print("\n📚 Testing Story Generator Creation")
    print("-" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        from story.story_generator import StoryGenerator
        
        config = {
            "lmstudio": {
                "base_url": "http://localhost:1234",
                "default_model": "local-model",
                "timeout": 120
            },
            "story_generation": {
                "num_entry_points": 3,
                "min_endings": 5,
                "max_nodes": 100
            },
            "inventory_system": {
                "initial_inventory": {"gold": 100}
            },
            "class_system": {
                "classes": ["Mage", "Ranger", "Charmer"]
            }
        }
        
        client = LMStudioClient(config)
        story_generator = StoryGenerator(config, client)
        print("✅ StoryGenerator created successfully with LMStudioClient")
        
        return True
        
    except Exception as e:
        print(f"❌ Story generator creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 LM Studio Integration Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Client Creation", test_client_creation),
        ("Story Generator", test_story_generator_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\nLM Studio integration is working correctly!")
        print("\n📋 Next steps:")
        print("1. Start LM Studio and load a model to test full functionality")
        print("2. Run: pixi run run  (to start the GUI)")
        print("3. Or run: pixi run wizard  (for CLI story creation)")
        return 0
    else:
        print(f"\n⚠️  {total - passed} TESTS FAILED")
        print("\nSome components need attention.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
