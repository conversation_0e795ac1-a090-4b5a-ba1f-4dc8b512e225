#!/usr/bin/env python3
"""
Quick test to check LM Studio connection
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    print("🔍 Quick LM Studio Connection Test")
    print("=" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://127.0.0.1:1234",
                "default_model": "google/gemma-3-12b",
                "timeout": 10  # Short timeout for quick test
            }
        }
        
        client = LMStudioClient(config)
        
        print("1. Testing availability...")
        if client.is_available():
            print("   ✅ LM Studio is available")
        else:
            print("   ❌ LM Studio not available")
            return 1
        
        print("2. Getting models...")
        models = client.get_available_models()
        print(f"   ✅ Found {len(models)} models: {models}")
        
        print("3. Testing connection...")
        connection_test = client.test_connection()
        print(f"   Status: {connection_test['status']}")
        print(f"   Message: {connection_test['message']}")
        
        if connection_test['status'] == 'success':
            print("   ✅ Connection test passed")
            details = connection_test.get('details', {})
            print(f"   Response time: {details.get('response_time', 'N/A')}s")
            print(f"   Tokens used: {details.get('tokens_used', 'N/A')}")
        else:
            print("   ❌ Connection test failed")
            for suggestion in connection_test.get('suggestions', []):
                print(f"   💡 {suggestion}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
