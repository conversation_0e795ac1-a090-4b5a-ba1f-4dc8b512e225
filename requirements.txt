# CYOA Automation System Requirements
# For users not using Pixi Manager

# Core dependencies
tweepy>=4.14.0
moviepy>=1.0.3
python-dotenv>=1.0.0
ffmpeg-python>=0.2.0
requests>=2.28.0

# GUI framework
PyQt5>=5.15.9

# Graph visualization
pygraphviz>=1.11
networkx>=3.1

# Machine learning and AI
torch>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Audio processing
pyttsx3>=2.90
librosa>=0.10.0
soundfile>=0.12.0

# Image and video processing
pillow>=10.0.0
opencv-python>=4.8.0
matplotlib>=3.7.0

# Development and testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Note: ComfyScript should be installed separately:
# pip install comfy-script

# Note: Ollama should be installed separately:
# curl -fsSL https://ollama.com/install.sh | sh
