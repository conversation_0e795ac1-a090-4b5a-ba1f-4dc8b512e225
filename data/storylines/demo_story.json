{"metadata": {"title": "Demo Adventure", "description": "A simple demo story", "created_at": "", "version": "1.0", "source": "demo_script"}, "config": {"story_generation": {"premium_nodes": [3, 4], "premium_entry_points": [1], "spicy_nodes": [3], "spicy_entry_points": [], "num_entry_points": 3, "min_endings": 5, "min_deaths": 3, "min_successes": 2, "max_nodes": 100, "side_mission_probability": 0.3, "max_choices_per_node": 5, "node_text_length": {"min": 50, "max": 100}}, "inventory_system": {"initial_inventory": {"gold": 100}, "max_inventory_types": 5, "gold_increment": 10, "common_items": ["sword", "potion", "key", "map", "food"]}, "class_system": {"classes": ["Mage", "<PERSON>", "Charmer"], "class_abilities": {"Mage": ["cast_spell", "enchant_item", "detect_magic"], "Ranger": ["track", "hunt", "survival"], "Charmer": ["persuade", "seduce", "negotiate"]}}, "rating_system": {"spicy_keywords": ["romance", "intimate", "seductive", "passionate"], "spicy_inheritance": true, "content_warnings": true}, "character_system": {"max_characters_per_story": 20, "auto_extract_characters": true, "track_character_consistency": true, "generate_character_dialogue": true, "default_importance_level": 3}, "media_generation": {"video_quality": "720p", "max_file_size_mb": 15, "video_duration_seconds": 10, "audio_sample_rate": 22050, "comfyui_timeout": 300}, "social_media": {"character_limit": 280, "post_delay_seconds": 10, "retry_attempts": 3, "retry_delay_minutes": 15}, "ollama": {"base_url": "http://localhost:11434", "model": "llama3.1", "timeout": 60, "max_tokens": 1000}, "comfyui": {"base_url": "http://127.0.0.1:8188", "timeout": 300, "workflows_dir": "workflows"}, "paths": {"videos_dir": "videos", "logs_dir": "logs", "data_dir": "data", "storylines_dir": "data/storylines", "workflows_dir": "workflows"}, "paywall": {"subscription_tiers": {"free": {"price": 0, "access_level": 0}, "premium": {"price": 4.99, "access_level": 1}, "spicy": {"price": 9.99, "access_level": 2}}, "trial_period_days": 7, "enable_content_warnings": true, "enable_super_follows": true}, "backup": {"auto_backup": true, "interval_minutes": 30, "max_backups": 50, "compress": true, "backup_dir": "backups"}, "wizard": {"show_on_startup": true, "default_player_class": "<PERSON>", "default_story_size": 25, "enable_templates": true}}, "entry_points": ["entry"], "endings": ["treasure_success", "dragon_death", "merchant_success"], "nodes": {"entry": {"id": "entry", "text": "You stand at the entrance of a mysterious cave. The air is thick with magic, and you can hear strange sounds echoing from within. Your adventure begins here.", "node_type": "entry", "choices": [{"id": "enter_cave", "text": "Enter the mysterious cave", "target_node_id": "cave_interior", "inventory_requirements": {}, "class_requirements": [], "inventory_changes": {}, "is_premium": false, "is_spicy": false}, {"id": "explore_forest", "text": "Explore the forest path instead", "target_node_id": "forest_path", "inventory_requirements": {}, "class_requirements": [], "inventory_changes": {}, "is_premium": false, "is_spicy": false}], "is_entry": true, "is_ending": false, "ending_type": null, "is_premium": false, "rating": "safe", "score": null, "inventory_state": {"gold": 100}, "class_context": "<PERSON>", "present_characters": [], "character_states": {}, "location": "", "metadata": {"rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}, "cave_interior": {"id": "cave_interior", "text": "Inside the cave, you discover ancient runes glowing on the walls. A treasure chest sits in the center, but you also notice a sleeping dragon nearby.", "node_type": "story", "choices": [{"id": "take_treasure", "text": "Carefully take the treasure", "target_node_id": "treasure_success", "inventory_requirements": {}, "class_requirements": ["<PERSON>"], "inventory_changes": {}, "is_premium": false, "is_spicy": false}, {"id": "wake_dragon", "text": "Boldly approach the dragon", "target_node_id": "dragon_death", "inventory_requirements": {}, "class_requirements": [], "inventory_changes": {}, "is_premium": false, "is_spicy": false}], "is_entry": false, "is_ending": false, "ending_type": null, "is_premium": false, "rating": "safe", "score": null, "inventory_state": {"gold": 100}, "class_context": null, "present_characters": [], "character_states": {}, "location": "", "metadata": {"rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}, "forest_path": {"id": "forest_path", "text": "You decide to explore the forest instead. The trees whisper secrets, and you find a hidden merchant willing to trade magical items.", "node_type": "story", "choices": [{"id": "trade_merchant", "text": "Trade with the merchant (50 gold)", "target_node_id": "merchant_success", "inventory_requirements": {"gold": 50}, "class_requirements": [], "inventory_changes": {"gold": -50}, "is_premium": false, "is_spicy": false}], "is_entry": false, "is_ending": false, "ending_type": null, "is_premium": false, "rating": "safe", "score": null, "inventory_state": {"gold": 100}, "class_context": null, "present_characters": [], "character_states": {}, "location": "", "metadata": {"rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}, "treasure_success": {"id": "treasure_success", "text": "You carefully take the treasure without waking the dragon. You emerge from the cave wealthy and victorious! The End.", "node_type": "ending", "choices": [], "is_entry": false, "is_ending": true, "ending_type": "success", "is_premium": false, "rating": "safe", "score": null, "inventory_state": {"gold": 1000, "treasure": 1}, "class_context": null, "present_characters": [], "character_states": {}, "location": "", "metadata": {"rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}, "dragon_death": {"id": "dragon_death", "text": "The dragon awakens and breathes fire! You perish in the flames. The End.", "node_type": "ending", "choices": [], "is_entry": false, "is_ending": true, "ending_type": "death", "is_premium": false, "rating": "safe", "score": 100.0, "inventory_state": {"gold": 0}, "class_context": null, "present_characters": [], "character_states": {}, "location": "", "metadata": {"score_details": {"death_path_length": 2, "success_path_length": 2, "death_entry_point": "entry", "furthest_success": "treasure_success"}, "rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}, "merchant_success": {"id": "merchant_success", "text": "You trade with the merchant and gain powerful magical items. Your forest adventure ends successfully! The End.", "node_type": "ending", "choices": [], "is_entry": false, "is_ending": true, "ending_type": "success", "is_premium": false, "rating": "safe", "score": null, "inventory_state": {"gold": 50, "magic_sword": 1, "potion": 2}, "class_context": null, "present_characters": [], "character_states": {}, "location": "", "metadata": {"rating_analysis": {"confidence": 1.0, "reasons": ["Explicitly marked as safe"], "keywords_found": []}}}}}