#!/usr/bin/env python3
"""
Demo Script for CYOA Automation System
Demonstrates basic functionality without requiring full setup
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from story.story_web import Story<PERSON><PERSON>, StoryNode, Choice, NodeType, EndingType
from story.inventory_system import InventoryManager
from story.class_system import ClassManager
from story.scoring_system import ScoringSystem
from story.rating_system import RatingSystem


def create_demo_story():
    """Create a simple demo story"""
    print("Creating demo story...")
    
    # Load config
    config_path = Path("config.json")
    if config_path.exists():
        with open(config_path) as f:
            config = json.load(f)
    else:
        config = {
            "story_generation": {"num_entry_points": 1, "min_endings": 2},
            "inventory_system": {"initial_inventory": {"gold": 100}},
            "class_system": {"classes": ["Ma<PERSON>", "<PERSON>", "Charmer"]}
        }
    
    # Create story web
    web = StoryWeb(config)
    web.metadata.update({
        "title": "Demo Adventure",
        "description": "A simple demo story",
        "source": "demo_script"
    })
    
    # Create entry node
    entry_node = StoryNode(
        id="entry",
        text="You stand at the entrance of a mysterious cave. The air is thick with magic, and you can hear strange sounds echoing from within. Your adventure begins here.",
        node_type=NodeType.ENTRY,
        is_entry=True,
        inventory_state={"gold": 100},
        class_context="Ranger"
    )
    web.add_node(entry_node)
    
    # Create choice nodes
    cave_node = StoryNode(
        id="cave_interior",
        text="Inside the cave, you discover ancient runes glowing on the walls. A treasure chest sits in the center, but you also notice a sleeping dragon nearby.",
        node_type=NodeType.STORY,
        inventory_state={"gold": 100}
    )
    web.add_node(cave_node)
    
    forest_node = StoryNode(
        id="forest_path",
        text="You decide to explore the forest instead. The trees whisper secrets, and you find a hidden merchant willing to trade magical items.",
        node_type=NodeType.STORY,
        inventory_state={"gold": 100}
    )
    web.add_node(forest_node)
    
    # Create ending nodes
    treasure_ending = StoryNode(
        id="treasure_success",
        text="You carefully take the treasure without waking the dragon. You emerge from the cave wealthy and victorious! The End.",
        node_type=NodeType.ENDING,
        is_ending=True,
        ending_type=EndingType.SUCCESS,
        inventory_state={"gold": 1000, "treasure": 1}
    )
    web.add_node(treasure_ending)
    
    dragon_death = StoryNode(
        id="dragon_death",
        text="The dragon awakens and breathes fire! You perish in the flames. The End.",
        node_type=NodeType.ENDING,
        is_ending=True,
        ending_type=EndingType.DEATH,
        inventory_state={"gold": 0}
    )
    web.add_node(dragon_death)
    
    merchant_ending = StoryNode(
        id="merchant_success",
        text="You trade with the merchant and gain powerful magical items. Your forest adventure ends successfully! The End.",
        node_type=NodeType.ENDING,
        is_ending=True,
        ending_type=EndingType.SUCCESS,
        inventory_state={"gold": 50, "magic_sword": 1, "potion": 2}
    )
    web.add_node(merchant_ending)
    
    # Add choices
    # From entry
    cave_choice = Choice(
        id="enter_cave",
        text="Enter the mysterious cave",
        target_node_id="cave_interior"
    )
    web.add_choice("entry", cave_choice)
    
    forest_choice = Choice(
        id="explore_forest",
        text="Explore the forest path instead",
        target_node_id="forest_path"
    )
    web.add_choice("entry", forest_choice)
    
    # From cave
    treasure_choice = Choice(
        id="take_treasure",
        text="Carefully take the treasure",
        target_node_id="treasure_success",
        class_requirements=["Ranger"]  # Rangers are stealthy
    )
    web.add_choice("cave_interior", treasure_choice)
    
    dragon_choice = Choice(
        id="wake_dragon",
        text="Boldly approach the dragon",
        target_node_id="dragon_death"
    )
    web.add_choice("cave_interior", dragon_choice)
    
    # From forest
    merchant_choice = Choice(
        id="trade_merchant",
        text="Trade with the merchant (50 gold)",
        target_node_id="merchant_success",
        inventory_requirements={"gold": 50},
        inventory_changes={"gold": -50}
    )
    web.add_choice("forest_path", merchant_choice)
    
    return web


def test_systems(web):
    """Test various systems with the demo story"""
    print("\nTesting systems...")
    
    # Load config
    config_path = Path("config.json")
    if config_path.exists():
        with open(config_path) as f:
            config = json.load(f)
    else:
        config = {
            "inventory_system": {"initial_inventory": {"gold": 100}},
            "class_system": {"classes": ["Mage", "Ranger", "Charmer"]},
            "rating_system": {"spicy_keywords": ["romance", "intimate"]}
        }
    
    # Test inventory system
    print("Testing inventory system...")
    inventory_manager = InventoryManager(config)
    initial_inventory = inventory_manager.create_initial_inventory()
    print(f"  Initial inventory: {inventory_manager.get_inventory_summary(initial_inventory)}")
    
    # Test class system
    print("Testing class system...")
    class_manager = ClassManager(config)
    available_classes = class_manager.get_available_classes()
    print(f"  Available classes: {', '.join(available_classes)}")
    
    ranger_abilities = class_manager.get_class_abilities("Ranger")
    print(f"  Ranger abilities: {[ability.name for ability in ranger_abilities]}")
    
    # Test scoring system
    print("Testing scoring system...")
    scoring_system = ScoringSystem(config)
    scoring_system.update_story_scores(web)
    
    score_summary = scoring_system.get_score_summary(web)
    print(f"  Death endings: {score_summary.get('death_endings', 0)}")
    print(f"  Success endings: {score_summary.get('success_endings', 0)}")
    
    # Test rating system
    print("Testing rating system...")
    rating_system = RatingSystem(config)
    rating_system.update_story_ratings(web)
    
    rating_summary = rating_system.get_rating_summary(web)
    print(f"  Safe nodes: {rating_summary.get('safe_nodes', 0)}")
    print(f"  Spicy nodes: {rating_summary.get('spicy_nodes', 0)}")
    
    # Test validation
    print("Testing validation...")
    is_valid, errors = web.validate_structure()
    if is_valid:
        print("  ✅ Story structure is valid")
    else:
        print("  ❌ Story validation errors:")
        for error in errors:
            print(f"    - {error}")


def main():
    """Main demo function"""
    print("🎮 CYOA Automation System Demo")
    print("=" * 40)
    
    # Create demo story
    web = create_demo_story()
    
    # Test systems
    test_systems(web)
    
    # Save demo story
    output_path = "data/storylines/demo_story.json"
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    
    if web.save_to_file(output_path):
        print(f"\n💾 Demo story saved to: {output_path}")
        
        print(f"\nStory Summary:")
        print(f"  Title: {web.metadata.get('title')}")
        print(f"  Nodes: {len(web.nodes)}")
        print(f"  Entry Points: {len(web.entry_points)}")
        print(f"  Endings: {len(web.endings)}")
        
        # Show story structure
        print(f"\nStory Structure:")
        for node_id, node in web.nodes.items():
            node_type = "📍" if node.is_entry else "🏁" if node.is_ending else "📄"
            print(f"  {node_type} {node_id}: {node.text[:50]}...")
            
            for choice in node.choices:
                print(f"    → {choice.text}")
    
    else:
        print("❌ Failed to save demo story")
        return 1
    
    print("\n🎉 Demo completed successfully!")
    print("\nNext steps:")
    print("1. Run the full application: python main.py")
    print("2. Import your own stories using the GUI")
    print("3. Generate videos and post to X")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
