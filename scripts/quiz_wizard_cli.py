#!/usr/bin/env python3
"""
CLI Quiz Creation Wizard - Beautiful command-line interface for quiz creation
Creates viral personality tests and quizzes
"""

import sys
import json
from pathlib import Path
from typing import Optional, List

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from quiz.quiz_generator import QuizGenerator
    from quiz.quiz_templates import QuizTemplateManager
    from quiz.quiz_system import QuizWeb, QuizType
    from utils.lmstudio_client import LMStudioClient
    from utils.system_health import SystemHealthChecker
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run quiz-wizard")
    sys.exit(1)

app = typer.Typer(help="🧠 Quiz Creation Wizard")
console = Console()


@app.command()
def create(
    title: Optional[str] = typer.Option(None, "--title", "-t", help="Quiz title"),
    quiz_type: Optional[str] = typer.Option("personality", "--type", help="Quiz type (personality/knowledge/compatibility)"),
    template: Optional[str] = typer.Option(None, "--template", help="Use quiz template"),
    quick: bool = typer.Option(False, "--quick", "-q", help="Quick mode with defaults"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path")
):
    """Create a new viral quiz"""
    
    # Welcome banner
    console.print(Panel.fit(
        "[bold magenta]🧠 Quiz Creation Wizard[/bold magenta]\n"
        "[dim]Create viral personality tests and engaging quizzes[/dim]",
        border_style="magenta"
    ))
    
    # System health check
    if not quick:
        console.print("\n[yellow]🔍 Checking system health...[/yellow]")
        health_ok = check_system_health()
        if not health_ok:
            console.print("[red]❌ System health issues detected. Run 'pixi run health' for details.[/red]")
            if not Confirm.ask("Continue anyway?"):
                raise typer.Exit(1)
    
    # Load configuration
    config = load_config()
    
    # Quiz creation flow
    if template:
        quiz = create_from_template(config, template, title)
    elif quick:
        quiz = quick_create(config, title, quiz_type)
    else:
        quiz = interactive_create(config, title, quiz_type)
    
    if quiz:
        # Save quiz
        output_path = save_quiz(quiz, output)
        console.print(f"\n[green]✅ Quiz created successfully![/green]")
        console.print(f"[dim]Saved to: {output_path}[/dim]")
        
        # Show quiz stats
        show_quiz_stats(quiz)
        
        # Next steps
        show_next_steps(output_path)
    else:
        console.print("[red]❌ Quiz creation failed[/red]")
        raise typer.Exit(1)


@app.command()
def templates():
    """List available quiz templates"""
    console.print(Panel.fit("[bold]🧠 Available Quiz Templates[/bold]", border_style="green"))
    
    config = load_config()
    template_manager = QuizTemplateManager(config)
    templates = template_manager.list_templates()
    
    if not templates:
        console.print("[yellow]No templates available[/yellow]")
        return
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("ID", style="cyan")
    table.add_column("Title", style="green")
    table.add_column("Type", style="blue")
    table.add_column("Questions", style="yellow")
    table.add_column("Outcomes", style="white")
    table.add_column("Viral Potential", style="red")
    
    for template in templates:
        table.add_row(
            template['id'],
            template['title'],
            template['quiz_type'],
            str(template['questions']),
            str(template['outcomes']),
            template['viral_potential']
        )
    
    console.print(table)
    console.print(f"\n[dim]Use: pixi run quiz-wizard create --template <ID>[/dim]")


@app.command()
def ideas(
    theme: str = typer.Argument(..., help="Theme for quiz ideas"),
    count: int = typer.Option(10, "--count", "-c", help="Number of ideas to generate")
):
    """Generate viral quiz ideas for a theme"""
    console.print(Panel.fit(f"[bold]💡 Quiz Ideas for '{theme}'[/bold]", border_style="yellow"))
    
    config = load_config()
    lmstudio_client = LMStudioClient(config)

    if not lmstudio_client.is_available():
        console.print("[red]❌ LM Studio not available. Please start LM Studio first.[/red]")
        console.print("[dim]Run: pixi run start-lmstudio[/dim]")
        return
    
    quiz_generator = QuizGenerator(config, lmstudio_client)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Generating quiz ideas...", total=None)
        ideas = quiz_generator.generate_viral_quiz_ideas(theme)
        progress.update(task, completed=True)
    
    if ideas:
        console.print(f"[green]✨ Generated {len(ideas)} viral quiz ideas:[/green]\n")
        for i, idea in enumerate(ideas, 1):
            console.print(f"[cyan]{i:2d}.[/cyan] {idea}")
        
        console.print(f"\n[dim]Create any of these with: pixi run quiz-wizard create --title \"<idea>\"[/dim]")
    else:
        console.print("[yellow]No ideas generated. Try a different theme.[/yellow]")


def interactive_create(config: dict, title: Optional[str], quiz_type: str):
    """Interactive quiz creation"""
    console.print("\n[bold cyan]🧠 Quiz Information[/bold cyan]")
    
    # Get quiz details
    if not title:
        title = Prompt.ask("Quiz title", default="My Awesome Quiz")
    
    description = Prompt.ask("Quiz description (optional)", default="")
    
    # Quiz type selection
    quiz_type = Prompt.ask(
        "Quiz type",
        choices=["personality", "knowledge", "compatibility", "assessment"],
        default=quiz_type or "personality"
    )
    
    # Source content
    console.print("\n[bold cyan]📝 Quiz Content[/bold cyan]")
    source_type = Prompt.ask(
        "How would you like to create your quiz?",
        choices=["prompt", "template", "manual"],
        default="prompt"
    )
    
    if source_type == "prompt":
        prompt = Prompt.ask("Enter quiz prompt/theme", 
                          default=f"Create a {quiz_type} quiz about {title}")
        
        # Generate quiz
        return generate_quiz_with_progress(config, prompt, title, quiz_type)
    
    elif source_type == "template":
        return create_from_template_interactive(config, title)
    
    else:  # manual
        console.print("[yellow]Manual quiz creation not yet implemented in CLI[/yellow]")
        console.print("[dim]Use the GUI for manual quiz creation: pixi run run[/dim]")
        return None


def quick_create(config: dict, title: Optional[str], quiz_type: str):
    """Quick quiz creation with defaults"""
    title = title or "Quick Quiz"
    prompt = f"Create a fun {quiz_type} quiz called '{title}'"
    
    console.print(f"[yellow]🚀 Quick creating {quiz_type} quiz: {title}[/yellow]")
    return generate_quiz_with_progress(config, prompt, title, quiz_type)


def create_from_template(config: dict, template_id: str, title: Optional[str]):
    """Create quiz from template"""
    template_manager = QuizTemplateManager(config)
    templates = template_manager.list_templates()
    
    # Find template
    template_info = None
    for t in templates:
        if t['id'] == template_id:
            template_info = t
            break
    
    if not template_info:
        console.print(f"[red]❌ Template '{template_id}' not found[/red]")
        console.print("Available templates:")
        for t in templates:
            console.print(f"  • {t['id']}: {t['title']}")
        return None
    
    title = title or f"{template_info['title']} (Custom)"
    
    console.print(f"[green]📚 Creating quiz from template: {template_info['title']}[/green]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Creating quiz from template...", total=None)
        quiz = template_manager.create_quiz_from_template(template_id, title)
        progress.update(task, completed=True)
    
    return quiz


def create_from_template_interactive(config: dict, title: str):
    """Interactive template selection"""
    template_manager = QuizTemplateManager(config)
    templates = template_manager.list_templates()
    
    if not templates:
        console.print("[yellow]No templates available, falling back to prompt input[/yellow]")
        return None
    
    console.print("\n[bold]Available Templates:[/bold]")
    for i, template in enumerate(templates):
        viral_indicator = "🔥" if template['viral_potential'] == 'very_high' else "⭐" if template['viral_potential'] == 'high' else ""
        console.print(f"  {i+1}. {template['title']} ({template['quiz_type']}) {viral_indicator}")
    
    choice = IntPrompt.ask("Select template", choices=[str(i+1) for i in range(len(templates))])
    template_id = templates[choice-1]['id']
    
    return create_from_template(config, template_id, title)


def generate_quiz_with_progress(config: dict, prompt: str, title: str, quiz_type: str):
    """Generate quiz with progress display"""
    console.print(f"\n[yellow]🧠 Generating {quiz_type} quiz: {title}[/yellow]")
    
    try:
        lmstudio_client = LMStudioClient(config)
        if not lmstudio_client.is_available():
            console.print("[red]❌ LM Studio not available. Please start LM Studio first.[/red]")
            console.print("[dim]Run: pixi run start-lmstudio[/dim]")
            return None
        
        quiz_generator = QuizGenerator(config, lmstudio_client)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Analyzing prompt...", total=None)
            
            # Simulate progress steps
            progress.update(task, description="Generating quiz structure...")
            time.sleep(1)
            
            progress.update(task, description="Creating questions and outcomes...")
            quiz = quiz_generator.generate_quiz_from_prompt(prompt, quiz_type)
            
            progress.update(task, description="Finalizing quiz...", completed=True)
        
        return quiz
        
    except Exception as e:
        console.print(f"[red]❌ Quiz generation failed: {e}[/red]")
        return None


def save_quiz(quiz, output_path: Optional[str]) -> str:
    """Save quiz to file"""
    if not output_path:
        # Generate filename from title
        safe_title = "".join(c for c in quiz.metadata.get('title', 'quiz') if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_').lower()
        output_path = f"data/quizzes/{safe_title}.json"
    
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(quiz.to_dict(), f, indent=2)
    
    return str(output_file)


def show_quiz_stats(quiz):
    """Display quiz statistics"""
    console.print("\n[bold cyan]📊 Quiz Statistics[/bold cyan]")
    
    stats = quiz.get_quiz_stats()
    
    table = Table(show_header=False, box=None)
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("📝 Quiz Type", stats['quiz_type'].title())
    table.add_row("❓ Questions", str(stats['total_questions']))
    table.add_row("🎯 Outcomes", str(stats['total_outcomes']))
    table.add_row("📄 Total Posts", str(stats['estimated_posts']))
    table.add_row("🔥 Viral Potential", stats['viral_potential'].title())
    
    console.print(table)


def show_next_steps(output_path: str):
    """Show next steps to user"""
    console.print("\n[bold green]🚀 Next Steps[/bold green]")
    
    steps = [
        "Edit your quiz: python main.py",
        "Generate media: pixi run media quiz",
        "Post to X: pixi run post quiz",
        "View in GUI: pixi run run",
        "Create more quizzes: pixi run quiz-wizard"
    ]
    
    for i, step in enumerate(steps, 1):
        console.print(f"  {i}. {step}")


def check_system_health() -> bool:
    """Quick system health check"""
    try:
        config = load_config()
        health_checker = SystemHealthChecker(config)
        checks = health_checker.run_all_checks()
        overall_status = health_checker.get_overall_status()
        
        if overall_status.value == "error":
            return False
        
        console.print("[green]✅ System health OK[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Health check failed: {e}[/red]")
        return False


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


if __name__ == "__main__":
    app()
