#!/usr/bin/env python3
"""
Backup Stories Script - Manual backup creation and management
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from utils.backup_manager import BackupManager
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

if RICH_AVAILABLE:
    app = typer.Typer(help="💾 Story Backup Manager")
    console = Console()
else:
    class SimpleApp:
        def command(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    app = SimpleApp()


@app.command()
def create(
    name: str = typer.Option(None, "--name", "-n", help="Backup name"),
    description: str = typer.Option("", "--description", "-d", help="Backup description")
):
    """Create a manual backup"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold blue]💾 Creating Backup[/bold blue]",
            border_style="blue"
        ))
    else:
        print("💾 Creating Backup")
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        
        # Generate name if not provided
        if not name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name = f"manual_backup_{timestamp}"
        
        if RICH_AVAILABLE:
            console.print(f"[yellow]Creating backup: {name}[/yellow]")
        else:
            print(f"Creating backup: {name}")
        
        backup_path = backup_manager.create_backup(name, description)
        
        if backup_path:
            if RICH_AVAILABLE:
                console.print(f"[green]✅ Backup created: {backup_path}[/green]")
            else:
                print(f"✅ Backup created: {backup_path}")
        else:
            if RICH_AVAILABLE:
                console.print("[red]❌ Backup creation failed[/red]")
            else:
                print("❌ Backup creation failed")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error creating backup: {e}[/red]")
        else:
            print(f"❌ Error creating backup: {e}")


@app.command()
def list():
    """List all available backups"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold green]📋 Available Backups[/bold green]",
            border_style="green"
        ))
    else:
        print("📋 Available Backups")
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        backups = backup_manager.list_backups()
        
        if not backups:
            if RICH_AVAILABLE:
                console.print("[yellow]No backups found[/yellow]")
            else:
                print("No backups found")
            return
        
        if RICH_AVAILABLE:
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Name", style="cyan")
            table.add_column("Type", style="blue")
            table.add_column("Date", style="green")
            table.add_column("Size", style="yellow")
            table.add_column("Description", style="white")
            
            for backup in backups:
                backup_date = datetime.fromisoformat(backup['timestamp']).strftime("%Y-%m-%d %H:%M")
                size_mb = backup.get('size', 0) / (1024 * 1024)
                
                table.add_row(
                    backup['name'],
                    backup.get('type', 'manual'),
                    backup_date,
                    f"{size_mb:.1f} MB",
                    backup.get('description', '')[:50] + ("..." if len(backup.get('description', '')) > 50 else "")
                )
            
            console.print(table)
        else:
            print(f"\nFound {len(backups)} backups:")
            for backup in backups:
                backup_date = datetime.fromisoformat(backup['timestamp']).strftime("%Y-%m-%d %H:%M")
                size_mb = backup.get('size', 0) / (1024 * 1024)
                print(f"  • {backup['name']} ({backup_date}) - {size_mb:.1f} MB")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error listing backups: {e}[/red]")
        else:
            print(f"❌ Error listing backups: {e}")


@app.command()
def restore(
    backup_name: str = typer.Argument(..., help="Name of backup to restore"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation prompt")
):
    """Restore from a backup"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold yellow]🔄 Restoring Backup[/bold yellow]\n"
            f"[dim]Backup: {backup_name}[/dim]",
            border_style="yellow"
        ))
    else:
        print(f"🔄 Restoring Backup: {backup_name}")
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        backups = backup_manager.list_backups()
        
        # Find backup
        backup_to_restore = None
        for backup in backups:
            if backup['name'] == backup_name:
                backup_to_restore = backup
                break
        
        if not backup_to_restore:
            if RICH_AVAILABLE:
                console.print(f"[red]❌ Backup not found: {backup_name}[/red]")
                console.print("\nAvailable backups:")
                for backup in backups[:5]:
                    console.print(f"  • {backup['name']}")
            else:
                print(f"❌ Backup not found: {backup_name}")
                print("\nAvailable backups:")
                for backup in backups[:5]:
                    print(f"  • {backup['name']}")
            return
        
        # Confirmation
        if not confirm:
            backup_date = datetime.fromisoformat(backup_to_restore['timestamp']).strftime("%Y-%m-%d %H:%M")
            
            if RICH_AVAILABLE:
                console.print(f"[yellow]⚠️  This will restore data from {backup_date}[/yellow]")
                console.print("[yellow]⚠️  Current data will be backed up first[/yellow]")
                
                if not Confirm.ask("Continue with restore?"):
                    console.print("[yellow]Restore cancelled[/yellow]")
                    return
            else:
                print(f"⚠️  This will restore data from {backup_date}")
                print("⚠️  Current data will be backed up first")
                response = input("Continue with restore? (y/n): ")
                if response.lower() != 'y':
                    print("Restore cancelled")
                    return
        
        # Perform restore
        if RICH_AVAILABLE:
            console.print("[blue]Restoring backup...[/blue]")
        else:
            print("Restoring backup...")
        
        success = backup_manager.restore_backup(backup_to_restore['path'])
        
        if success:
            if RICH_AVAILABLE:
                console.print("[green]✅ Backup restored successfully[/green]")
            else:
                print("✅ Backup restored successfully")
        else:
            if RICH_AVAILABLE:
                console.print("[red]❌ Backup restore failed[/red]")
            else:
                print("❌ Backup restore failed")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error restoring backup: {e}[/red]")
        else:
            print(f"❌ Error restoring backup: {e}")


@app.command()
def delete(
    backup_name: str = typer.Argument(..., help="Name of backup to delete"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation prompt")
):
    """Delete a backup"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold red]🗑️  Deleting Backup[/bold red]\n"
            f"[dim]Backup: {backup_name}[/dim]",
            border_style="red"
        ))
    else:
        print(f"🗑️  Deleting Backup: {backup_name}")
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        backups = backup_manager.list_backups()
        
        # Find backup
        backup_to_delete = None
        for backup in backups:
            if backup['name'] == backup_name:
                backup_to_delete = backup
                break
        
        if not backup_to_delete:
            if RICH_AVAILABLE:
                console.print(f"[red]❌ Backup not found: {backup_name}[/red]")
            else:
                print(f"❌ Backup not found: {backup_name}")
            return
        
        # Confirmation
        if not confirm:
            if RICH_AVAILABLE:
                console.print("[red]⚠️  This action cannot be undone[/red]")
                
                if not Confirm.ask("Delete backup?"):
                    console.print("[yellow]Delete cancelled[/yellow]")
                    return
            else:
                print("⚠️  This action cannot be undone")
                response = input("Delete backup? (y/n): ")
                if response.lower() != 'y':
                    print("Delete cancelled")
                    return
        
        # Perform delete
        success = backup_manager.delete_backup(backup_to_delete['path'])
        
        if success:
            if RICH_AVAILABLE:
                console.print("[green]✅ Backup deleted successfully[/green]")
            else:
                print("✅ Backup deleted successfully")
        else:
            if RICH_AVAILABLE:
                console.print("[red]❌ Backup delete failed[/red]")
            else:
                print("❌ Backup delete failed")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error deleting backup: {e}[/red]")
        else:
            print(f"❌ Error deleting backup: {e}")


@app.command()
def stats():
    """Show backup statistics"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold cyan]📊 Backup Statistics[/bold cyan]",
            border_style="cyan"
        ))
    else:
        print("📊 Backup Statistics")
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        stats = backup_manager.get_backup_stats()
        
        if RICH_AVAILABLE:
            table = Table(show_header=False, box=None)
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Backups", str(stats['total_backups']))
            table.add_row("Auto Backups", str(stats['auto_backups']))
            table.add_row("Manual Backups", str(stats['manual_backups']))
            table.add_row("Total Size", f"{stats['total_size_mb']} MB")
            table.add_row("Auto Backup", "Enabled" if stats['auto_backup_enabled'] else "Disabled")
            table.add_row("Backup Interval", f"{stats['backup_interval_minutes']} minutes")
            
            if stats['latest_backup']:
                latest_date = datetime.fromisoformat(stats['latest_backup']['timestamp']).strftime("%Y-%m-%d %H:%M")
                table.add_row("Latest Backup", latest_date)
            
            console.print(table)
        else:
            print(f"Total Backups: {stats['total_backups']}")
            print(f"Auto Backups: {stats['auto_backups']}")
            print(f"Manual Backups: {stats['manual_backups']}")
            print(f"Total Size: {stats['total_size_mb']} MB")
            print(f"Auto Backup: {'Enabled' if stats['auto_backup_enabled'] else 'Disabled'}")
            print(f"Backup Interval: {stats['backup_interval_minutes']} minutes")
            
            if stats['latest_backup']:
                latest_date = datetime.fromisoformat(stats['latest_backup']['timestamp']).strftime("%Y-%m-%d %H:%M")
                print(f"Latest Backup: {latest_date}")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error getting backup stats: {e}[/red]")
        else:
            print(f"❌ Error getting backup stats: {e}")


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main function for non-typer usage"""
    if len(sys.argv) < 2:
        print("Usage: python backup_stories.py <command>")
        print("Commands: create, list, restore, delete, stats")
        print("   or: pixi run backup <command>")
        sys.exit(1)
    
    command = sys.argv[1]
    if command == "create":
        create()
    elif command == "list":
        list()
    elif command == "stats":
        stats()
    else:
        print(f"Unknown command: {command}")


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
