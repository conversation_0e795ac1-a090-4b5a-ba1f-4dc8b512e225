#!/usr/bin/env python3
"""
Story Validation Script - Validate story structure and content
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from story.story_web import <PERSON>Web
    from story.character_system import CharacterManager
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

if RICH_AVAILABLE:
    app = typer.Typer(help="📋 Story Validation Tool")
    console = Console()
else:
    class SimpleApp:
        def command(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    app = SimpleApp()


@app.command()
def validate(
    story_path: str = typer.Argument(..., help="Path to story file"),
    detailed: bool = typer.Option(False, "--detailed", "-d", help="Show detailed validation"),
    fix: bool = typer.Option(False, "--fix", help="Attempt to fix issues automatically")
):
    """Validate a story file"""
    
    story_file = Path(story_path)
    if not story_file.exists():
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Story file not found: {story_path}[/red]")
        else:
            print(f"❌ Story file not found: {story_path}")
        return
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold blue]📋 Validating Story[/bold blue]\n"
            f"[dim]File: {story_path}[/dim]",
            border_style="blue"
        ))
    else:
        print(f"📋 Validating Story: {story_path}")
    
    # Load story
    try:
        story = StoryWeb.load_from_file(story_path)
        if not story:
            if RICH_AVAILABLE:
                console.print("[red]❌ Failed to load story[/red]")
            else:
                print("❌ Failed to load story")
            return
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error loading story: {e}[/red]")
        else:
            print(f"❌ Error loading story: {e}")
        return
    
    # Run validation
    is_valid, errors = story.validate_structure()
    
    # Character consistency check
    char_consistent, char_errors = story.validate_character_consistency()
    
    # Display results
    if RICH_AVAILABLE:
        display_rich_results(story, is_valid, errors, char_consistent, char_errors, detailed)
    else:
        display_simple_results(story, is_valid, errors, char_consistent, char_errors, detailed)
    
    # Attempt fixes if requested
    if fix and (not is_valid or not char_consistent):
        attempt_fixes(story, errors + char_errors, story_path)


def display_rich_results(story, is_valid, errors, char_consistent, char_errors, detailed):
    """Display validation results with Rich formatting"""
    
    # Story stats
    table = Table(title="Story Statistics", show_header=False)
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Total Nodes", str(len(story.nodes)))
    table.add_row("Entry Points", str(len(story.entry_points)))
    table.add_row("Endings", str(len(story.endings)))
    
    if story.character_manager:
        table.add_row("Characters", str(len(story.character_manager.characters)))
    
    console.print(table)
    
    # Validation results
    console.print("\n[bold]Validation Results:[/bold]")
    
    if is_valid:
        console.print("[green]✅ Story structure is valid[/green]")
    else:
        console.print(f"[red]❌ Story structure has {len(errors)} issues[/red]")
        for error in errors:
            console.print(f"  [red]•[/red] {error}")
    
    if char_consistent:
        console.print("[green]✅ Character consistency is valid[/green]")
    else:
        console.print(f"[red]❌ Character consistency has {len(char_errors)} issues[/red]")
        for error in char_errors:
            console.print(f"  [red]•[/red] {error}")
    
    # Overall status
    if is_valid and char_consistent:
        console.print("\n[bold green]🎉 Story is ready for use![/bold green]")
    else:
        console.print("\n[bold yellow]⚠️  Story needs attention before use[/bold yellow]")


def display_simple_results(story, is_valid, errors, char_consistent, char_errors, detailed):
    """Display validation results in simple text format"""
    
    print("\nStory Statistics:")
    print(f"  Total Nodes: {len(story.nodes)}")
    print(f"  Entry Points: {len(story.entry_points)}")
    print(f"  Endings: {len(story.endings)}")
    
    if story.character_manager:
        print(f"  Characters: {len(story.character_manager.characters)}")
    
    print("\nValidation Results:")
    
    if is_valid:
        print("✅ Story structure is valid")
    else:
        print(f"❌ Story structure has {len(errors)} issues:")
        for error in errors:
            print(f"  • {error}")
    
    if char_consistent:
        print("✅ Character consistency is valid")
    else:
        print(f"❌ Character consistency has {len(char_errors)} issues:")
        for error in char_errors:
            print(f"  • {error}")
    
    if is_valid and char_consistent:
        print("\n🎉 Story is ready for use!")
    else:
        print("\n⚠️  Story needs attention before use")


def attempt_fixes(story, all_errors, story_path):
    """Attempt to fix common issues"""
    
    if RICH_AVAILABLE:
        console.print("\n[yellow]🔧 Attempting to fix issues...[/yellow]")
    else:
        print("\n🔧 Attempting to fix issues...")
    
    fixes_applied = 0
    
    # Fix missing entry points
    if not story.entry_points and story.nodes:
        # Find nodes that could be entry points
        potential_entries = [node for node in story.nodes.values() if node.is_entry or not any(
            choice.target_node_id == node.id for other_node in story.nodes.values() 
            for choice in other_node.choices
        )]
        
        if potential_entries:
            story.entry_points = [potential_entries[0].id]
            fixes_applied += 1
            if RICH_AVAILABLE:
                console.print("[green]✅ Fixed: Added entry point[/green]")
            else:
                print("✅ Fixed: Added entry point")
    
    # Fix missing endings
    if not story.endings and story.nodes:
        # Find nodes that could be endings
        potential_endings = [node for node in story.nodes.values() if node.is_ending or not node.choices]
        
        if potential_endings:
            story.endings = [node.id for node in potential_endings[:3]]
            fixes_applied += 1
            if RICH_AVAILABLE:
                console.print("[green]✅ Fixed: Added endings[/green]")
            else:
                print("✅ Fixed: Added endings")
    
    # Save fixed story
    if fixes_applied > 0:
        try:
            story.save_to_file(story_path)
            if RICH_AVAILABLE:
                console.print(f"[green]✅ Applied {fixes_applied} fixes and saved story[/green]")
            else:
                print(f"✅ Applied {fixes_applied} fixes and saved story")
        except Exception as e:
            if RICH_AVAILABLE:
                console.print(f"[red]❌ Failed to save fixed story: {e}[/red]")
            else:
                print(f"❌ Failed to save fixed story: {e}")
    else:
        if RICH_AVAILABLE:
            console.print("[yellow]No automatic fixes available[/yellow]")
        else:
            print("No automatic fixes available")


@app.command()
def batch_validate(
    directory: str = typer.Argument("data/storylines", help="Directory containing story files"),
    pattern: str = typer.Option("*.json", "--pattern", "-p", help="File pattern to match")
):
    """Validate all stories in a directory"""
    
    story_dir = Path(directory)
    if not story_dir.exists():
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Directory not found: {directory}[/red]")
        else:
            print(f"❌ Directory not found: {directory}")
        return
    
    story_files = list(story_dir.glob(pattern))
    
    if not story_files:
        if RICH_AVAILABLE:
            console.print(f"[yellow]No story files found in {directory}[/yellow]")
        else:
            print(f"No story files found in {directory}")
        return
    
    if RICH_AVAILABLE:
        console.print(f"[blue]📋 Validating {len(story_files)} stories...[/blue]")
    else:
        print(f"📋 Validating {len(story_files)} stories...")
    
    results = []
    
    for story_file in story_files:
        try:
            story = StoryWeb.load_from_file(str(story_file))
            if story:
                is_valid, errors = story.validate_structure()
                char_consistent, char_errors = story.validate_character_consistency()
                
                results.append({
                    'file': story_file.name,
                    'valid': is_valid and char_consistent,
                    'errors': len(errors) + len(char_errors)
                })
            else:
                results.append({
                    'file': story_file.name,
                    'valid': False,
                    'errors': 1
                })
        except Exception as e:
            results.append({
                'file': story_file.name,
                'valid': False,
                'errors': 1
            })
    
    # Display batch results
    if RICH_AVAILABLE:
        table = Table(title="Batch Validation Results")
        table.add_column("File", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Issues", style="yellow")
        
        for result in results:
            status = "[green]✅ Valid[/green]" if result['valid'] else "[red]❌ Invalid[/red]"
            table.add_row(result['file'], status, str(result['errors']))
        
        console.print(table)
        
        valid_count = sum(1 for r in results if r['valid'])
        console.print(f"\n[bold]Summary: {valid_count}/{len(results)} stories are valid[/bold]")
    else:
        print("\nBatch Validation Results:")
        for result in results:
            status = "✅ Valid" if result['valid'] else "❌ Invalid"
            print(f"  {result['file']}: {status} ({result['errors']} issues)")
        
        valid_count = sum(1 for r in results if r['valid'])
        print(f"\nSummary: {valid_count}/{len(results)} stories are valid")


def main():
    """Main function for non-typer usage"""
    if len(sys.argv) < 2:
        print("Usage: python validate_story.py <story_file>")
        print("   or: pixi run validate <story_file>")
        sys.exit(1)
    
    story_path = sys.argv[1]
    validate(story_path, detailed=False, fix=False)


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
