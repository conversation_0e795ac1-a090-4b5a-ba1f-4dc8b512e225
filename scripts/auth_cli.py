#!/usr/bin/env python3
"""
Authentication CLI - X/Twitter authentication and subscription management
Beautiful command-line interface for OAuth and rate limiting
"""

import sys
import json
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.progress import Progress, SpinnerColumn, TextColumn
import webbrowser

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from social.x_auth import XAuthenticator, XSubscriptionTier
    from social.rate_limiter import PostingSchedule
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run auth")
    sys.exit(1)

app = typer.Typer(help="🔐 X Authentication & Subscription Management")
console = Console()


@app.command()
def login():
    """Login to X (Twitter) using OAuth 2.0"""
    
    console.print(Panel.fit(
        "[bold cyan]🔐 X Authentication[/bold cyan]\n"
        "[dim]Login to your X (Twitter) account[/dim]",
        border_style="cyan"
    ))
    
    # Load config
    config = load_config()
    authenticator = XAuthenticator(config)
    
    # Check if already authenticated
    if authenticator.is_authenticated():
        user_info = authenticator.user_info
        username = user_info.get('username', 'Unknown') if user_info else 'Unknown'
        
        console.print(f"[green]✅ Already authenticated as @{username}[/green]")
        
        if not Confirm.ask("Do you want to login with a different account?"):
            return
        
        authenticator.logout()
    
    # Check API configuration
    if not authenticator.client_id or not authenticator.client_secret:
        console.print("[red]❌ X API credentials not configured[/red]")
        console.print("\nPlease configure your X API credentials first:")
        console.print("1. Go to https://developer.twitter.com/")
        console.print("2. Create an app with OAuth 2.0")
        console.print("3. Run: pixi run auth setup")
        return
    
    try:
        # Generate auth URL
        console.print("\n[yellow]🔗 Generating authentication URL...[/yellow]")
        auth_url, state = authenticator.get_auth_url()
        
        if not auth_url:
            console.print("[red]❌ Failed to generate authentication URL[/red]")
            return
        
        # Open browser
        console.print(f"\n[green]🌐 Opening browser for authentication...[/green]")
        console.print(f"[dim]URL: {auth_url}[/dim]")
        
        webbrowser.open(auth_url)
        
        # Get authorization code
        console.print("\n[bold]📋 Complete the following steps:[/bold]")
        console.print("1. Authorize the application in your browser")
        console.print("2. Copy the authorization code from the callback URL")
        console.print("3. Paste it below")
        
        auth_code = Prompt.ask("\n[cyan]Authorization code[/cyan]")
        
        if not auth_code:
            console.print("[red]❌ No authorization code provided[/red]")
            return
        
        # Complete authentication
        console.print("\n[yellow]🔄 Completing authentication...[/yellow]")
        
        success = authenticator.authenticate_with_code(auth_code, state)
        
        if success:
            user_info = authenticator.user_info
            username = user_info.get('username', 'Unknown') if user_info else 'Unknown'
            
            console.print(f"\n[green]✅ Successfully authenticated as @{username}![/green]")
            
            # Show subscription info
            show_subscription_info(authenticator)
            
        else:
            console.print("\n[red]❌ Authentication failed[/red]")
            console.print("Please check your authorization code and try again")
            
    except Exception as e:
        console.print(f"\n[red]❌ Authentication error: {e}[/red]")


@app.command()
def status():
    """Show current authentication and subscription status"""
    
    console.print(Panel.fit(
        "[bold magenta]📊 Authentication Status[/bold magenta]\n"
        "[dim]Current X account and subscription information[/dim]",
        border_style="magenta"
    ))
    
    config = load_config()
    authenticator = XAuthenticator(config)
    
    if not authenticator.is_authenticated():
        console.print("[red]❌ Not authenticated[/red]")
        console.print("\nRun: [cyan]pixi run auth login[/cyan] to authenticate")
        return
    
    # Show user info
    user_info = authenticator.user_info
    if user_info:
        console.print("\n[bold green]👤 User Information[/bold green]")
        
        table = Table(show_header=False, box=None)
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="white")
        
        table.add_row("Username", f"@{user_info.get('username', 'Unknown')}")
        table.add_row("User ID", user_info.get('id', 'Unknown'))
        table.add_row("Display Name", user_info.get('name', 'Unknown'))
        table.add_row("Verified", "Yes" if user_info.get('verified') else "No")
        
        console.print(table)
    
    # Show subscription info
    show_subscription_info(authenticator)
    
    # Show rate limits
    show_rate_limits(authenticator)


@app.command()
def subscription(
    tier: Optional[str] = typer.Option(None, "--tier", help="Set subscription tier manually"),
    auto_detect: bool = typer.Option(True, "--auto-detect", help="Auto-detect tier from account")
):
    """Manage subscription tier settings"""
    
    console.print(Panel.fit(
        "[bold yellow]📊 Subscription Management[/bold yellow]\n"
        "[dim]Configure your X subscription tier for rate limiting[/dim]",
        border_style="yellow"
    ))
    
    config = load_config()
    authenticator = XAuthenticator(config)
    
    if tier:
        # Set tier manually
        tiers = XSubscriptionTier.get_all_tiers()
        if tier not in tiers:
            console.print(f"[red]❌ Invalid tier: {tier}[/red]")
            console.print(f"Available tiers: {list(tiers.keys())}")
            return
        
        authenticator.set_subscription_tier(tier)
        console.print(f"[green]✅ Subscription tier set to: {tier}[/green]")
        
    elif auto_detect and authenticator.is_authenticated():
        # Auto-detect tier
        console.print("[yellow]🔍 Auto-detecting subscription tier...[/yellow]")
        authenticator._detect_subscription_tier()
        console.print(f"[green]✅ Detected tier: {authenticator.subscription_tier}[/green]")
        
    else:
        # Interactive tier selection
        tiers = XSubscriptionTier.get_all_tiers()
        
        console.print("\n[bold]Available Subscription Tiers:[/bold]")
        
        table = Table()
        table.add_column("Tier", style="cyan")
        table.add_column("Cost", style="green")
        table.add_column("Posts/Day", style="yellow")
        table.add_column("Posts/Hour", style="blue")
        table.add_column("Video Length", style="magenta")
        
        for tier_id, tier_data in tiers.items():
            table.add_row(
                tier_data['name'],
                f"${tier_data['monthly_cost']}/month",
                str(tier_data['posts_per_day']),
                str(tier_data['posts_per_hour']),
                f"{tier_data['video_length_max']}s"
            )
        
        console.print(table)
        
        # Get user selection
        tier_names = [tier_data['name'] for tier_data in tiers.values()]
        selected_name = Prompt.ask(
            "\nSelect your subscription tier",
            choices=tier_names,
            default="Free"
        )
        
        # Find tier ID
        selected_tier = None
        for tier_id, tier_data in tiers.items():
            if tier_data['name'] == selected_name:
                selected_tier = tier_id
                break
        
        if selected_tier:
            authenticator.set_subscription_tier(selected_tier)
            console.print(f"[green]✅ Subscription tier set to: {selected_tier}[/green]")
    
    # Show updated subscription info
    show_subscription_info(authenticator)


@app.command()
def setup():
    """Setup X API credentials"""
    
    console.print(Panel.fit(
        "[bold red]🔧 X API Setup[/bold red]\n"
        "[dim]Configure your X API credentials for authentication[/dim]",
        border_style="red"
    ))
    
    console.print("\n[bold]📋 Setup Instructions:[/bold]")
    console.print("1. Go to https://developer.twitter.com/")
    console.print("2. Create a new app or use existing app")
    console.print("3. Enable OAuth 2.0 with PKCE")
    console.print("4. Set callback URL to: http://localhost:8080/callback")
    console.print("5. Get your Client ID and Client Secret")
    
    if not Confirm.ask("\nDo you have your X API credentials ready?"):
        console.print("\nPlease complete the setup at https://developer.twitter.com/ first")
        return
    
    # Get credentials
    client_id = Prompt.ask("\n[cyan]Client ID[/cyan]")
    client_secret = Prompt.ask("[cyan]Client Secret[/cyan]", password=True)
    
    if not client_id or not client_secret:
        console.print("[red]❌ Both Client ID and Client Secret are required[/red]")
        return
    
    # Save to config
    config = load_config()
    if 'x_auth' not in config:
        config['x_auth'] = {}
    
    config['x_auth']['client_id'] = client_id
    config['x_auth']['client_secret'] = client_secret
    config['x_auth']['redirect_uri'] = 'http://localhost:8080/callback'
    
    save_config(config)
    
    console.print("\n[green]✅ X API credentials saved successfully![/green]")
    console.print("\nYou can now run: [cyan]pixi run auth login[/cyan]")


@app.command()
def logout():
    """Logout from X"""
    
    config = load_config()
    authenticator = XAuthenticator(config)
    
    if not authenticator.is_authenticated():
        console.print("[yellow]⚠️  Not currently authenticated[/yellow]")
        return
    
    user_info = authenticator.user_info
    username = user_info.get('username', 'Unknown') if user_info else 'Unknown'
    
    if Confirm.ask(f"Logout from @{username}?"):
        authenticator.logout()
        console.print("[green]✅ Successfully logged out[/green]")
    else:
        console.print("[yellow]Logout cancelled[/yellow]")


@app.command()
def estimate(
    nodes: int = typer.Argument(..., help="Number of story nodes to post"),
    tier: Optional[str] = typer.Option(None, "--tier", help="Subscription tier to use for estimate")
):
    """Estimate story posting time based on rate limits"""
    
    console.print(Panel.fit(
        f"[bold green]⏱️  Posting Time Estimate[/bold green]\n"
        f"[dim]Estimate for posting {nodes} story nodes[/dim]",
        border_style="green"
    ))
    
    config = load_config()
    authenticator = XAuthenticator(config)
    
    # Use specified tier or current tier
    if tier:
        authenticator.set_subscription_tier(tier)
    
    # Get rate limits
    rate_limits = authenticator.get_rate_limits()
    rate_limiter = PostingSchedule(authenticator.subscription_tier, rate_limits)
    
    # Calculate estimate
    estimate = rate_limiter.estimate_story_posting_time(nodes)
    
    # Show results
    console.print(f"\n[bold]📊 Posting Estimate for {nodes} nodes:[/bold]")
    
    table = Table(show_header=False, box=None)
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="white")
    
    if estimate['total_time_seconds'] > 0:
        hours = estimate['total_time_hours']
        days = estimate['total_time_days']
        
        table.add_row("Subscription Tier", authenticator.subscription_tier.title())
        table.add_row("Total Time", f"{hours:.1f} hours ({days:.1f} days)")
        table.add_row("Posts per Day", f"{estimate['posts_per_day']:.1f}")
        table.add_row("Start Time", estimate['start_time'])
        table.add_row("End Time", estimate['end_time'])
        
        console.print(table)
        
        # Show recommendations
        if days > 7:
            console.print("\n[yellow]💡 Recommendation: Consider upgrading subscription for faster posting[/yellow]")
        elif days < 1:
            console.print("\n[green]✅ Story can be posted within 1 day[/green]")
        
    else:
        console.print("[red]❌ Unable to calculate estimate[/red]")


def show_subscription_info(authenticator: XAuthenticator):
    """Show subscription information"""
    sub_info = authenticator.get_subscription_info()
    tier_data = sub_info['tier_data']
    
    console.print("\n[bold blue]📊 Subscription Information[/bold blue]")
    
    table = Table(show_header=False, box=None)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="white")
    
    table.add_row("Tier", tier_data['name'])
    table.add_row("Monthly Cost", f"${tier_data['monthly_cost']}")
    table.add_row("Posts per Day", str(tier_data['posts_per_day']))
    table.add_row("Posts per Hour", str(tier_data['posts_per_hour']))
    table.add_row("Max Video Length", f"{tier_data['video_length_max']} seconds")
    table.add_row("API Rate Limit", f"{tier_data['api_rate_limit']} requests/15min")
    
    console.print(table)


def show_rate_limits(authenticator: XAuthenticator):
    """Show current rate limit usage"""
    rate_limits = authenticator.get_rate_limits()
    rate_limiter = PostingSchedule(authenticator.subscription_tier, rate_limits)
    stats = rate_limiter.get_posting_stats()
    
    console.print("\n[bold red]⏱️  Rate Limit Usage[/bold red]")
    
    table = Table()
    table.add_column("Period", style="cyan")
    table.add_column("Usage", style="yellow")
    table.add_column("Limit", style="green")
    table.add_column("Percentage", style="blue")
    
    # Daily usage
    daily_usage = stats['current_usage']['posts_today']
    daily_limit = stats['rate_limits']['posts_per_day']
    daily_percent = stats['usage_percentages']['daily']
    
    table.add_row(
        "Today",
        str(daily_usage),
        str(daily_limit),
        f"{daily_percent:.1f}%"
    )
    
    # Hourly usage
    hourly_usage = stats['current_usage']['posts_this_hour']
    hourly_limit = stats['rate_limits']['posts_per_hour']
    hourly_percent = stats['usage_percentages']['hourly']
    
    table.add_row(
        "This Hour",
        str(hourly_usage),
        str(hourly_limit),
        f"{hourly_percent:.1f}%"
    )
    
    console.print(table)
    
    # Show status
    can_post = stats['can_post_now']
    status_text = "[green]✅ Can post now[/green]" if can_post else "[red]❌ Rate limited[/red]"
    console.print(f"\nStatus: {status_text}")


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def save_config(config: dict):
    """Save configuration"""
    config_path = Path("config.json")
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        console.print(f"[red]Error saving config: {e}[/red]")


if __name__ == "__main__":
    app()
