#!/usr/bin/env python3
"""
Complete System Test
Test all components including wizard, health checker, templates, and backup system
"""

import sys
import json
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from utils.system_health import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from utils.backup_manager import BackupManager
    from story.story_templates import StoryTemplateManager
    from story.story_generator import StoryGenerator
    from utils.lmstudio_client import LMStudioClient
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)


def test_system_health():
    """Test system health checker"""
    print("=" * 50)
    print("TESTING SYSTEM HEALTH CHECKER")
    print("=" * 50)
    
    try:
        config = load_config()
        health_checker = SystemHealthChecker(config)
        
        print("Running comprehensive health check...")
        checks = health_checker.run_all_checks()
        
        print(f"✅ Health check completed: {len(checks)} components checked")
        
        # Display results
        for name, check in checks.items():
            status_icon = "✅" if check.status.value == 'healthy' else "⚠️" if check.status.value == 'warning' else "❌"
            print(f"  {status_icon} {name}: {check.message}")
        
        # Overall status
        overall_status = health_checker.get_overall_status()
        print(f"\n🎯 Overall System Status: {overall_status.value.upper()}")
        
        # Get summary
        summary = health_checker.get_health_summary()
        if summary['recommendations']:
            print("\n📋 Recommendations:")
            for rec in summary['recommendations'][:3]:
                print(f"  • {rec}")
        
        return overall_status.value != 'error'
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


def test_backup_system():
    """Test backup manager"""
    print("\n" + "=" * 50)
    print("TESTING BACKUP SYSTEM")
    print("=" * 50)
    
    try:
        config = load_config()
        backup_manager = BackupManager(config)
        
        print("✅ Backup manager initialized")
        
        # Test manual backup
        print("Creating manual backup...")
        backup_path = backup_manager.create_backup("test_backup", "Test backup from system test")
        
        if backup_path:
            print(f"✅ Manual backup created: {backup_path}")
        else:
            print("⚠️  Manual backup failed")
        
        # List backups
        backups = backup_manager.list_backups()
        print(f"✅ Found {len(backups)} backups")
        
        # Get backup stats
        stats = backup_manager.get_backup_stats()
        print(f"✅ Backup stats: {stats['total_backups']} total, {stats['total_size_mb']} MB")
        
        # Test auto-backup (without actually waiting)
        if backup_manager.auto_backup_enabled:
            print("✅ Auto-backup is enabled")
        else:
            print("⚠️  Auto-backup is disabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Backup system test failed: {e}")
        return False


def test_story_templates():
    """Test story template system"""
    print("\n" + "=" * 50)
    print("TESTING STORY TEMPLATES")
    print("=" * 50)
    
    try:
        config = load_config()
        template_manager = StoryTemplateManager(config)
        
        print("✅ Template manager initialized")
        
        # List available templates
        templates = template_manager.list_templates()
        print(f"✅ Found {len(templates)} templates:")
        
        for template in templates:
            print(f"  • {template['name']} ({template['genre']}) - {template['difficulty']}")
        
        # Test creating story from template
        if templates:
            template_id = templates[0]['id']
            print(f"\nTesting story creation from template: {template_id}")
            
            story = template_manager.create_story_from_template(
                template_id, "Test Template Story", "Mage"
            )
            
            if story:
                print(f"✅ Story created from template: {len(story.nodes)} nodes")
                
                # Check character manager
                char_manager = story.get_character_manager()
                if char_manager:
                    print(f"✅ Characters: {len(char_manager.characters)} characters")
                
                # Validate story
                is_valid, errors = story.validate_structure()
                if is_valid:
                    print("✅ Template story structure is valid")
                else:
                    print(f"⚠️  Template story has {len(errors)} validation issues")
                
            else:
                print("❌ Failed to create story from template")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Template system test failed: {e}")
        return False


def test_wizard_components():
    """Test wizard-related components"""
    print("\n" + "=" * 50)
    print("TESTING WIZARD COMPONENTS")
    print("=" * 50)
    
    try:
        config = load_config()
        
        # Test story generator with wizard-like parameters
        lmstudio_client = LMStudioClient(config)
        if not lmstudio_client.is_available():
            print("⚠️  LM Studio not available - skipping AI-dependent tests")
            return True

        story_generator = StoryGenerator(config, lmstudio_client)
        print("✅ Story generator initialized")
        
        # Test with sample wizard data
        sample_text = """
        Once upon a time, there was a brave knight named Sir Galahad who lived in Camelot.
        One day, an evil dragon threatened the kingdom, and Sir Galahad had to decide
        whether to face the dragon alone or seek help from other knights.
        """
        
        print("Testing story generation with sample text...")
        story = story_generator.import_storyline_from_text(
            sample_text, "Wizard Test Story", "Ranger"
        )
        
        if story:
            print(f"✅ Story generated: {len(story.nodes)} nodes")
            
            # Check character extraction
            char_manager = story.get_character_manager()
            if char_manager and len(char_manager.characters) > 0:
                print(f"✅ Characters extracted: {len(char_manager.characters)} characters")
                
                # List characters
                for character in char_manager.characters.values():
                    print(f"  • {character.name} ({character.role.value})")
            else:
                print("⚠️  No characters extracted")
            
            return True
        else:
            print("❌ Story generation failed")
            return False
        
    except Exception as e:
        print(f"❌ Wizard components test failed: {e}")
        return False


def test_error_recovery():
    """Test error recovery and resilience"""
    print("\n" + "=" * 50)
    print("TESTING ERROR RECOVERY")
    print("=" * 50)
    
    try:
        config = load_config()
        
        # Test with invalid configuration
        print("Testing with invalid configuration...")
        invalid_config = {"invalid": "config"}
        
        try:
            health_checker = SystemHealthChecker(invalid_config)
            checks = health_checker.run_all_checks()
            print("✅ Health checker handles invalid config gracefully")
        except Exception as e:
            print(f"⚠️  Health checker error with invalid config: {e}")
        
        # Test backup with missing directories
        print("Testing backup with missing directories...")
        try:
            backup_manager = BackupManager(config)
            # This should create directories if they don't exist
            print("✅ Backup manager handles missing directories")
        except Exception as e:
            print(f"⚠️  Backup manager error: {e}")
        
        # Test template manager with missing templates
        print("Testing template manager resilience...")
        try:
            template_manager = StoryTemplateManager(config)
            templates = template_manager.list_templates()
            print(f"✅ Template manager loaded {len(templates)} templates")
        except Exception as e:
            print(f"⚠️  Template manager error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error recovery test failed: {e}")
        return False


def test_integration():
    """Test integration between components"""
    print("\n" + "=" * 50)
    print("TESTING COMPONENT INTEGRATION")
    print("=" * 50)
    
    try:
        config = load_config()
        
        # Test health check → backup → template workflow
        print("Testing integrated workflow...")
        
        # 1. Health check
        health_checker = SystemHealthChecker(config)
        health_checker.run_all_checks()
        overall_status = health_checker.get_overall_status()
        print(f"✅ Health check: {overall_status.value}")
        
        # 2. Create backup before major operation
        backup_manager = BackupManager(config)
        backup_path = backup_manager.create_backup("integration_test", "Before integration test")
        if backup_path:
            print("✅ Backup created before operation")
        
        # 3. Create story from template
        template_manager = StoryTemplateManager(config)
        templates = template_manager.list_templates()
        
        if templates:
            story = template_manager.create_story_from_template(
                templates[0]['id'], "Integration Test Story", "Charmer"
            )
            
            if story:
                print("✅ Story created from template")
                
                # 4. Validate the integrated story
                is_valid, errors = story.validate_structure()
                if is_valid:
                    print("✅ Integrated story is valid")
                else:
                    print(f"⚠️  Integrated story has {len(errors)} issues")
                
                # 5. Test character system integration
                char_manager = story.get_character_manager()
                if char_manager:
                    consistency_ok, issues = story.validate_character_consistency()
                    if consistency_ok:
                        print("✅ Character consistency validated")
                    else:
                        print(f"⚠️  Character consistency issues: {len(issues)}")
        
        print("✅ Integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main test function"""
    print("🧪 CYOA Automation System - Complete System Test")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("System Health Checker", test_system_health),
        ("Backup System", test_backup_system),
        ("Story Templates", test_story_templates),
        ("Wizard Components", test_wizard_components),
        ("Error Recovery", test_error_recovery),
        ("Component Integration", test_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    elapsed_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("COMPLETE SYSTEM TEST SUMMARY")
    print("=" * 60)
    
    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nResults: {passed_count}/{total_count} tests passed")
    print(f"Time: {elapsed_time:.1f} seconds")
    
    if passed_count == total_count:
        print("\n🎉 ALL TESTS PASSED!")
        print("\nThe CYOA Automation System is ready for production use!")
        print("\n🚀 Next steps:")
        print("1. Install PyQt5 for GUI: pip install PyQt5")
        print("2. Run the application: python main.py")
        print("3. Use the Story Creation Wizard to create your first story")
        print("4. Configure X API credentials for posting")
        print("5. Start creating amazing interactive stories!")
        return 0
    else:
        print(f"\n⚠️  {total_count - passed_count} TESTS FAILED")
        print("\nSome components need attention before production use.")
        print("Check the test output above for specific issues.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
