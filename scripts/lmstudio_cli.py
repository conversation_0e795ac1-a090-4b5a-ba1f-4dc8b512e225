#!/usr/bin/env python3
"""
LM Studio CLI - Command-line interface for LM Studio management
Beautiful CLI for model management, testing, and configuration
"""

import sys
import json
from pathlib import Path
from typing import Optional, Dict, Any

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from utils.lmstudio_client import LMStudioClient
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run lmstudio")
    sys.exit(1)

app = typer.Typer(help="🧠 LM Studio Management CLI")
console = Console()


@app.command()
def status():
    """Check LM Studio connection and model status"""
    
    console.print(Panel.fit(
        "[bold blue]🧠 LM Studio Status[/bold blue]\n"
        "[dim]Connection and model information[/dim]",
        border_style="blue"
    ))
    
    config = load_config()
    client = LMStudioClient(config)
    
    # Test connection
    test_result = client.test_connection()
    
    if test_result['status'] == 'success':
        console.print("[green]✅ LM Studio is running and accessible[/green]")
        
        details = test_result.get('details', {})
        
        # Show connection info
        console.print("\n[bold cyan]📡 Connection Information[/bold cyan]")
        
        table = Table(show_header=False, box=None)
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="white")
        
        table.add_row("Base URL", client.base_url)
        table.add_row("Response Time", f"{details.get('response_time', 0):.2f}s")
        table.add_row("Tokens Used", str(details.get('tokens_used', 0)))
        
        console.print(table)
        
        # Show available models
        models = details.get('available_models', [])
        current_model = details.get('current_model')
        
        if models:
            console.print("\n[bold green]🤖 Available Models[/bold green]")
            
            model_table = Table()
            model_table.add_column("Model", style="cyan")
            model_table.add_column("Status", style="green")
            
            for model in models:
                status = "🟢 Active" if model == current_model else "⚪ Available"
                model_table.add_row(model, status)
            
            console.print(model_table)
        else:
            console.print("\n[yellow]⚠️ No models loaded in LM Studio[/yellow]")
            
    elif test_result['status'] == 'warning':
        console.print(f"[yellow]⚠️ {test_result['message']}[/yellow]")
        
        suggestions = test_result.get('suggestions', [])
        if suggestions:
            console.print("\n[bold]💡 Suggestions:[/bold]")
            for suggestion in suggestions:
                console.print(f"  • {suggestion}")
                
    else:
        console.print(f"[red]❌ {test_result['message']}[/red]")
        
        suggestions = test_result.get('suggestions', [])
        if suggestions:
            console.print("\n[bold]💡 Suggestions:[/bold]")
            for suggestion in suggestions:
                console.print(f"  • {suggestion}")


@app.command()
def models():
    """List available models in LM Studio"""
    
    console.print(Panel.fit(
        "[bold magenta]🤖 Model Management[/bold magenta]\n"
        "[dim]Available models in LM Studio[/dim]",
        border_style="magenta"
    ))
    
    config = load_config()
    client = LMStudioClient(config)
    
    if not client.is_available():
        console.print("[red]❌ LM Studio is not running[/red]")
        console.print("\nPlease start LM Studio and try again")
        return
    
    models = client.get_available_models()
    current_model = client.get_current_model()
    
    if not models:
        console.print("[yellow]⚠️ No models loaded in LM Studio[/yellow]")
        console.print("\nTo load a model:")
        console.print("1. Open LM Studio")
        console.print("2. Go to the Chat tab")
        console.print("3. Select and load a model")
        return
    
    console.print(f"\n[bold]Found {len(models)} model(s):[/bold]")
    
    table = Table()
    table.add_column("Model", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="dim")
    
    for model in models:
        if model == current_model:
            status = "🟢 Active"
            details = "Currently loaded and ready"
        else:
            status = "⚪ Available"
            details = "Available for loading"
        
        table.add_row(model, status, details)
    
    console.print(table)
    
    if current_model:
        console.print(f"\n[green]✅ Current model: {current_model}[/green]")
    else:
        console.print("\n[yellow]⚠️ No model currently active[/yellow]")


@app.command()
def test(
    prompt: str = typer.Option("Hello, how are you?", "--prompt", "-p", help="Test prompt"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Specific model to test"),
    temperature: float = typer.Option(0.7, "--temperature", "-t", help="Temperature for generation"),
    max_tokens: int = typer.Option(100, "--max-tokens", help="Maximum tokens to generate")
):
    """Test text generation with LM Studio"""
    
    console.print(Panel.fit(
        "[bold yellow]🧪 Text Generation Test[/bold yellow]\n"
        "[dim]Testing LM Studio text generation[/dim]",
        border_style="yellow"
    ))
    
    config = load_config()
    client = LMStudioClient(config)
    
    if not client.is_available():
        console.print("[red]❌ LM Studio is not running[/red]")
        return
    
    console.print(f"\n[cyan]📝 Prompt:[/cyan] {prompt}")
    console.print(f"[cyan]🎛️ Temperature:[/cyan] {temperature}")
    console.print(f"[cyan]📏 Max Tokens:[/cyan] {max_tokens}")
    
    if model:
        console.print(f"[cyan]🤖 Model:[/cyan] {model}")
    
    # Generate text with progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Generating text...", total=None)
        
        response = client.generate_text(
            prompt=prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        progress.remove_task(task)
    
    if response.success:
        console.print("\n[green]✅ Generation successful![/green]")
        
        # Show response
        console.print("\n[bold cyan]📄 Generated Text:[/bold cyan]")
        console.print(Panel(response.text, border_style="green"))
        
        # Show metadata
        console.print("\n[bold]📊 Generation Details:[/bold]")
        
        details_table = Table(show_header=False, box=None)
        details_table.add_column("Metric", style="cyan")
        details_table.add_column("Value", style="white")
        
        details_table.add_row("Model Used", response.model or "Unknown")
        details_table.add_row("Tokens Used", str(response.tokens_used))
        details_table.add_row("Response Time", f"{response.response_time:.2f}s")
        
        console.print(details_table)
        
    else:
        console.print(f"\n[red]❌ Generation failed: {response.error}[/red]")


@app.command()
def config():
    """Show and manage LM Studio configuration"""
    
    console.print(Panel.fit(
        "[bold green]⚙️ LM Studio Configuration[/bold green]\n"
        "[dim]Current configuration settings[/dim]",
        border_style="green"
    ))
    
    config_data = load_config()
    lmstudio_config = config_data.get('lmstudio', {})
    
    console.print("\n[bold]Current Configuration:[/bold]")
    
    table = Table(show_header=False, box=None)
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="white")
    
    table.add_row("Base URL", lmstudio_config.get('base_url', 'http://localhost:1234'))
    table.add_row("Default Model", lmstudio_config.get('default_model', 'local-model'))
    table.add_row("Timeout", f"{lmstudio_config.get('timeout', 120)}s")
    table.add_row("Max Tokens", str(lmstudio_config.get('max_tokens', 2048)))
    table.add_row("Temperature", str(lmstudio_config.get('temperature', 0.7)))
    
    console.print(table)
    
    if Confirm.ask("\nWould you like to update the configuration?"):
        update_config_interactive(config_data)


@app.command()
def benchmark(
    iterations: int = typer.Option(5, "--iterations", "-i", help="Number of test iterations"),
    prompt: str = typer.Option("Write a short story about a robot.", "--prompt", "-p", help="Benchmark prompt")
):
    """Benchmark LM Studio performance"""
    
    console.print(Panel.fit(
        "[bold red]🏃 Performance Benchmark[/bold red]\n"
        "[dim]Testing LM Studio performance and reliability[/dim]",
        border_style="red"
    ))
    
    config = load_config()
    client = LMStudioClient(config)
    
    if not client.is_available():
        console.print("[red]❌ LM Studio is not running[/red]")
        return
    
    console.print(f"\n[cyan]Running {iterations} iterations...[/cyan]")
    console.print(f"[cyan]Prompt:[/cyan] {prompt}")
    
    results = []
    
    with Progress(console=console) as progress:
        task = progress.add_task("Running benchmark...", total=iterations)
        
        for i in range(iterations):
            response = client.generate_text(prompt=prompt, max_tokens=100)
            
            if response.success:
                results.append({
                    'success': True,
                    'response_time': response.response_time,
                    'tokens_used': response.tokens_used,
                    'text_length': len(response.text)
                })
            else:
                results.append({
                    'success': False,
                    'error': response.error
                })
            
            progress.advance(task)
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    console.print(f"\n[bold]📊 Benchmark Results:[/bold]")
    
    summary_table = Table(show_header=False, box=None)
    summary_table.add_column("Metric", style="cyan")
    summary_table.add_column("Value", style="white")
    
    summary_table.add_row("Total Iterations", str(iterations))
    summary_table.add_row("Successful", f"{len(successful)} ({len(successful)/iterations*100:.1f}%)")
    summary_table.add_row("Failed", f"{len(failed)} ({len(failed)/iterations*100:.1f}%)")
    
    if successful:
        avg_time = sum(r['response_time'] for r in successful) / len(successful)
        avg_tokens = sum(r['tokens_used'] for r in successful) / len(successful)
        avg_length = sum(r['text_length'] for r in successful) / len(successful)
        
        summary_table.add_row("Avg Response Time", f"{avg_time:.2f}s")
        summary_table.add_row("Avg Tokens Used", f"{avg_tokens:.1f}")
        summary_table.add_row("Avg Text Length", f"{avg_length:.1f} chars")
    
    console.print(summary_table)
    
    if failed:
        console.print(f"\n[red]❌ {len(failed)} iterations failed[/red]")
        for i, failure in enumerate(failed[:3]):  # Show first 3 failures
            console.print(f"  {i+1}. {failure['error']}")


def update_config_interactive(config_data: Dict[str, Any]):
    """Interactive configuration update"""
    lmstudio_config = config_data.setdefault('lmstudio', {})
    
    console.print("\n[bold]Update Configuration:[/bold]")
    
    # Base URL
    current_url = lmstudio_config.get('base_url', 'http://localhost:1234')
    new_url = Prompt.ask("Base URL", default=current_url)
    lmstudio_config['base_url'] = new_url
    
    # Timeout
    current_timeout = lmstudio_config.get('timeout', 120)
    new_timeout = typer.prompt("Timeout (seconds)", default=current_timeout, type=int)
    lmstudio_config['timeout'] = new_timeout
    
    # Max tokens
    current_max_tokens = lmstudio_config.get('max_tokens', 2048)
    new_max_tokens = typer.prompt("Max tokens", default=current_max_tokens, type=int)
    lmstudio_config['max_tokens'] = new_max_tokens
    
    # Temperature
    current_temp = lmstudio_config.get('temperature', 0.7)
    new_temp = typer.prompt("Temperature", default=current_temp, type=float)
    lmstudio_config['temperature'] = new_temp
    
    # Save configuration
    save_config(config_data)
    console.print("\n[green]✅ Configuration updated successfully![/green]")


def load_config() -> Dict[str, Any]:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def save_config(config: Dict[str, Any]):
    """Save configuration"""
    config_path = Path("config.json")
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        console.print(f"[red]Error saving config: {e}[/red]")


if __name__ == "__main__":
    app()
