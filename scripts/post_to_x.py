#!/usr/bin/env python3
"""
Post to X Script - Automated posting to X (Twitter)
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from story.story_web import <PERSON><PERSON>eb
    from social.x_poster import XPoster
    from social.paywall_manager import PaywallManager
    from rich.console import Console
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    from rich.table import Table
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

if RICH_AVAILABLE:
    app = typer.Typer(help="🐦 X (Twitter) Posting Tool")
    console = Console()
else:
    class SimpleApp:
        def command(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    app = SimpleApp()


@app.command()
def post(
    story_path: str = typer.Argument(..., help="Path to story file"),
    node_id: str = typer.Option(None, "--node", "-n", help="Specific node to post"),
    dry_run: bool = typer.Option(False, "--dry-run", "-d", help="Preview without posting"),
    schedule: bool = typer.Option(False, "--schedule", "-s", help="Schedule posts instead of immediate")
):
    """Post story content to X (Twitter)"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold blue]🐦 Posting to X[/bold blue]\n"
            f"[dim]Story: {story_path}[/dim]",
            border_style="blue"
        ))
    else:
        print(f"🐦 Posting to X: {story_path}")
    
    # Load story
    story = load_story(story_path)
    if not story:
        return
    
    # Load config
    config = load_config()
    
    try:
        # Initialize X poster
        x_poster = XPoster(config)
        paywall_manager = PaywallManager(config)
        
        # Check X API credentials
        if not x_poster.is_authenticated():
            if RICH_AVAILABLE:
                console.print("[red]❌ X API credentials not configured[/red]")
                console.print("[dim]Please set up your X API credentials in .env file[/dim]")
            else:
                print("❌ X API credentials not configured")
                print("Please set up your X API credentials in .env file")
            return
        
        # Determine nodes to post
        if node_id:
            if node_id not in story.nodes:
                if RICH_AVAILABLE:
                    console.print(f"[red]❌ Node not found: {node_id}[/red]")
                else:
                    print(f"❌ Node not found: {node_id}")
                return
            nodes_to_post = [story.nodes[node_id]]
        else:
            # Post entry points by default
            nodes_to_post = [story.nodes[node_id] for node_id in story.entry_points if node_id in story.nodes]
            
            if not nodes_to_post:
                if RICH_AVAILABLE:
                    console.print("[yellow]⚠️  No entry points found, posting first node[/yellow]")
                else:
                    print("⚠️  No entry points found, posting first node")
                nodes_to_post = [list(story.nodes.values())[0]] if story.nodes else []
        
        if not nodes_to_post:
            if RICH_AVAILABLE:
                console.print("[red]❌ No nodes to post[/red]")
            else:
                print("❌ No nodes to post")
            return
        
        # Preview posts
        if RICH_AVAILABLE:
            console.print(f"\n[cyan]📋 Posts to create: {len(nodes_to_post)}[/cyan]")
        else:
            print(f"\n📋 Posts to create: {len(nodes_to_post)}")
        
        for i, node in enumerate(nodes_to_post):
            if RICH_AVAILABLE:
                console.print(f"\n[bold]Post {i+1}:[/bold]")
                console.print(Panel(
                    node.text[:200] + ("..." if len(node.text) > 200 else ""),
                    title=f"Node: {node.id}",
                    border_style="dim"
                ))
            else:
                print(f"\nPost {i+1} (Node: {node.id}):")
                print(f"  {node.text[:200]}{'...' if len(node.text) > 200 else ''}")
        
        # Confirmation for actual posting
        if not dry_run:
            if RICH_AVAILABLE:
                if not Confirm.ask("\nProceed with posting?"):
                    console.print("[yellow]Posting cancelled[/yellow]")
                    return
            else:
                response = input("\nProceed with posting? (y/n): ")
                if response.lower() != 'y':
                    print("Posting cancelled")
                    return
        
        # Post nodes
        if dry_run:
            if RICH_AVAILABLE:
                console.print("\n[yellow]🔍 Dry run - no actual posts created[/yellow]")
            else:
                print("\n🔍 Dry run - no actual posts created")
        else:
            if RICH_AVAILABLE:
                console.print("\n[blue]📤 Creating posts...[/blue]")
            else:
                print("\n📤 Creating posts...")
            
            for i, node in enumerate(nodes_to_post):
                try:
                    if schedule:
                        # Schedule post for later
                        post_id = x_poster.schedule_post(node, delay_minutes=i * 60)
                        if RICH_AVAILABLE:
                            console.print(f"[green]✅ Scheduled post {i+1}: {post_id}[/green]")
                        else:
                            print(f"✅ Scheduled post {i+1}: {post_id}")
                    else:
                        # Post immediately
                        post_id = x_poster.post_story_node(node)
                        if RICH_AVAILABLE:
                            console.print(f"[green]✅ Posted {i+1}: {post_id}[/green]")
                        else:
                            print(f"✅ Posted {i+1}: {post_id}")
                
                except Exception as e:
                    if RICH_AVAILABLE:
                        console.print(f"[red]❌ Failed to post node {node.id}: {e}[/red]")
                    else:
                        print(f"❌ Failed to post node {node.id}: {e}")
            
            if RICH_AVAILABLE:
                console.print(f"\n[green]🎉 Posting complete![/green]")
            else:
                print(f"\n🎉 Posting complete!")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Posting failed: {e}[/red]")
        else:
            print(f"❌ Posting failed: {e}")


@app.command()
def status():
    """Check X API status and recent posts"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold cyan]🐦 X API Status[/bold cyan]",
            border_style="cyan"
        ))
    else:
        print("🐦 X API Status")
    
    try:
        config = load_config()
        x_poster = XPoster(config)
        
        # Check authentication
        if x_poster.is_authenticated():
            if RICH_AVAILABLE:
                console.print("[green]✅ X API authenticated[/green]")
            else:
                print("✅ X API authenticated")
            
            # Get user info
            try:
                user_info = x_poster.get_user_info()
                if user_info and RICH_AVAILABLE:
                    table = Table(show_header=False, box=None)
                    table.add_column("Field", style="cyan")
                    table.add_column("Value", style="green")
                    
                    table.add_row("Username", f"@{user_info.get('username', 'unknown')}")
                    table.add_row("Followers", str(user_info.get('public_metrics', {}).get('followers_count', 0)))
                    table.add_row("Following", str(user_info.get('public_metrics', {}).get('following_count', 0)))
                    
                    console.print(table)
                elif user_info:
                    print(f"Username: @{user_info.get('username', 'unknown')}")
                    print(f"Followers: {user_info.get('public_metrics', {}).get('followers_count', 0)}")
                    print(f"Following: {user_info.get('public_metrics', {}).get('following_count', 0)}")
            
            except Exception as e:
                if RICH_AVAILABLE:
                    console.print(f"[yellow]⚠️  Could not get user info: {e}[/yellow]")
                else:
                    print(f"⚠️  Could not get user info: {e}")
        
        else:
            if RICH_AVAILABLE:
                console.print("[red]❌ X API not authenticated[/red]")
                console.print("[dim]Check your credentials in .env file[/dim]")
            else:
                print("❌ X API not authenticated")
                print("Check your credentials in .env file")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error checking X status: {e}[/red]")
        else:
            print(f"❌ Error checking X status: {e}")


@app.command()
def analytics():
    """Show posting analytics and subscriber stats"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold magenta]📊 Analytics[/bold magenta]",
            border_style="magenta"
        ))
    else:
        print("📊 Analytics")
    
    try:
        config = load_config()
        paywall_manager = PaywallManager(config)
        
        # Get subscriber stats
        stats = paywall_manager.get_subscriber_stats()
        
        if RICH_AVAILABLE:
            table = Table(title="Subscriber Statistics")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Subscribers", str(stats.get('total_subscribers', 0)))
            table.add_row("Active Subscribers", str(stats.get('active_subscribers', 0)))
            table.add_row("Revenue Estimate", f"${stats.get('revenue_estimate', 0):.2f}")
            table.add_row("Free Tier", str(stats.get('free_subscribers', 0)))
            table.add_row("Premium Tier", str(stats.get('premium_subscribers', 0)))
            table.add_row("Spicy Tier", str(stats.get('spicy_subscribers', 0)))
            
            console.print(table)
        else:
            print("Subscriber Statistics:")
            print(f"  Total Subscribers: {stats.get('total_subscribers', 0)}")
            print(f"  Active Subscribers: {stats.get('active_subscribers', 0)}")
            print(f"  Revenue Estimate: ${stats.get('revenue_estimate', 0):.2f}")
            print(f"  Free Tier: {stats.get('free_subscribers', 0)}")
            print(f"  Premium Tier: {stats.get('premium_subscribers', 0)}")
            print(f"  Spicy Tier: {stats.get('spicy_subscribers', 0)}")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error getting analytics: {e}[/red]")
        else:
            print(f"❌ Error getting analytics: {e}")


def load_story(story_path: str):
    """Load story from file"""
    story_file = Path(story_path)
    if not story_file.exists():
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Story file not found: {story_path}[/red]")
        else:
            print(f"❌ Story file not found: {story_path}")
        return None
    
    try:
        story = StoryWeb.load_from_file(story_path)
        if not story:
            if RICH_AVAILABLE:
                console.print("[red]❌ Failed to load story[/red]")
            else:
                print("❌ Failed to load story")
            return None
        
        if RICH_AVAILABLE:
            console.print(f"[green]✅ Loaded story with {len(story.nodes)} nodes[/green]")
        else:
            print(f"✅ Loaded story with {len(story.nodes)} nodes")
        
        return story
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error loading story: {e}[/red]")
        else:
            print(f"❌ Error loading story: {e}")
        return None


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main function for non-typer usage"""
    if len(sys.argv) < 2:
        print("Usage: python post_to_x.py <command> [story_file]")
        print("Commands: post, status, analytics")
        print("   or: pixi run post <command> [story_file]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "post" and len(sys.argv) > 2:
        story_path = sys.argv[2]
        post(story_path)
    elif command == "status":
        status()
    elif command == "analytics":
        analytics()
    else:
        print(f"Unknown command or missing story file: {command}")


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
