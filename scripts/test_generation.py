#!/usr/bin/env python3
"""
Test Story Generation
Test the story generation functionality without GUI dependencies
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.lmstudio_client import LMStudioClient
from story.story_generator import StoryGenerator


def test_lmstudio_connection():
    """Test connection to LM Studio"""
    print("Testing LM Studio connection...")

    client = LMStudioClient({})

    if client.is_available():
        print("✅ LM Studio is available")
        
        # Test simple generation
        response = client.generate_text("Say hello in one word.", max_tokens=10)
        if response.success:
            print(f"✅ Test generation successful: '{response.text}'")
            return True
        else:
            print(f"❌ Test generation failed: {response.error}")
            return False
    else:
        print("❌ LM Studio is not available")
        print("Please start LM Studio and load a model")
        return False


def test_story_generation():
    """Test story generation from text"""
    print("\nTesting story generation...")
    
    # Load config
    config_path = Path("config.json")
    if config_path.exists():
        with open(config_path) as f:
            config = json.load(f)
    else:
        print("❌ config.json not found")
        return False
    
    # Initialize components
    lmstudio_client = LMStudioClient(config)
    story_generator = StoryGenerator(config, lmstudio_client)
    
    # Test with Hansel and Gretel
    source_file = Path("data/templates/hansel_gretel.txt")
    if not source_file.exists():
        print(f"❌ Source file not found: {source_file}")
        return False
    
    with open(source_file, 'r', encoding='utf-8') as f:
        source_text = f.read()
    
    print("Generating storyline from Hansel and Gretel...")
    print("This may take a minute...")
    
    story = story_generator.import_storyline_from_text(
        source_text, "Hansel and Gretel", "Ranger"
    )
    
    if story:
        print("✅ Story generation successful!")
        print(f"   Nodes: {len(story.nodes)}")
        print(f"   Entry points: {len(story.entry_points)}")
        print(f"   Endings: {len(story.endings)}")
        
        # Save the generated story
        output_path = "data/storylines/hansel_gretel_generated.json"
        if story.save_to_file(output_path):
            print(f"✅ Story saved to: {output_path}")
        
        # Show first few nodes
        print("\nFirst few nodes:")
        for i, (node_id, node) in enumerate(story.nodes.items()):
            if i >= 3:  # Show only first 3 nodes
                break
            print(f"  {node_id}: {node.text[:60]}...")
            for choice in node.choices[:2]:  # Show first 2 choices
                print(f"    → {choice.text}")
        
        return True
    else:
        print("❌ Story generation failed")
        return False


def main():
    """Main test function"""
    print("🧪 Testing CYOA Story Generation")
    print("=" * 40)
    
    # Test LM Studio connection
    if not test_lmstudio_connection():
        print("\n❌ Cannot proceed without LM Studio")
        print("Please install and start LM Studio:")
        print("1. Download LM Studio from https://lmstudio.ai/")
        print("2. Load a model (e.g., Llama 3.1)")
        print("3. Start the local server")
        return 1
    
    # Test story generation
    if not test_story_generation():
        print("\n❌ Story generation test failed")
        return 1
    
    print("\n🎉 All tests passed!")
    print("\nThe story generation system is working correctly.")
    print("You can now:")
    print("1. Install PyQt5 to run the full GUI: pip install PyQt5")
    print("2. Use the command-line tools in scripts/")
    print("3. Generate more stories with different themes")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
