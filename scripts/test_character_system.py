#!/usr/bin/env python3
"""
Test Character System
Test character tracking, consistency, and integration with story generation
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from story.character_system import (
    CharacterManager, Character, CharacterRole, CharacterAppearance, 
    CharacterVoice, CharacterPersonality, CharacterStatus
)
from story.story_web import Story<PERSON>eb, StoryNode, NodeType
from utils.lmstudio_client import LMStudioClient


def test_character_creation():
    """Test basic character creation"""
    print("=" * 50)
    print("TESTING CHARACTER CREATION")
    print("=" * 50)
    
    config = {"character_system": {}}
    char_manager = CharacterManager(config)
    
    # Create a protagonist
    protagonist = char_manager.create_character(
        name="Hero",
        role=CharacterRole.PROTAGONIST,
        appearance=CharacterAppearance(
            physical_description="A brave young warrior",
            age_range="young adult",
            hair_color="brown",
            eye_color="blue"
        ),
        voice=CharacterVoice(
            voice_description="clear and confident",
            accent="slight rural accent",
            speech_patterns=["speaks with conviction", "uses simple words"]
        ),
        personality=CharacterPersonality(
            traits=["brave", "determined", "loyal"],
            motivations=["save the kingdom", "protect friends"],
            fears=["failure", "losing loved ones"],
            moral_alignment="good"
        ),
        is_player_character=True,
        importance_level=5
    )
    
    print(f"✅ Created protagonist: {protagonist.name}")
    print(f"   Role: {protagonist.role.value}")
    print(f"   Appearance: {protagonist.appearance.get_visual_prompt()}")
    print(f"   Personality: {protagonist.personality.get_personality_prompt()}")
    
    # Create an antagonist
    antagonist = char_manager.create_character(
        name="Dark Lord",
        role=CharacterRole.ANTAGONIST,
        appearance=CharacterAppearance(
            physical_description="A tall figure in black robes",
            distinctive_features="glowing red eyes",
            clothing="dark ceremonial robes"
        ),
        personality=CharacterPersonality(
            traits=["cunning", "ruthless", "powerful"],
            motivations=["world domination", "revenge"],
            moral_alignment="evil"
        ),
        importance_level=5
    )
    
    print(f"✅ Created antagonist: {antagonist.name}")
    
    # Test character summary
    summary = char_manager.get_character_summary()
    print(f"✅ Character summary: {summary['total_characters']} characters")
    print(f"   By role: {summary['by_role']}")
    
    return char_manager


def test_character_story_integration():
    """Test character integration with story web"""
    print("\n" + "=" * 50)
    print("TESTING CHARACTER-STORY INTEGRATION")
    print("=" * 50)
    
    # Create story web
    config = {
        "story_generation": {"max_nodes": 10},
        "character_system": {}
    }
    
    story = StoryWeb(config)
    char_manager = story.get_character_manager()
    
    # Create characters
    hero = char_manager.create_character("Hero", CharacterRole.PROTAGONIST, is_player_character=True)
    villain = char_manager.create_character("Villain", CharacterRole.ANTAGONIST)
    guide = char_manager.create_character("Wise Guide", CharacterRole.MENTOR)
    
    print(f"✅ Created {len(char_manager.characters)} characters")
    
    # Create story nodes
    entry_node = StoryNode(
        id="entry",
        text="You enter a dark forest. A wise old man approaches you.",
        node_type=NodeType.ENTRY,
        is_entry=True,
        location="Dark Forest Entrance"
    )
    
    cave_node = StoryNode(
        id="cave",
        text="Inside the cave, you face the villain in final combat!",
        node_type=NodeType.STORY,
        location="Evil Lair"
    )
    
    story.add_node(entry_node)
    story.add_node(cave_node)
    
    # Add characters to nodes
    story.add_character_to_node("entry", hero.id, {"emotional_state": "curious"})
    story.add_character_to_node("entry", guide.id, {"emotional_state": "wise"})
    
    story.add_character_to_node("cave", hero.id, {"emotional_state": "determined"})
    story.add_character_to_node("cave", villain.id, {"emotional_state": "angry"})
    
    print(f"✅ Added characters to story nodes")
    
    # Test character context generation
    entry_context = story.get_character_context_for_generation("entry")
    print(f"✅ Entry node context: {len(entry_context['present_characters'])} characters present")
    
    cave_context = story.get_character_context_for_generation("cave")
    print(f"✅ Cave node context: {len(cave_context['present_characters'])} characters present")
    
    # Test character consistency
    is_consistent, issues = story.validate_character_consistency()
    if is_consistent:
        print("✅ Character consistency validated")
    else:
        print(f"⚠️  Character consistency issues: {len(issues)}")
        for issue in issues:
            print(f"   - {issue}")
    
    return story


def test_character_dialogue_generation():
    """Test character-specific dialogue generation"""
    print("\n" + "=" * 50)
    print("TESTING CHARACTER DIALOGUE GENERATION")
    print("=" * 50)
    
    # Initialize LM Studio client
    lmstudio_client = LMStudioClient({})
    if not lmstudio_client.is_available():
        print("⚠️  LM Studio not available - skipping dialogue generation test")
        return
    
    config = {"character_system": {}}
    char_manager = CharacterManager(config)
    
    # Create a character with distinct personality
    character = char_manager.create_character(
        name="Gruff Dwarf",
        role=CharacterRole.ALLY,
        appearance=CharacterAppearance(
            physical_description="Short, stocky dwarf with a braided beard"
        ),
        voice=CharacterVoice(
            voice_description="deep and gruff",
            accent="Scottish-like accent",
            speech_patterns=["uses 'aye' frequently", "speaks in short sentences"],
            vocabulary_style="informal"
        ),
        personality=CharacterPersonality(
            traits=["gruff", "loyal", "practical"],
            motivations=["protect clan", "forge great weapons"],
            quirks=["always mentions his beard", "distrusts magic"]
        )
    )
    
    print(f"✅ Created character: {character.name}")
    
    # Generate dialogue
    context = "The party is discussing whether to enter a magical portal"
    dialogue = char_manager.generate_character_dialogue(
        character.id, context, lmstudio_client
    )
    
    if dialogue:
        print(f"✅ Generated dialogue: '{dialogue}'")
    else:
        print("❌ Failed to generate dialogue")
    
    # Suggest actions
    actions = char_manager.suggest_character_actions(
        character.id, context, lmstudio_client
    )
    
    if actions:
        print(f"✅ Suggested actions:")
        for action in actions:
            print(f"   - {action}")
    else:
        print("❌ Failed to suggest actions")


def test_character_serialization():
    """Test character data serialization"""
    print("\n" + "=" * 50)
    print("TESTING CHARACTER SERIALIZATION")
    print("=" * 50)
    
    config = {"character_system": {}}
    char_manager = CharacterManager(config)
    
    # Create test character
    character = char_manager.create_character(
        name="Test Character",
        role=CharacterRole.ALLY,
        appearance=CharacterAppearance(physical_description="Test appearance"),
        personality=CharacterPersonality(traits=["test trait"])
    )
    
    print(f"✅ Created test character: {character.name}")
    
    # Export character data
    char_data = char_manager.export_character_data(character.id)
    if char_data:
        print("✅ Character data exported successfully")
        print(f"   Data keys: {list(char_data.keys())}")
    else:
        print("❌ Failed to export character data")
        return
    
    # Create new manager and import
    new_char_manager = CharacterManager(config)
    success = new_char_manager.import_character_data(char_data)
    
    if success:
        print("✅ Character data imported successfully")
        imported_char = list(new_char_manager.characters.values())[0]
        print(f"   Imported character: {imported_char.name}")
    else:
        print("❌ Failed to import character data")
    
    # Test full manager serialization
    manager_data = char_manager.to_dict()
    print(f"✅ Manager serialized: {len(manager_data.get('characters', {}))} characters")
    
    # Test manager reconstruction
    reconstructed_manager = CharacterManager.from_dict(config, manager_data)
    print(f"✅ Manager reconstructed: {len(reconstructed_manager.characters)} characters")


def test_character_extraction():
    """Test character extraction from text"""
    print("\n" + "=" * 50)
    print("TESTING CHARACTER EXTRACTION")
    print("=" * 50)
    
    # Initialize LM Studio client
    lmstudio_client = LMStudioClient({})
    if not lmstudio_client.is_available():
        print("⚠️  LM Studio not available - skipping character extraction test")
        return
    
    # Sample story text
    story_text = """
    Once upon a time, there was a brave young knight named Sir Galahad who lived in the kingdom of Camelot. 
    He was known for his pure heart and unwavering courage. One day, the evil sorcerer Morgana cast a dark 
    spell over the land, turning the crops to ash and the rivers to poison. 
    
    King Arthur called upon his bravest knights, including Sir Galahad and the wise Sir Merlin, to find a 
    way to break the curse. Along their journey, they met a mysterious old woman who claimed to know the 
    secret to defeating Morgana's magic.
    """
    
    print("✅ Testing character extraction from sample text")
    
    # Test extraction
    response = lmstudio_client.extract_characters_from_text(story_text, "The Quest")
    
    if response.success:
        print("✅ Character extraction successful")
        print("Raw response preview:")
        print(response.text[:200] + "..." if len(response.text) > 200 else response.text)
        
        # Try to parse the response
        try:
            import json
            json_start = response.text.find('[')
            json_end = response.text.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response.text[json_start:json_end]
                characters_data = json.loads(json_text)
                
                print(f"✅ Parsed {len(characters_data)} characters:")
                for char_data in characters_data:
                    name = char_data.get('name', 'Unknown')
                    role = char_data.get('role', 'unknown')
                    print(f"   - {name} ({role})")
            else:
                print("⚠️  No JSON array found in response")
                
        except json.JSONDecodeError as e:
            print(f"⚠️  Failed to parse character JSON: {e}")
    else:
        print(f"❌ Character extraction failed: {response.error}")


def main():
    """Main test function"""
    print("🎭 Character System Test Suite")
    print("=" * 60)
    
    try:
        # Test character creation
        char_manager = test_character_creation()
        
        # Test story integration
        story = test_character_story_integration()
        
        # Test dialogue generation
        test_character_dialogue_generation()
        
        # Test serialization
        test_character_serialization()
        
        # Test character extraction
        test_character_extraction()
        
        print("\n" + "=" * 60)
        print("CHARACTER SYSTEM TEST SUMMARY")
        print("=" * 60)
        print("✅ Character creation and management")
        print("✅ Story web integration")
        print("✅ Character state tracking")
        print("✅ Data serialization")
        print("✅ AI-powered dialogue generation")
        print("✅ Character extraction from text")
        
        print("\n🎉 CHARACTER SYSTEM TESTS COMPLETED!")
        print("\nThe character system provides:")
        print("• Comprehensive character modeling")
        print("• Story integration with location tracking")
        print("• AI-powered dialogue and action generation")
        print("• Character consistency validation")
        print("• Complete serialization support")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
