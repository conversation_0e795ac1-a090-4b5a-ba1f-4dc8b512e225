#!/usr/bin/env python3
"""
Logging Management CLI
Beautiful command-line interface for managing logging configuration and viewing logs
"""

import sys
import json
from pathlib import Path
from typing import Optional, List
from datetime import datetime, timedelta

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.syntax import Syntax
from rich.live import Live
from rich.layout import Layout
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from utils.logging_config import LogLevel, setup_logging, get_logger, set_verbosity
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run logging")
    sys.exit(1)

app = typer.Typer(help="📊 Logging Management CLI")
console = Console()


@app.command()
def status():
    """Show current logging status and configuration"""
    
    console.print(Panel.fit(
        "[bold blue]📊 Logging System Status[/bold blue]\n"
        "[dim]Current logging configuration and statistics[/dim]",
        border_style="blue"
    ))
    
    # Load current configuration
    config = load_config()
    logging_config = config.get('logging', {})
    
    # Show configuration
    console.print("\n[bold cyan]⚙️ Current Configuration[/bold cyan]")
    
    config_table = Table(show_header=False, box=None)
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="white")
    
    verbosity = logging_config.get('verbosity', LogLevel.INFO)
    verbosity_name = LogLevel(verbosity).name if verbosity in LogLevel.__members__.values() else str(verbosity)
    
    config_table.add_row("Verbosity Level", f"{verbosity_name} ({verbosity})")
    config_table.add_row("Log Format", logging_config.get('format', 'detailed'))
    config_table.add_row("File Logging", "Enabled" if logging_config.get('log_to_file', True) else "Disabled")
    config_table.add_row("Console Logging", "Enabled" if logging_config.get('log_to_console', True) else "Disabled")
    config_table.add_row("Log Directory", logging_config.get('log_dir', 'logs'))
    config_table.add_row("Max File Size", f"{logging_config.get('max_file_size', 10485760) / 1024 / 1024:.1f} MB")
    config_table.add_row("Backup Count", str(logging_config.get('backup_count', 5)))
    
    console.print(config_table)
    
    # Show log files
    show_log_files()
    
    # Show recent activity
    show_recent_activity()


@app.command()
def set_level(
    level: int = typer.Argument(..., help="Verbosity level (0=silent, 5=trace, 10=debug, 20=info, 30=warning, 40=error, 50=critical)")
):
    """Set logging verbosity level"""
    
    if level not in [0, 5, 10, 20, 30, 40, 50]:
        console.print("[red]❌ Invalid verbosity level. Use: 0, 5, 10, 20, 30, 40, or 50[/red]")
        return
    
    # Update configuration
    config = load_config()
    if 'logging' not in config:
        config['logging'] = {}
    
    config['logging']['verbosity'] = level
    save_config(config)
    
    # Apply immediately if logging is already setup
    try:
        set_verbosity(level)
        level_name = LogLevel(level).name if level in LogLevel.__members__.values() else str(level)
        console.print(f"[green]✅ Logging level set to: {level_name} ({level})[/green]")
    except:
        console.print(f"[yellow]⚠️ Level saved to config. Restart application to apply.[/yellow]")


@app.command()
def tail(
    file: str = typer.Option("cyoax.log", "--file", "-f", help="Log file to tail"),
    lines: int = typer.Option(50, "--lines", "-n", help="Number of lines to show"),
    follow: bool = typer.Option(False, "--follow", help="Follow log file for new entries")
):
    """Tail log files with real-time updates"""
    
    log_dir = Path("logs")
    log_file = log_dir / file
    
    if not log_file.exists():
        console.print(f"[red]❌ Log file not found: {log_file}[/red]")
        
        # Show available files
        available_files = list(log_dir.glob("*.log")) if log_dir.exists() else []
        if available_files:
            console.print("\n[cyan]Available log files:[/cyan]")
            for f in available_files:
                console.print(f"  • {f.name}")
        return
    
    console.print(Panel.fit(
        f"[bold green]📄 Tailing: {log_file}[/bold green]\n"
        f"[dim]Showing last {lines} lines{' (following)' if follow else ''}[/dim]",
        border_style="green"
    ))
    
    if follow:
        tail_follow(log_file, lines)
    else:
        tail_static(log_file, lines)


@app.command()
def analyze(
    hours: int = typer.Option(24, "--hours", "-h", help="Hours of logs to analyze"),
    level: Optional[str] = typer.Option(None, "--level", "-l", help="Filter by log level"),
    component: Optional[str] = typer.Option(None, "--component", "-c", help="Filter by component")
):
    """Analyze log patterns and statistics"""
    
    console.print(Panel.fit(
        f"[bold magenta]📈 Log Analysis[/bold magenta]\n"
        f"[dim]Analyzing last {hours} hours of logs[/dim]",
        border_style="magenta"
    ))
    
    log_dir = Path("logs")
    if not log_dir.exists():
        console.print("[red]❌ No logs directory found[/red]")
        return
    
    # Analyze logs
    analysis = analyze_logs(log_dir, hours, level, component)
    
    # Show statistics
    show_log_statistics(analysis)
    
    # Show patterns
    show_log_patterns(analysis)


@app.command()
def clean(
    days: int = typer.Option(7, "--days", "-d", help="Keep logs newer than N days"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be deleted without deleting")
):
    """Clean old log files"""
    
    console.print(Panel.fit(
        f"[bold red]🧹 Log Cleanup[/bold red]\n"
        f"[dim]{'Dry run: ' if dry_run else ''}Removing logs older than {days} days[/dim]",
        border_style="red"
    ))
    
    log_dir = Path("logs")
    if not log_dir.exists():
        console.print("[yellow]⚠️ No logs directory found[/yellow]")
        return
    
    cutoff_date = datetime.now() - timedelta(days=days)
    files_to_delete = []
    
    for log_file in log_dir.glob("*.log*"):
        if log_file.stat().st_mtime < cutoff_date.timestamp():
            files_to_delete.append(log_file)
    
    if not files_to_delete:
        console.print("[green]✅ No old log files found[/green]")
        return
    
    # Show files to be deleted
    console.print(f"\n[yellow]Files to {'be deleted' if not dry_run else 'delete'}:[/yellow]")
    total_size = 0
    
    for file in files_to_delete:
        size = file.stat().st_size
        total_size += size
        age_days = (datetime.now() - datetime.fromtimestamp(file.stat().st_mtime)).days
        console.print(f"  • {file.name} ({size / 1024:.1f} KB, {age_days} days old)")
    
    console.print(f"\n[cyan]Total: {len(files_to_delete)} files, {total_size / 1024 / 1024:.1f} MB[/cyan]")
    
    if dry_run:
        console.print("\n[blue]💡 This was a dry run. Use without --dry-run to actually delete files.[/blue]")
        return
    
    if not Confirm.ask(f"\nDelete {len(files_to_delete)} files?"):
        console.print("[yellow]Cancelled[/yellow]")
        return
    
    # Delete files
    deleted_count = 0
    for file in files_to_delete:
        try:
            file.unlink()
            deleted_count += 1
        except Exception as e:
            console.print(f"[red]❌ Failed to delete {file.name}: {e}[/red]")
    
    console.print(f"[green]✅ Deleted {deleted_count} files[/green]")


@app.command()
def config():
    """Configure logging settings interactively"""
    
    console.print(Panel.fit(
        "[bold yellow]⚙️ Logging Configuration[/bold yellow]\n"
        "[dim]Interactive logging configuration[/dim]",
        border_style="yellow"
    ))
    
    current_config = load_config()
    logging_config = current_config.setdefault('logging', {})
    
    console.print("\n[bold]Current Settings:[/bold]")
    
    # Show current settings
    for key, value in logging_config.items():
        console.print(f"  {key}: {value}")
    
    if not Confirm.ask("\nUpdate configuration?"):
        return
    
    # Interactive configuration
    console.print("\n[bold cyan]📝 Configuration Update[/bold cyan]")
    
    # Verbosity level
    current_verbosity = logging_config.get('verbosity', LogLevel.INFO)
    console.print(f"\nVerbosity levels:")
    console.print("  0 = Silent (no output)")
    console.print("  5 = Trace (everything)")
    console.print("  10 = Debug (detailed)")
    console.print("  20 = Info (normal)")
    console.print("  30 = Warning (warnings+)")
    console.print("  40 = Error (errors+)")
    console.print("  50 = Critical (critical only)")
    
    new_verbosity = IntPrompt.ask(
        f"Verbosity level",
        default=current_verbosity,
        choices=[0, 5, 10, 20, 30, 40, 50]
    )
    logging_config['verbosity'] = new_verbosity
    
    # Log format
    current_format = logging_config.get('format', 'detailed')
    formats = ['simple', 'detailed', 'json', 'rich']
    console.print(f"\nLog formats:")
    for fmt in formats:
        console.print(f"  {fmt}")
    
    new_format = Prompt.ask(
        "Log format",
        default=current_format,
        choices=formats
    )
    logging_config['format'] = new_format
    
    # File logging
    current_file_logging = logging_config.get('log_to_file', True)
    new_file_logging = Confirm.ask(
        "Enable file logging?",
        default=current_file_logging
    )
    logging_config['log_to_file'] = new_file_logging
    
    # Console logging
    current_console_logging = logging_config.get('log_to_console', True)
    new_console_logging = Confirm.ask(
        "Enable console logging?",
        default=current_console_logging
    )
    logging_config['log_to_console'] = new_console_logging
    
    # Save configuration
    save_config(current_config)
    
    console.print("\n[green]✅ Configuration saved![/green]")
    console.print("[blue]💡 Restart the application to apply all changes.[/blue]")


def show_log_files():
    """Show available log files"""
    log_dir = Path("logs")
    
    if not log_dir.exists():
        console.print("\n[yellow]⚠️ No logs directory found[/yellow]")
        return
    
    log_files = list(log_dir.glob("*.log"))
    
    if not log_files:
        console.print("\n[yellow]⚠️ No log files found[/yellow]")
        return
    
    console.print("\n[bold green]📁 Log Files[/bold green]")
    
    files_table = Table()
    files_table.add_column("File", style="cyan")
    files_table.add_column("Size", style="yellow")
    files_table.add_column("Modified", style="green")
    files_table.add_column("Lines", style="blue")
    
    for log_file in sorted(log_files):
        try:
            stat = log_file.stat()
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            
            # Count lines
            try:
                with open(log_file) as f:
                    line_count = sum(1 for _ in f)
            except:
                line_count = "?"
            
            files_table.add_row(
                log_file.name,
                f"{size / 1024:.1f} KB" if size < 1024 * 1024 else f"{size / 1024 / 1024:.1f} MB",
                modified.strftime("%Y-%m-%d %H:%M"),
                str(line_count)
            )
        except Exception as e:
            files_table.add_row(log_file.name, "Error", "Error", "Error")
    
    console.print(files_table)


def show_recent_activity():
    """Show recent log activity"""
    log_file = Path("logs/cyoax.log")
    
    if not log_file.exists():
        return
    
    console.print("\n[bold magenta]📊 Recent Activity (Last 10 entries)[/bold magenta]")
    
    try:
        with open(log_file) as f:
            lines = f.readlines()
        
        recent_lines = lines[-10:] if len(lines) >= 10 else lines
        
        for line in recent_lines:
            line = line.strip()
            if line:
                # Color code by level
                if "ERROR" in line:
                    console.print(f"[red]{line}[/red]")
                elif "WARNING" in line:
                    console.print(f"[yellow]{line}[/yellow]")
                elif "INFO" in line:
                    console.print(f"[green]{line}[/green]")
                elif "DEBUG" in line:
                    console.print(f"[blue]{line}[/blue]")
                else:
                    console.print(line)
    
    except Exception as e:
        console.print(f"[red]Error reading log file: {e}[/red]")


def tail_static(log_file: Path, lines: int):
    """Show last N lines of log file"""
    try:
        with open(log_file) as f:
            all_lines = f.readlines()
        
        recent_lines = all_lines[-lines:] if len(all_lines) >= lines else all_lines
        
        for line in recent_lines:
            line = line.strip()
            if line:
                console.print(line)
    
    except Exception as e:
        console.print(f"[red]Error reading log file: {e}[/red]")


def tail_follow(log_file: Path, lines: int):
    """Follow log file for new entries"""
    console.print("[dim]Press Ctrl+C to stop following...[/dim]\n")
    
    # Show initial lines
    tail_static(log_file, lines)
    
    # Follow new lines
    try:
        with open(log_file) as f:
            # Seek to end
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    console.print(line.strip())
                else:
                    time.sleep(0.1)
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Stopped following log file[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error following log file: {e}[/red]")


def analyze_logs(log_dir: Path, hours: int, level_filter: str, component_filter: str) -> dict:
    """Analyze log files and return statistics"""
    # This is a simplified analysis - in a real implementation,
    # you'd parse log entries and extract detailed statistics
    
    analysis = {
        'total_entries': 0,
        'by_level': {},
        'by_component': {},
        'by_hour': {},
        'errors': [],
        'patterns': []
    }
    
    # For now, return mock data
    # In a real implementation, parse log files here
    
    return analysis


def show_log_statistics(analysis: dict):
    """Show log analysis statistics"""
    console.print("\n[bold blue]📊 Statistics[/bold blue]")
    console.print("Log analysis would be displayed here")


def show_log_patterns(analysis: dict):
    """Show log patterns and insights"""
    console.print("\n[bold cyan]🔍 Patterns[/bold cyan]")
    console.print("Log patterns would be displayed here")


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def save_config(config: dict):
    """Save configuration"""
    config_path = Path("config.json")
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        console.print(f"[red]Error saving config: {e}[/red]")


if __name__ == "__main__":
    app()
