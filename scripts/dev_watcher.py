#!/usr/bin/env python3
"""
Development Watcher - Auto-reload and development utilities
Watches for file changes and provides development conveniences
"""

import sys
import time
import subprocess
from pathlib import Path
from typing import Set, Dict, Any

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    from rich.console import Console
    from rich.panel import Panel
    from rich.live import Live
    from rich.table import Table
    from rich.text import Text
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Development watcher requires additional dependencies.")
    print("Install with: pixi add watchdog rich typer")
    sys.exit(1)

app = typer.Typer(help="🔧 Development Tools")
console = Console()


class ProjectWatcher(FileSystemEventHandler):
    """Watches project files for changes"""
    
    def __init__(self, auto_test: bool = False, auto_format: bool = False):
        self.auto_test = auto_test
        self.auto_format = auto_format
        self.last_change = 0
        self.change_count = 0
        self.watched_extensions = {'.py', '.json', '.md', '.toml'}
        
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        # Only watch certain file types
        if file_path.suffix not in self.watched_extensions:
            return
        
        # Debounce rapid changes
        current_time = time.time()
        if current_time - self.last_change < 1:
            return
        
        self.last_change = current_time
        self.change_count += 1
        
        console.print(f"[yellow]📝 File changed: {file_path.name}[/yellow]")
        
        # Auto-format Python files
        if self.auto_format and file_path.suffix == '.py':
            self.format_file(file_path)
        
        # Auto-test on Python changes
        if self.auto_test and file_path.suffix == '.py':
            self.run_tests()
    
    def format_file(self, file_path: Path):
        """Format a Python file"""
        try:
            result = subprocess.run(['black', str(file_path)], capture_output=True, text=True)
            if result.returncode == 0:
                console.print(f"[green]✅ Formatted: {file_path.name}[/green]")
            else:
                console.print(f"[red]❌ Format failed: {file_path.name}[/red]")
        except FileNotFoundError:
            console.print("[yellow]⚠️  Black not found - install with: pip install black[/yellow]")
    
    def run_tests(self):
        """Run quick tests"""
        try:
            console.print("[blue]🧪 Running quick tests...[/blue]")
            result = subprocess.run(['python', '-m', 'pytest', 'tests/', '-x', '-q'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                console.print("[green]✅ Tests passed[/green]")
            else:
                console.print("[red]❌ Tests failed[/red]")
                if result.stdout:
                    console.print(f"[dim]{result.stdout[:200]}...[/dim]")
        except (FileNotFoundError, subprocess.TimeoutExpired):
            console.print("[yellow]⚠️  Test run failed or timed out[/yellow]")


@app.command()
def watch(
    auto_test: bool = typer.Option(False, "--test", "-t", help="Run tests on changes"),
    auto_format: bool = typer.Option(False, "--format", "-f", help="Auto-format Python files"),
    path: str = typer.Option(".", "--path", "-p", help="Path to watch")
):
    """Watch project files for changes"""
    
    console.print(Panel.fit(
        "[bold blue]🔧 Development Watcher[/bold blue]\n"
        f"[dim]Watching: {path}[/dim]\n"
        f"[dim]Auto-test: {'✅' if auto_test else '❌'}[/dim]\n"
        f"[dim]Auto-format: {'✅' if auto_format else '❌'}[/dim]",
        border_style="blue"
    ))
    
    # Set up watcher
    event_handler = ProjectWatcher(auto_test=auto_test, auto_format=auto_format)
    observer = Observer()
    observer.schedule(event_handler, path, recursive=True)
    
    # Start watching
    observer.start()
    console.print("[green]👀 Watching for changes... (Press Ctrl+C to stop)[/green]")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
        console.print("\n[yellow]Watcher stopped[/yellow]")
    
    observer.join()


@app.command()
def dev():
    """Start development environment"""
    
    console.print(Panel.fit(
        "[bold green]🚀 Development Environment[/bold green]\n"
        "[dim]Starting all development services[/dim]",
        border_style="green"
    ))
    
    # Services to start
    services = [
        ("File Watcher", ["python", "scripts/dev_watcher.py", "watch", "--format"]),
        ("Health Monitor", ["python", "scripts/health_check.py", "monitor", "--interval", "60"]),
    ]
    
    processes = []
    
    try:
        for service_name, command in services:
            console.print(f"[blue]Starting {service_name}...[/blue]")
            process = subprocess.Popen(command)
            processes.append((service_name, process))
            time.sleep(1)
        
        console.print("[green]✅ All services started[/green]")
        console.print("[dim]Press Ctrl+C to stop all services[/dim]")
        
        # Wait for interrupt
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Stopping all services...[/yellow]")
        
        for service_name, process in processes:
            console.print(f"[dim]Stopping {service_name}...[/dim]")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        
        console.print("[green]✅ All services stopped[/green]")


@app.command()
def lint():
    """Run code quality checks"""
    
    console.print(Panel.fit("[bold]🔍 Code Quality Check[/bold]", border_style="yellow"))
    
    checks = [
        ("Black (formatting)", ["black", "--check", "src/", "scripts/"]),
        ("Flake8 (linting)", ["flake8", "src/", "scripts/"]),
        ("MyPy (type checking)", ["mypy", "src/"]),
    ]
    
    results = {}
    
    for check_name, command in checks:
        console.print(f"[blue]Running {check_name}...[/blue]")
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                console.print(f"[green]✅ {check_name} passed[/green]")
                results[check_name] = True
            else:
                console.print(f"[red]❌ {check_name} failed[/red]")
                if result.stdout:
                    console.print(f"[dim]{result.stdout[:300]}...[/dim]")
                results[check_name] = False
                
        except (FileNotFoundError, subprocess.TimeoutExpired):
            console.print(f"[yellow]⚠️  {check_name} not available or timed out[/yellow]")
            results[check_name] = None
    
    # Summary
    passed = sum(1 for result in results.values() if result is True)
    total = len([r for r in results.values() if r is not None])
    
    if total > 0:
        console.print(f"\n[bold]Summary: {passed}/{total} checks passed[/bold]")
    else:
        console.print("\n[yellow]No checks could be run - install development dependencies[/yellow]")


@app.command()
def format():
    """Format all code"""
    
    console.print(Panel.fit("[bold]🎨 Code Formatting[/bold]", border_style="cyan"))
    
    # Format Python files
    console.print("[blue]Formatting Python files...[/blue]")
    try:
        result = subprocess.run(['black', 'src/', 'scripts/'], capture_output=True, text=True)
        if result.returncode == 0:
            console.print("[green]✅ Python files formatted[/green]")
        else:
            console.print("[red]❌ Python formatting failed[/red]")
            console.print(result.stderr)
    except FileNotFoundError:
        console.print("[yellow]⚠️  Black not found - install with: pip install black[/yellow]")
    
    # Sort imports
    console.print("[blue]Sorting imports...[/blue]")
    try:
        result = subprocess.run(['isort', 'src/', 'scripts/'], capture_output=True, text=True)
        if result.returncode == 0:
            console.print("[green]✅ Imports sorted[/green]")
        else:
            console.print("[red]❌ Import sorting failed[/red]")
    except FileNotFoundError:
        console.print("[yellow]⚠️  isort not found - install with: pip install isort[/yellow]")


@app.command()
def test():
    """Run comprehensive tests"""
    
    console.print(Panel.fit("[bold]🧪 Test Suite[/bold]", border_style="green"))
    
    test_commands = [
        ("Unit Tests", ["python", "-m", "pytest", "tests/", "-v"]),
        ("Story Generation", ["python", "scripts/test_generation.py"]),
        ("Character System", ["python", "scripts/test_character_system.py"]),
        ("Complete System", ["python", "scripts/test_complete_system.py"]),
    ]
    
    results = {}
    
    for test_name, command in test_commands:
        console.print(f"[blue]Running {test_name}...[/blue]")
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                console.print(f"[green]✅ {test_name} passed[/green]")
                results[test_name] = True
            else:
                console.print(f"[red]❌ {test_name} failed[/red]")
                # Show last few lines of output
                if result.stdout:
                    lines = result.stdout.split('\n')[-5:]
                    for line in lines:
                        if line.strip():
                            console.print(f"[dim]  {line}[/dim]")
                results[test_name] = False
                
        except subprocess.TimeoutExpired:
            console.print(f"[yellow]⚠️  {test_name} timed out[/yellow]")
            results[test_name] = None
        except FileNotFoundError:
            console.print(f"[yellow]⚠️  {test_name} not found[/yellow]")
            results[test_name] = None
    
    # Summary
    passed = sum(1 for result in results.values() if result is True)
    failed = sum(1 for result in results.values() if result is False)
    skipped = sum(1 for result in results.values() if result is None)
    
    console.print(f"\n[bold]Test Summary:[/bold]")
    console.print(f"  [green]✅ Passed: {passed}[/green]")
    console.print(f"  [red]❌ Failed: {failed}[/red]")
    console.print(f"  [yellow]⚠️  Skipped: {skipped}[/yellow]")


@app.command()
def clean():
    """Clean up development artifacts"""
    
    console.print(Panel.fit("[bold]🧹 Cleanup[/bold]", border_style="red"))
    
    # Patterns to clean
    patterns = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo",
        "**/.pytest_cache",
        "**/temp/*",
        "**/.mypy_cache",
        "**/*.log",
    ]
    
    import glob
    import shutil
    
    cleaned_count = 0
    
    for pattern in patterns:
        matches = list(Path(".").glob(pattern))
        for match in matches:
            try:
                if match.is_file():
                    match.unlink()
                    cleaned_count += 1
                elif match.is_dir():
                    shutil.rmtree(match)
                    cleaned_count += 1
                console.print(f"[dim]Removed: {match}[/dim]")
            except Exception as e:
                console.print(f"[yellow]Could not remove {match}: {e}[/yellow]")
    
    console.print(f"[green]✅ Cleaned {cleaned_count} items[/green]")


if __name__ == "__main__":
    app()
