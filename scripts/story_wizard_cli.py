#!/usr/bin/env python3
"""
CLI Story Creation Wizard - Beautiful command-line interface for story creation
Uses Rich for beautiful output and Typer for CLI interface
"""

import sys
import json
from pathlib import Path
from typing import Optional, List

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text
from rich.layout import Layout
from rich.live import Live
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from story.story_generator import StoryGenerator
    from story.story_templates import StoryTemplateManager
    from utils.lmstudio_client import LMStudioClient
    from utils.system_health import SystemHealthChecker
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run wizard")
    sys.exit(1)

app = typer.Typer(help="🎭 CYOA Story Creation Wizard")
console = Console()


@app.command()
def create(
    title: Optional[str] = typer.Option(None, "--title", "-t", help="Story title"),
    player_class: Optional[str] = typer.Option("Ranger", "--class", "-c", help="Player class (Mage/Ranger/Charmer)"),
    template: Optional[str] = typer.Option(None, "--template", help="Use story template"),
    quick: bool = typer.Option(False, "--quick", "-q", help="Quick mode with defaults"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path")
):
    """Create a new interactive story"""
    
    # Welcome banner
    console.print(Panel.fit(
        "[bold blue]🎭 CYOA Story Creation Wizard[/bold blue]\n"
        "[dim]Create engaging Choose Your Own Adventure stories[/dim]",
        border_style="blue"
    ))
    
    # System health check
    if not quick:
        console.print("\n[yellow]🔍 Checking system health...[/yellow]")
        health_ok = check_system_health()
        if not health_ok:
            console.print("[red]❌ System health issues detected. Run 'pixi run health' for details.[/red]")
            if not Confirm.ask("Continue anyway?"):
                raise typer.Exit(1)
    
    # Load configuration
    config = load_config()
    
    # Story creation flow
    if template:
        story = create_from_template(config, template, title, player_class)
    elif quick:
        story = quick_create(config, title, player_class)
    else:
        story = interactive_create(config, title, player_class)
    
    if story:
        # Save story
        output_path = save_story(story, output)
        console.print(f"\n[green]✅ Story created successfully![/green]")
        console.print(f"[dim]Saved to: {output_path}[/dim]")
        
        # Show story stats
        show_story_stats(story)
        
        # Next steps
        show_next_steps(output_path)
    else:
        console.print("[red]❌ Story creation failed[/red]")
        raise typer.Exit(1)


@app.command()
def templates():
    """List available story templates"""
    console.print(Panel.fit("[bold]📚 Available Story Templates[/bold]", border_style="green"))
    
    config = load_config()
    template_manager = StoryTemplateManager(config)
    templates = template_manager.list_templates()
    
    if not templates:
        console.print("[yellow]No templates available[/yellow]")
        return
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("ID", style="cyan")
    table.add_column("Name", style="green")
    table.add_column("Genre", style="blue")
    table.add_column("Difficulty", style="yellow")
    table.add_column("Time", style="dim")
    table.add_column("Description", style="white")
    
    for template in templates:
        table.add_row(
            template['id'],
            template['name'],
            template['genre'],
            template['difficulty'],
            template['estimated_time'],
            template['description'][:50] + "..." if len(template['description']) > 50 else template['description']
        )
    
    console.print(table)
    console.print(f"\n[dim]Use: pixi run wizard create --template <ID>[/dim]")


@app.command()
def health():
    """Check system health"""
    console.print(Panel.fit("[bold]🏥 System Health Check[/bold]", border_style="blue"))
    
    config = load_config()
    health_checker = SystemHealthChecker(config)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Running health checks...", total=None)
        checks = health_checker.run_all_checks()
        progress.update(task, completed=True)
    
    # Display results
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="white")
    table.add_column("Message", style="white")
    
    for name, check in checks.items():
        status_style = "green" if check.status.value == "healthy" else "yellow" if check.status.value == "warning" else "red"
        status_icon = "✅" if check.status.value == "healthy" else "⚠️" if check.status.value == "warning" else "❌"
        
        table.add_row(
            name,
            f"[{status_style}]{status_icon} {check.status.value.upper()}[/{status_style}]",
            check.message
        )
    
    console.print(table)
    
    # Overall status
    overall_status = health_checker.get_overall_status()
    status_color = "green" if overall_status.value == "healthy" else "yellow" if overall_status.value == "warning" else "red"
    console.print(f"\n[{status_color}]Overall Status: {overall_status.value.upper()}[/{status_color}]")
    
    # Recommendations
    summary = health_checker.get_health_summary()
    if summary['recommendations']:
        console.print("\n[bold]📋 Recommendations:[/bold]")
        for rec in summary['recommendations'][:5]:
            console.print(f"  • {rec}")


def check_system_health() -> bool:
    """Quick system health check"""
    try:
        config = load_config()
        health_checker = SystemHealthChecker(config)
        checks = health_checker.run_all_checks()
        overall_status = health_checker.get_overall_status()
        
        if overall_status.value == "error":
            return False
        
        console.print("[green]✅ System health OK[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Health check failed: {e}[/red]")
        return False


def interactive_create(config: dict, title: Optional[str], player_class: str):
    """Interactive story creation"""
    console.print("\n[bold cyan]📝 Story Information[/bold cyan]")
    
    # Get story details
    if not title:
        title = Prompt.ask("Story title", default="My Adventure Story")
    
    description = Prompt.ask("Story description (optional)", default="")
    
    genre = Prompt.ask(
        "Genre",
        choices=["Fantasy", "Sci-Fi", "Mystery", "Romance", "Horror", "Modern", "Historical"],
        default="Fantasy"
    )
    
    if not player_class:
        player_class = Prompt.ask(
            "Player class",
            choices=["Mage", "Ranger", "Charmer"],
            default="Ranger"
        )
    
    # Source content
    console.print("\n[bold cyan]📖 Source Content[/bold cyan]")
    source_type = Prompt.ask(
        "How would you like to create your story?",
        choices=["text", "theme", "template"],
        default="text"
    )
    
    if source_type == "text":
        console.print("Enter your source text (press Ctrl+D when done):")
        source_text = ""
        try:
            while True:
                line = input()
                source_text += line + "\n"
        except EOFError:
            pass
        
        if not source_text.strip():
            console.print("[yellow]No text provided, using sample story[/yellow]")
            source_text = get_sample_story()
    
    elif source_type == "theme":
        theme = Prompt.ask("Enter story theme or prompt")
        source_text = f"Theme: {theme}"
    
    else:  # template
        return create_from_template_interactive(config, title, player_class)
    
    # Generate story
    return generate_story_with_progress(config, source_text, title, player_class, genre)


def quick_create(config: dict, title: Optional[str], player_class: str):
    """Quick story creation with defaults"""
    title = title or "Quick Adventure"
    source_text = get_sample_story()
    
    console.print(f"[yellow]🚀 Quick creating story: {title}[/yellow]")
    return generate_story_with_progress(config, source_text, title, player_class, "Fantasy")


def create_from_template(config: dict, template_id: str, title: Optional[str], player_class: str):
    """Create story from template"""
    template_manager = StoryTemplateManager(config)
    template = template_manager.get_template(template_id)
    
    if not template:
        console.print(f"[red]❌ Template '{template_id}' not found[/red]")
        console.print("Available templates:")
        templates = template_manager.list_templates()
        for t in templates:
            console.print(f"  • {t['id']}: {t['name']}")
        return None
    
    title = title or f"{template.name} Adventure"
    
    console.print(f"[green]📚 Creating story from template: {template.name}[/green]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Creating story from template...", total=None)
        story = template_manager.create_story_from_template(template_id, title, player_class)
        progress.update(task, completed=True)
    
    return story


def create_from_template_interactive(config: dict, title: str, player_class: str):
    """Interactive template selection"""
    template_manager = StoryTemplateManager(config)
    templates = template_manager.list_templates()
    
    if not templates:
        console.print("[yellow]No templates available, falling back to text input[/yellow]")
        return None
    
    console.print("\n[bold]Available Templates:[/bold]")
    for i, template in enumerate(templates):
        console.print(f"  {i+1}. {template['name']} ({template['genre']}) - {template['difficulty']}")
    
    choice = Prompt.ask("Select template", choices=[str(i+1) for i in range(len(templates))])
    template_id = templates[int(choice)-1]['id']
    
    return create_from_template(config, template_id, title, player_class)


def generate_story_with_progress(config: dict, source_text: str, title: str, player_class: str, genre: str):
    """Generate story with progress display"""
    console.print(f"\n[yellow]🎭 Generating story: {title}[/yellow]")
    
    try:
        lmstudio_client = LMStudioClient(config)
        if not lmstudio_client.is_available():
            console.print("[red]❌ LM Studio not available. Please start LM Studio first.[/red]")
            console.print("[dim]Run: pixi run start-lmstudio[/dim]")
            return None
        
        story_generator = StoryGenerator(config, lmstudio_client)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Analyzing source content...", total=None)
            
            # Simulate progress steps
            progress.update(task, description="Extracting characters...")
            time.sleep(1)
            
            progress.update(task, description="Creating story structure...")
            time.sleep(1)
            
            progress.update(task, description="Generating story nodes...")
            story = story_generator.import_storyline_from_text(source_text, title, player_class)
            
            progress.update(task, description="Validating story...", completed=True)
        
        if story:
            story.metadata.update({
                'genre': genre,
                'cli_generated': True
            })
        
        return story
        
    except Exception as e:
        console.print(f"[red]❌ Story generation failed: {e}[/red]")
        return None


def save_story(story, output_path: Optional[str]) -> str:
    """Save story to file"""
    if not output_path:
        # Generate filename from title
        safe_title = "".join(c for c in story.metadata.get('title', 'story') if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_').lower()
        output_path = f"data/storylines/{safe_title}.json"
    
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(story.to_dict(), f, indent=2)
    
    return str(output_file)


def show_story_stats(story):
    """Display story statistics"""
    console.print("\n[bold cyan]📊 Story Statistics[/bold cyan]")
    
    table = Table(show_header=False, box=None)
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("📄 Total Nodes", str(len(story.nodes)))
    table.add_row("🚪 Entry Points", str(len(story.entry_points)))
    table.add_row("🏁 Endings", str(len(story.endings)))
    
    if story.character_manager:
        table.add_row("🎭 Characters", str(len(story.character_manager.characters)))
    
    # Count node types
    story_nodes = sum(1 for node in story.nodes.values() if not node.is_entry and not node.is_ending)
    table.add_row("📖 Story Nodes", str(story_nodes))
    
    console.print(table)


def show_next_steps(output_path: str):
    """Show next steps to user"""
    console.print("\n[bold green]🚀 Next Steps[/bold green]")
    
    steps = [
        "Edit your story: python main.py",
        "View story structure: pixi run validate",
        "Generate media: pixi run media",
        "Post to X: pixi run post",
        "Run full test: pixi run test-all"
    ]
    
    for i, step in enumerate(steps, 1):
        console.print(f"  {i}. {step}")


def get_sample_story() -> str:
    """Get sample story text"""
    return """
    Once upon a time, there was a brave knight named Sir Galahad who lived in the kingdom of Camelot.
    He was known throughout the land for his pure heart and unwavering courage.
    
    One dark morning, an evil dragon named Shadowfang descended upon the kingdom, breathing fire
    and terrorizing the villagers. The wise wizard Merlin approached Sir Galahad with urgent news.
    
    "The dragon can only be defeated with the Crystal of Light," Merlin explained, "but it lies
    deep within the Forbidden Forest, guarded by ancient magic."
    
    Sir Galahad had to make a choice: venture into the dangerous forest alone, seek help from
    other knights, or find another way to defeat the dragon.
    """


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


if __name__ == "__main__":
    app()
