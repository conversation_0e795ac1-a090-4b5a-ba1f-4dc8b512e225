#!/usr/bin/env python3
"""
Setup script for CYOA Automation System
Performs initial setup and validation
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10+ required")
        return False
    print("✅ Python version OK")
    return True

def check_directories():
    """Create necessary directories"""
    directories = [
        "data/storylines",
        "videos", 
        "logs",
        "workflows"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created")
    return True

def check_config():
    """Check configuration file"""
    config_path = Path("config.json")
    if not config_path.exists():
        print("❌ config.json not found")
        return False
    
    try:
        with open(config_path) as f:
            config = json.load(f)
        print("✅ Configuration file OK")
        return True
    except json.JSONDecodeError:
        print("❌ Invalid JSON in config.json")
        return False

def check_env_file():
    """Check environment file"""
    env_path = Path(".env")
    if not env_path.exists():
        print("⚠️  .env file not found, copying from .env.example")
        example_path = Path(".env.example")
        if example_path.exists():
            import shutil
            shutil.copy(example_path, env_path)
            print("✅ .env file created from example")
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file exists")
    return True

def check_ollama():
    """Check if Ollama is available"""
    try:
        result = subprocess.run(
            ["ollama", "list"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        if result.returncode == 0:
            print("✅ Ollama is available")
            
            # Check for llama3.1 model
            if "llama3.1" in result.stdout:
                print("✅ llama3.1 model found")
            else:
                print("⚠️  llama3.1 model not found, run: ollama pull llama3.1")
            return True
        else:
            print("❌ Ollama not responding")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Ollama not installed or not in PATH")
        return False

def check_comfyui():
    """Check if ComfyUI is available"""
    comfyui_path = Path("ComfyUI")
    if comfyui_path.exists():
        print("✅ ComfyUI directory found")
        return True
    else:
        print("⚠️  ComfyUI not found, install with:")
        print("   git clone https://github.com/comfyanonymous/ComfyUI.git")
        return False

def main():
    """Main setup function"""
    print("🚀 CYOA Automation System Setup")
    print("=" * 40)
    
    checks = [
        ("Python Version", check_python_version),
        ("Directories", check_directories),
        ("Configuration", check_config),
        ("Environment", check_env_file),
        ("Ollama", check_ollama),
        ("ComfyUI", check_comfyui),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\nChecking {name}...")
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 40)
    print("Setup Summary:")
    
    all_passed = True
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env with your X API credentials")
        print("2. Start Ollama: ollama serve")
        print("3. Start ComfyUI (optional): python ComfyUI/main.py")
        print("4. Run application: pixi run run")
    else:
        print("\n⚠️  Setup completed with issues")
        print("Please resolve the failed checks before running the application")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
