#!/usr/bin/env python3
"""
Generate Media Script - Create videos and audio for stories
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from story.story_web import <PERSON><PERSON>eb
    from media.video_generator import VideoGenerator
    from media.audio_generator import AudioGenerator
    from media.comfyui_client import ComfyUIClient
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
    from rich.panel import Panel
    import typer
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

if RICH_AVAILABLE:
    app = typer.Typer(help="🎬 Media Generation Tool")
    console = Console()
else:
    class SimpleApp:
        def command(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    app = SimpleApp()


@app.command()
def video(
    story_path: str = typer.Argument(..., help="Path to story file"),
    output_dir: str = typer.Option("videos", "--output", "-o", help="Output directory"),
    quality: str = typer.Option("720p", "--quality", "-q", help="Video quality (720p, 1080p)"),
    node_id: str = typer.Option(None, "--node", "-n", help="Generate for specific node only")
):
    """Generate videos for story nodes"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold blue]🎬 Generating Videos[/bold blue]\n"
            f"[dim]Story: {story_path}[/dim]",
            border_style="blue"
        ))
    else:
        print(f"🎬 Generating Videos: {story_path}")
    
    # Load story
    story = load_story(story_path)
    if not story:
        return
    
    # Load config
    config = load_config()
    
    try:
        # Initialize video generator
        comfyui_client = ComfyUIClient(config)
        video_generator = VideoGenerator(config, comfyui_client)
        
        # Check ComfyUI availability
        if not comfyui_client.is_available():
            if RICH_AVAILABLE:
                console.print("[yellow]⚠️  ComfyUI not available, using fallback generation[/yellow]")
            else:
                print("⚠️  ComfyUI not available, using fallback generation")
        
        # Determine nodes to process
        if node_id:
            if node_id not in story.nodes:
                if RICH_AVAILABLE:
                    console.print(f"[red]❌ Node not found: {node_id}[/red]")
                else:
                    print(f"❌ Node not found: {node_id}")
                return
            nodes_to_process = [story.nodes[node_id]]
        else:
            nodes_to_process = list(story.nodes.values())
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate videos
        if RICH_AVAILABLE:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console
            ) as progress:
                task = progress.add_task("Generating videos...", total=len(nodes_to_process))
                
                for node in nodes_to_process:
                    progress.update(task, description=f"Processing node: {node.id}")
                    
                    try:
                        video_path = video_generator.generate_video_for_node(
                            node, str(output_path), quality
                        )
                        
                        if video_path:
                            console.print(f"[green]✅ Generated: {video_path}[/green]")
                        else:
                            console.print(f"[yellow]⚠️  Skipped: {node.id}[/yellow]")
                    
                    except Exception as e:
                        console.print(f"[red]❌ Error generating video for {node.id}: {e}[/red]")
                    
                    progress.advance(task)
        else:
            for i, node in enumerate(nodes_to_process):
                print(f"[{i+1}/{len(nodes_to_process)}] Processing node: {node.id}")
                
                try:
                    video_path = video_generator.generate_video_for_node(
                        node, str(output_path), quality
                    )
                    
                    if video_path:
                        print(f"✅ Generated: {video_path}")
                    else:
                        print(f"⚠️  Skipped: {node.id}")
                
                except Exception as e:
                    print(f"❌ Error generating video for {node.id}: {e}")
        
        if RICH_AVAILABLE:
            console.print(f"\n[green]🎉 Video generation complete![/green]")
            console.print(f"[dim]Output directory: {output_path}[/dim]")
        else:
            print(f"\n🎉 Video generation complete!")
            print(f"Output directory: {output_path}")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Video generation failed: {e}[/red]")
        else:
            print(f"❌ Video generation failed: {e}")


@app.command()
def audio(
    story_path: str = typer.Argument(..., help="Path to story file"),
    output_dir: str = typer.Option("audio", "--output", "-o", help="Output directory"),
    voice: str = typer.Option("default", "--voice", "-v", help="Voice to use"),
    node_id: str = typer.Option(None, "--node", "-n", help="Generate for specific node only")
):
    """Generate audio for story nodes"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold green]🎵 Generating Audio[/bold green]\n"
            f"[dim]Story: {story_path}[/dim]",
            border_style="green"
        ))
    else:
        print(f"🎵 Generating Audio: {story_path}")
    
    # Load story
    story = load_story(story_path)
    if not story:
        return
    
    # Load config
    config = load_config()
    
    try:
        # Initialize audio generator
        audio_generator = AudioGenerator(config)
        
        # Determine nodes to process
        if node_id:
            if node_id not in story.nodes:
                if RICH_AVAILABLE:
                    console.print(f"[red]❌ Node not found: {node_id}[/red]")
                else:
                    print(f"❌ Node not found: {node_id}")
                return
            nodes_to_process = [story.nodes[node_id]]
        else:
            nodes_to_process = list(story.nodes.values())
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate audio
        if RICH_AVAILABLE:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console
            ) as progress:
                task = progress.add_task("Generating audio...", total=len(nodes_to_process))
                
                for node in nodes_to_process:
                    progress.update(task, description=f"Processing node: {node.id}")
                    
                    try:
                        audio_path = audio_generator.generate_audio_for_node(
                            node, str(output_path), voice
                        )
                        
                        if audio_path:
                            console.print(f"[green]✅ Generated: {audio_path}[/green]")
                        else:
                            console.print(f"[yellow]⚠️  Skipped: {node.id}[/yellow]")
                    
                    except Exception as e:
                        console.print(f"[red]❌ Error generating audio for {node.id}: {e}[/red]")
                    
                    progress.advance(task)
        else:
            for i, node in enumerate(nodes_to_process):
                print(f"[{i+1}/{len(nodes_to_process)}] Processing node: {node.id}")
                
                try:
                    audio_path = audio_generator.generate_audio_for_node(
                        node, str(output_path), voice
                    )
                    
                    if audio_path:
                        print(f"✅ Generated: {audio_path}")
                    else:
                        print(f"⚠️  Skipped: {node.id}")
                
                except Exception as e:
                    print(f"❌ Error generating audio for {node.id}: {e}")
        
        if RICH_AVAILABLE:
            console.print(f"\n[green]🎉 Audio generation complete![/green]")
            console.print(f"[dim]Output directory: {output_path}[/dim]")
        else:
            print(f"\n🎉 Audio generation complete!")
            print(f"Output directory: {output_path}")
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Audio generation failed: {e}[/red]")
        else:
            print(f"❌ Audio generation failed: {e}")


@app.command()
def both(
    story_path: str = typer.Argument(..., help="Path to story file"),
    output_dir: str = typer.Option("media", "--output", "-o", help="Output directory"),
    quality: str = typer.Option("720p", "--quality", "-q", help="Video quality"),
    voice: str = typer.Option("default", "--voice", "-v", help="Voice to use"),
    node_id: str = typer.Option(None, "--node", "-n", help="Generate for specific node only")
):
    """Generate both video and audio for story nodes"""
    
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            f"[bold magenta]🎬🎵 Generating Media[/bold magenta]\n"
            f"[dim]Story: {story_path}[/dim]",
            border_style="magenta"
        ))
    else:
        print(f"🎬🎵 Generating Media: {story_path}")
    
    # Create subdirectories
    video_dir = Path(output_dir) / "videos"
    audio_dir = Path(output_dir) / "audio"
    
    # Generate videos
    if RICH_AVAILABLE:
        console.print("[blue]Generating videos...[/blue]")
    else:
        print("Generating videos...")
    
    video(story_path, str(video_dir), quality, node_id)
    
    # Generate audio
    if RICH_AVAILABLE:
        console.print("\n[green]Generating audio...[/green]")
    else:
        print("\nGenerating audio...")
    
    audio(story_path, str(audio_dir), voice, node_id)
    
    if RICH_AVAILABLE:
        console.print(f"\n[bold green]🎉 Complete media generation finished![/bold green]")
        console.print(f"[dim]Videos: {video_dir}[/dim]")
        console.print(f"[dim]Audio: {audio_dir}[/dim]")
    else:
        print(f"\n🎉 Complete media generation finished!")
        print(f"Videos: {video_dir}")
        print(f"Audio: {audio_dir}")


def load_story(story_path: str):
    """Load story from file"""
    story_file = Path(story_path)
    if not story_file.exists():
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Story file not found: {story_path}[/red]")
        else:
            print(f"❌ Story file not found: {story_path}")
        return None
    
    try:
        story = StoryWeb.load_from_file(story_path)
        if not story:
            if RICH_AVAILABLE:
                console.print("[red]❌ Failed to load story[/red]")
            else:
                print("❌ Failed to load story")
            return None
        
        if RICH_AVAILABLE:
            console.print(f"[green]✅ Loaded story with {len(story.nodes)} nodes[/green]")
        else:
            print(f"✅ Loaded story with {len(story.nodes)} nodes")
        
        return story
    
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Error loading story: {e}[/red]")
        else:
            print(f"❌ Error loading story: {e}")
        return None


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


def main():
    """Main function for non-typer usage"""
    if len(sys.argv) < 3:
        print("Usage: python generate_media.py <command> <story_file>")
        print("Commands: video, audio, both")
        print("   or: pixi run media <command> <story_file>")
        sys.exit(1)
    
    command = sys.argv[1]
    story_path = sys.argv[2]
    
    if command == "video":
        video(story_path)
    elif command == "audio":
        audio(story_path)
    elif command == "both":
        both(story_path)
    else:
        print(f"Unknown command: {command}")


if __name__ == "__main__":
    if RICH_AVAILABLE:
        app()
    else:
        main()
