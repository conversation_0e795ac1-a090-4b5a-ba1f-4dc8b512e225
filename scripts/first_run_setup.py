#!/usr/bin/env python3
"""
First Run Setup - Automated setup for new users
Checks dependencies, downloads models, creates directories, and guides initial configuration
"""

import sys
import json
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Tuple

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
    from rich.table import Table
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Rich not available, using basic output")

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

if RICH_AVAILABLE:
    console = Console()
else:
    class SimpleConsole:
        def print(self, *args, **kwargs):
            print(*args)
    console = SimpleConsole()


def main():
    """Main setup function"""
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold blue]🚀 CYOA Automation System - First Run Setup[/bold blue]\n"
            "[dim]Setting up your Choose Your Own Adventure automation system[/dim]",
            border_style="blue"
        ))
    else:
        print("🚀 CYOA Automation System - First Run Setup")
        print("Setting up your Choose Your Own Adventure automation system")
    
    # Setup steps
    steps = [
        ("Checking system requirements", check_system_requirements),
        ("Creating directories", create_directories),
        ("Checking Ollama installation", check_ollama),
        ("Downloading AI models", download_models),
        ("Setting up configuration", setup_configuration),
        ("Creating example content", create_examples),
        ("Running system validation", validate_setup),
    ]
    
    if RICH_AVAILABLE:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            main_task = progress.add_task("Setting up system...", total=len(steps))
            
            for i, (description, func) in enumerate(steps):
                progress.update(main_task, description=description)
                success = func()
                
                if not success:
                    console.print(f"[red]❌ {description} failed[/red]")
                    if not Confirm.ask("Continue anyway?"):
                        return False
                
                progress.advance(main_task)
                time.sleep(0.5)  # Visual feedback
    else:
        for i, (description, func) in enumerate(steps):
            print(f"[{i+1}/{len(steps)}] {description}...")
            success = func()
            if not success:
                print(f"❌ {description} failed")
                response = input("Continue anyway? (y/n): ")
                if response.lower() != 'y':
                    return False
    
    # Setup complete
    show_completion_message()
    return True


def check_system_requirements() -> bool:
    """Check system requirements"""
    try:
        # Check Python version
        import sys
        if sys.version_info < (3, 10):
            console.print("[red]❌ Python 3.10+ required[/red]")
            return False
        
        # Check available disk space
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**3)  # GB
        if free_space < 5:
            console.print(f"[yellow]⚠️  Low disk space: {free_space:.1f}GB available[/yellow]")
        
        # Check memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            if memory_gb < 8:
                console.print(f"[yellow]⚠️  Low memory: {memory_gb:.1f}GB available[/yellow]")
        except ImportError:
            pass
        
        console.print("[green]✅ System requirements OK[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ System check failed: {e}[/red]")
        return False


def create_directories() -> bool:
    """Create necessary directories"""
    try:
        directories = [
            'data',
            'data/storylines',
            'data/characters',
            'data/templates',
            'logs',
            'videos',
            'audio',
            'backups',
            'workflows',
            'temp'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        console.print("[green]✅ Directories created[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Directory creation failed: {e}[/red]")
        return False


def check_ollama() -> bool:
    """Check Ollama installation and setup"""
    try:
        # Check if Ollama is installed
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode != 0:
            console.print("[yellow]⚠️  Ollama not found[/yellow]")
            console.print("Please install Ollama from: https://ollama.com")
            
            if RICH_AVAILABLE and Confirm.ask("Open Ollama website?"):
                import webbrowser
                webbrowser.open("https://ollama.com")
            
            return False
        
        # Check if Ollama is running
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                console.print("[green]✅ Ollama is running[/green]")
                return True
            else:
                console.print("[yellow]⚠️  Ollama not responding[/yellow]")
                console.print("Please start Ollama with: ollama serve")
                return False
        except requests.RequestException:
            console.print("[yellow]⚠️  Ollama not running[/yellow]")
            console.print("Please start Ollama with: ollama serve")
            return False
        
    except Exception as e:
        console.print(f"[red]❌ Ollama check failed: {e}[/red]")
        return False


def download_models() -> bool:
    """Download required AI models"""
    try:
        # Check if models are already available
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        
        if response.status_code != 200:
            console.print("[yellow]⚠️  Cannot check models - Ollama not running[/yellow]")
            return False
        
        models = response.json().get('models', [])
        model_names = [model['name'] for model in models]
        
        # Required models
        required_models = ['llama3.1', 'llama3.1:8b']
        available_model = None
        
        for model in required_models:
            if any(model in name for name in model_names):
                available_model = model
                break
        
        if available_model:
            console.print(f"[green]✅ AI model available: {available_model}[/green]")
            return True
        
        # Download model
        console.print("[yellow]📥 Downloading AI model (this may take a while)...[/yellow]")
        
        if RICH_AVAILABLE and not Confirm.ask("Download llama3.1:8b model (~4.7GB)?"):
            console.print("[yellow]⚠️  Skipping model download[/yellow]")
            return False
        
        # Download model
        result = subprocess.run(['ollama', 'pull', 'llama3.1:8b'], capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("[green]✅ AI model downloaded[/green]")
            return True
        else:
            console.print(f"[red]❌ Model download failed: {result.stderr}[/red]")
            return False
        
    except Exception as e:
        console.print(f"[red]❌ Model download failed: {e}[/red]")
        return False


def setup_configuration() -> bool:
    """Setup configuration files"""
    try:
        config_path = Path("config.json")
        env_path = Path(".env")
        
        # Check if config exists
        if config_path.exists():
            console.print("[green]✅ Configuration file exists[/green]")
        else:
            console.print("[yellow]⚠️  No configuration file found[/yellow]")
            return False
        
        # Setup .env file
        if not env_path.exists():
            console.print("[yellow]📝 Creating .env file...[/yellow]")
            
            env_content = """# X (Twitter) API Credentials
# Get these from https://developer.twitter.com
X_API_KEY=your_api_key_here
X_API_SECRET=your_api_secret_here
X_ACCESS_TOKEN=your_access_token_here
X_ACCESS_TOKEN_SECRET=your_access_token_secret_here
X_BEARER_TOKEN=your_bearer_token_here

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b

# ComfyUI Configuration
COMFYUI_BASE_URL=http://127.0.0.1:8188

# Optional: OpenAI API (fallback)
# OPENAI_API_KEY=your_openai_key_here
"""
            
            with open(env_path, 'w') as f:
                f.write(env_content)
            
            console.print("[green]✅ .env file created[/green]")
            console.print("[dim]Edit .env file to add your API credentials[/dim]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Configuration setup failed: {e}[/red]")
        return False


def create_examples() -> bool:
    """Create example content"""
    try:
        # Create example story
        example_story = {
            "metadata": {
                "title": "The Enchanted Forest",
                "description": "A magical adventure in an enchanted forest",
                "created_at": "2024-01-01T00:00:00",
                "version": "1.0",
                "example": True
            },
            "entry_points": ["forest_entrance"],
            "endings": ["victory", "defeat"],
            "nodes": {
                "forest_entrance": {
                    "id": "forest_entrance",
                    "text": "You stand at the edge of an enchanted forest. Ancient trees tower above you, their leaves whispering secrets in the wind.",
                    "node_type": "entry",
                    "is_entry": True,
                    "choices": [
                        {
                            "text": "Enter the forest cautiously",
                            "target_node_id": "deep_forest"
                        },
                        {
                            "text": "Call out to see if anyone is there",
                            "target_node_id": "mysterious_voice"
                        }
                    ]
                },
                "deep_forest": {
                    "id": "deep_forest",
                    "text": "Deeper in the forest, you discover a clearing with a magical fountain.",
                    "node_type": "story",
                    "choices": [
                        {
                            "text": "Drink from the fountain",
                            "target_node_id": "victory"
                        },
                        {
                            "text": "Walk away",
                            "target_node_id": "defeat"
                        }
                    ]
                },
                "victory": {
                    "id": "victory",
                    "text": "The magical water grants you wisdom and power. You have succeeded!",
                    "node_type": "ending",
                    "is_ending": True,
                    "ending_type": "success",
                    "choices": []
                }
            }
        }
        
        example_path = Path("data/storylines/example_story.json")
        with open(example_path, 'w') as f:
            json.dump(example_story, f, indent=2)
        
        console.print("[green]✅ Example content created[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Example creation failed: {e}[/red]")
        return False


def validate_setup() -> bool:
    """Validate the setup"""
    try:
        # Run basic system health check
        sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
        
        try:
            from utils.system_health import SystemHealthChecker
            
            config = load_config()
            health_checker = SystemHealthChecker(config)
            checks = health_checker.run_all_checks()
            overall_status = health_checker.get_overall_status()
            
            if overall_status.value == "error":
                console.print("[red]❌ System validation failed[/red]")
                return False
            else:
                console.print("[green]✅ System validation passed[/green]")
                return True
                
        except ImportError:
            console.print("[yellow]⚠️  Cannot run full validation[/yellow]")
            return True
        
    except Exception as e:
        console.print(f"[red]❌ Validation failed: {e}[/red]")
        return False


def show_completion_message():
    """Show setup completion message"""
    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold green]🎉 Setup Complete![/bold green]\n\n"
            "[white]Your CYOA Automation System is ready to use![/white]\n\n"
            "[bold]Next Steps:[/bold]\n"
            "[cyan]1.[/cyan] Create your first story: [bold]pixi run wizard[/bold]\n"
            "[cyan]2.[/cyan] Launch the GUI: [bold]pixi run run[/bold]\n"
            "[cyan]3.[/cyan] Check system health: [bold]pixi run health[/bold]\n"
            "[cyan]4.[/cyan] View available commands: [bold]pixi task list[/bold]\n\n"
            "[dim]Edit .env file to add your X API credentials for posting[/dim]",
            border_style="green"
        ))
    else:
        print("\n🎉 Setup Complete!")
        print("\nYour CYOA Automation System is ready to use!")
        print("\nNext Steps:")
        print("1. Create your first story: pixi run wizard")
        print("2. Launch the GUI: pixi run run")
        print("3. Check system health: pixi run health")
        print("4. View available commands: pixi task list")
        print("\nEdit .env file to add your X API credentials for posting")


def load_config() -> dict:
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
