#!/usr/bin/env python3
"""
Analytics CLI - Beautiful command-line analytics for story performance
Track engagement, monetization, and optimization opportunities
"""

import sys
import json
from pathlib import Path
from typing import Optional, List, Dict, Any

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text
from rich.layout import Layout
from rich.columns import Columns
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from analytics.story_analytics import StoryAnalytics, PostMetrics, PathAnalytics
    from story.story_web import StoryWeb
    from social.x_ads_testing import XAdsABTesting
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the project root with: pixi run analytics")
    sys.exit(1)

app = typer.Typer(help="📊 Story Analytics & Optimization")
console = Console()


@app.command()
def analyze(
    story_id: str = typer.Argument(..., help="Story ID to analyze"),
    collect_fresh: bool = typer.Option(False, "--fresh", help="Collect fresh metrics from X"),
    show_paths: bool = typer.Option(True, "--paths", help="Show path analysis"),
    show_suggestions: bool = typer.Option(True, "--suggestions", help="Show optimization suggestions")
):
    """Analyze story performance and provide optimization insights"""
    
    console.print(Panel.fit(
        f"[bold cyan]📊 Story Analytics: {story_id}[/bold cyan]\n"
        "[dim]Analyzing engagement, monetization, and optimization opportunities[/dim]",
        border_style="cyan"
    ))
    
    # Load story
    story = load_story(story_id)
    if not story:
        console.print(f"[red]❌ Story '{story_id}' not found[/red]")
        raise typer.Exit(1)
    
    # Initialize analytics
    config = load_config()
    analytics = StoryAnalytics(config)
    
    # Collect or load metrics
    if collect_fresh:
        console.print("\n[yellow]🔍 Collecting fresh metrics from X...[/yellow]")
        post_mappings = load_post_mappings(story_id)
        if not post_mappings:
            console.print("[red]❌ No post mappings found. Make sure story has been posted to X.[/red]")
            raise typer.Exit(1)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Collecting post metrics...", total=None)
            metrics = analytics.collect_post_metrics(story_id, post_mappings)
            progress.update(task, completed=True)
    else:
        console.print("\n[blue]📂 Loading cached metrics...[/blue]")
        metrics = analytics.load_metrics(story_id)
    
    if not metrics:
        console.print("[red]❌ No metrics available. Try running with --fresh flag.[/red]")
        raise typer.Exit(1)
    
    # Show overall performance
    show_overall_performance(story, metrics)
    
    # Show path analysis
    if show_paths:
        console.print("\n" + "="*60)
        show_path_analysis(story, metrics, analytics)
    
    # Show optimization suggestions
    if show_suggestions:
        console.print("\n" + "="*60)
        show_optimization_suggestions(story, metrics, analytics)


@app.command()
def compare(
    story_ids: List[str] = typer.Argument(..., help="Story IDs to compare"),
    metric: str = typer.Option("revenue", "--metric", help="Metric to compare (revenue, engagement, completion)")
):
    """Compare performance across multiple stories"""
    
    console.print(Panel.fit(
        "[bold magenta]📈 Story Performance Comparison[/bold magenta]\n"
        f"[dim]Comparing {len(story_ids)} stories by {metric}[/dim]",
        border_style="magenta"
    ))
    
    config = load_config()
    analytics = StoryAnalytics(config)
    
    # Collect data for all stories
    story_data = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Loading story data...", total=len(story_ids))
        
        for story_id in story_ids:
            story = load_story(story_id)
            metrics = analytics.load_metrics(story_id)
            
            if story and metrics:
                story_data.append({
                    'id': story_id,
                    'title': story.metadata.get('title', story_id),
                    'story': story,
                    'metrics': metrics
                })
            
            progress.advance(task)
    
    if not story_data:
        console.print("[red]❌ No valid story data found[/red]")
        raise typer.Exit(1)
    
    # Show comparison
    show_story_comparison(story_data, metric)


@app.command()
def paths(
    story_id: str = typer.Argument(..., help="Story ID to analyze"),
    min_completion: float = typer.Option(0.0, "--min-completion", help="Minimum completion rate to show"),
    sort_by: str = typer.Option("revenue", "--sort", help="Sort paths by (revenue, completion, engagement)")
):
    """Detailed path analysis for a story"""
    
    console.print(Panel.fit(
        f"[bold green]🛤️  Path Analysis: {story_id}[/bold green]\n"
        "[dim]Analyzing user journeys through your story[/dim]",
        border_style="green"
    ))
    
    story = load_story(story_id)
    if not story:
        console.print(f"[red]❌ Story '{story_id}' not found[/red]")
        raise typer.Exit(1)
    
    config = load_config()
    analytics = StoryAnalytics(config)
    metrics = analytics.load_metrics(story_id)
    
    if not metrics:
        console.print("[red]❌ No metrics available[/red]")
        raise typer.Exit(1)
    
    # Analyze paths
    path_analytics = analytics.analyze_story_paths(story, metrics)
    
    # Filter and sort paths
    filtered_paths = {
        path_id: path_data for path_id, path_data in path_analytics.items()
        if path_data.completion_rate >= min_completion
    }
    
    if sort_by == "revenue":
        sorted_paths = sorted(filtered_paths.items(), key=lambda x: x[1].total_revenue, reverse=True)
    elif sort_by == "completion":
        sorted_paths = sorted(filtered_paths.items(), key=lambda x: x[1].completion_rate, reverse=True)
    elif sort_by == "engagement":
        sorted_paths = sorted(filtered_paths.items(), key=lambda x: x[1].average_engagement, reverse=True)
    else:
        sorted_paths = list(filtered_paths.items())
    
    # Display paths
    show_detailed_paths(sorted_paths, story)


@app.command()
def optimize(
    story_id: str = typer.Argument(..., help="Story ID to optimize"),
    auto_apply: bool = typer.Option(False, "--auto-apply", help="Automatically apply safe optimizations"),
    max_edits: int = typer.Option(3, "--max-edits", help="Maximum edits to suggest (X has 5 edit limit)")
):
    """Get specific optimization recommendations"""
    
    console.print(Panel.fit(
        f"[bold yellow]🚀 Story Optimization: {story_id}[/bold yellow]\n"
        "[dim]AI-powered recommendations to improve performance[/dim]",
        border_style="yellow"
    ))
    
    story = load_story(story_id)
    if not story:
        console.print(f"[red]❌ Story '{story_id}' not found[/red]")
        raise typer.Exit(1)
    
    config = load_config()
    analytics = StoryAnalytics(config)
    metrics = analytics.load_metrics(story_id)
    
    if not metrics:
        console.print("[red]❌ No metrics available[/red]")
        raise typer.Exit(1)
    
    # Generate optimization report
    path_analytics = analytics.analyze_story_paths(story, metrics)
    performance = analytics.generate_optimization_report(story, metrics, path_analytics)
    
    # Get specific edit suggestions
    edit_suggestions = analytics.suggest_story_edits(story, performance)
    
    # Show optimization recommendations
    show_optimization_recommendations(performance, edit_suggestions[:max_edits])
    
    # Apply optimizations if requested
    if auto_apply and edit_suggestions:
        apply_optimizations(story_id, edit_suggestions[:max_edits])


@app.command()
def ab_test(
    story_id: str = typer.Argument(..., help="Story ID"),
    node_id: str = typer.Argument(..., help="Node ID to test"),
    variant_videos: List[str] = typer.Option(..., "--video", help="Variant video paths"),
    budget: float = typer.Option(20.0, "--budget", help="Total test budget"),
    duration: int = typer.Option(24, "--duration", help="Test duration in hours")
):
    """Create A/B test for a story node"""
    
    console.print(Panel.fit(
        f"[bold red]🧪 A/B Test Setup[/bold red]\n"
        f"[dim]Testing {len(variant_videos)} video variants for node {node_id}[/dim]",
        border_style="red"
    ))
    
    # Load post mapping
    post_mappings = load_post_mappings(story_id)
    if not post_mappings or node_id not in post_mappings:
        console.print(f"[red]❌ No post found for node {node_id}[/red]")
        raise typer.Exit(1)
    
    post_id = post_mappings[node_id]
    
    # Setup A/B test
    config = load_config()
    ab_testing = XAdsABTesting(config)
    
    test_config = {
        'duration_hours': duration,
        'budget_per_variant': budget / len(variant_videos),
        'target_audience': {}
    }
    
    # Create test
    ab_test = ab_testing.create_ab_test(
        node_id=node_id,
        post_id=post_id,
        original_video="",  # Would get from story
        variant_videos=variant_videos,
        test_config=test_config
    )
    
    if ab_test:
        console.print(f"[green]✅ Created A/B test: {ab_test.test_id}[/green]")
        
        # Start test
        if ab_testing.start_ab_test(ab_test.test_id):
            console.print(f"[green]🚀 Started A/B test - will run for {duration} hours[/green]")
            console.print(f"[dim]Monitor with: pixi run analytics ab-status {ab_test.test_id}[/dim]")
        else:
            console.print("[red]❌ Failed to start A/B test[/red]")
    else:
        console.print("[red]❌ Failed to create A/B test[/red]")


def show_overall_performance(story, metrics: Dict[str, PostMetrics]):
    """Show overall story performance"""
    console.print("\n[bold cyan]📊 Overall Performance[/bold cyan]")
    
    # Calculate totals
    total_views = sum(m.views for m in metrics.values())
    total_engagement = sum(m.likes + m.retweets + m.replies + m.clicks for m in metrics.values())
    total_revenue = sum(m.revenue_generated for m in metrics.values())
    total_conversions = sum(m.subscription_conversions for m in metrics.values())
    
    # Create performance table
    table = Table(show_header=False, box=None, padding=(0, 2))
    table.add_column("Metric", style="cyan", width=20)
    table.add_column("Value", style="green", width=15)
    table.add_column("Details", style="dim", width=30)
    
    table.add_row("👀 Total Views", f"{total_views:,}", f"Across {len(metrics)} posts")
    table.add_row("💝 Total Engagement", f"{total_engagement:,}", f"{total_engagement/total_views:.1%} rate" if total_views > 0 else "0%")
    table.add_row("💰 Total Revenue", f"${total_revenue:.2f}", f"${total_revenue/total_views*1000:.2f} RPM" if total_views > 0 else "$0 RPM")
    table.add_row("🔄 Conversions", f"{total_conversions:,}", f"{total_conversions/total_views:.2%} rate" if total_views > 0 else "0%")
    
    console.print(table)


def show_path_analysis(story, metrics: Dict[str, PostMetrics], analytics: StoryAnalytics):
    """Show path analysis"""
    console.print("\n[bold green]🛤️  Path Analysis[/bold green]")
    
    path_analytics = analytics.analyze_story_paths(story, metrics)
    
    if not path_analytics:
        console.print("[yellow]No path data available[/yellow]")
        return
    
    # Show top 3 paths
    sorted_paths = sorted(path_analytics.items(), key=lambda x: x[1].total_revenue, reverse=True)
    
    for i, (path_id, path_data) in enumerate(sorted_paths[:3]):
        console.print(f"\n[bold]Path {i+1}:[/bold] {len(path_data.path_nodes)} nodes")
        
        path_table = Table(show_header=False, box=None)
        path_table.add_column("Metric", style="blue")
        path_table.add_column("Value", style="white")
        
        path_table.add_row("Users", f"{path_data.total_users:,}")
        path_table.add_row("Completion Rate", f"{path_data.completion_rate:.1%}")
        path_table.add_row("Avg Engagement", f"{path_data.average_engagement:.0f}")
        path_table.add_row("Revenue", f"${path_data.total_revenue:.2f}")
        
        if path_data.drop_off_points:
            path_table.add_row("Drop-off Points", f"{len(path_data.drop_off_points)} nodes")
        
        console.print(path_table)


def show_optimization_suggestions(story, metrics: Dict[str, PostMetrics], analytics: StoryAnalytics):
    """Show optimization suggestions"""
    console.print("\n[bold yellow]🚀 Optimization Suggestions[/bold yellow]")
    
    path_analytics = analytics.analyze_story_paths(story, metrics)
    performance = analytics.generate_optimization_report(story, metrics, path_analytics)
    
    if performance.optimization_suggestions:
        for i, suggestion in enumerate(performance.optimization_suggestions, 1):
            console.print(f"[cyan]{i}.[/cyan] {suggestion}")
    else:
        console.print("[green]✅ Story is performing well - no major optimizations needed[/green]")


def show_story_comparison(story_data: List[Dict], metric: str):
    """Show comparison between stories"""
    table = Table(title=f"Story Comparison by {metric.title()}")
    table.add_column("Story", style="cyan")
    table.add_column("Views", style="blue")
    table.add_column("Engagement", style="green")
    table.add_column("Revenue", style="yellow")
    table.add_column("Conversion Rate", style="red")
    
    for data in story_data:
        metrics = data['metrics']
        total_views = sum(m.views for m in metrics.values())
        total_engagement = sum(m.likes + m.retweets + m.replies + m.clicks for m in metrics.values())
        total_revenue = sum(m.revenue_generated for m in metrics.values())
        total_conversions = sum(m.subscription_conversions for m in metrics.values())
        conversion_rate = total_conversions / total_views if total_views > 0 else 0
        
        table.add_row(
            data['title'][:30],
            f"{total_views:,}",
            f"{total_engagement:,}",
            f"${total_revenue:.2f}",
            f"{conversion_rate:.2%}"
        )
    
    console.print(table)


def show_detailed_paths(sorted_paths: List, story):
    """Show detailed path information"""
    for i, (path_id, path_data) in enumerate(sorted_paths[:10]):  # Top 10 paths
        console.print(f"\n[bold cyan]Path {i+1}:[/bold cyan]")
        
        # Show path nodes
        path_nodes = " → ".join([node_id[:8] for node_id in path_data.path_nodes])
        console.print(f"[dim]Route:[/dim] {path_nodes}")
        
        # Show metrics
        metrics_text = (
            f"👥 {path_data.total_users:,} users | "
            f"✅ {path_data.completion_rate:.1%} completion | "
            f"💰 ${path_data.total_revenue:.2f} revenue"
        )
        console.print(metrics_text)
        
        # Show drop-off points
        if path_data.drop_off_points:
            console.print("[red]⚠️  Drop-off points:[/red]")
            for node_id, drop_rate in path_data.drop_off_points[:3]:
                console.print(f"   {node_id}: {drop_rate:.1%} drop-off")


def show_optimization_recommendations(performance, edit_suggestions: List[Dict]):
    """Show specific optimization recommendations"""
    console.print(f"\n[bold]📈 Performance Summary:[/bold]")
    console.print(f"Total Revenue: ${performance.total_revenue:.2f}")
    console.print(f"Conversion Rate: {performance.conversion_rate:.2%}")
    console.print(f"Total Engagement: {performance.total_engagement:,}")
    
    if edit_suggestions:
        console.print(f"\n[bold yellow]🔧 Recommended Edits (within 5-edit limit):[/bold yellow]")
        
        for i, suggestion in enumerate(edit_suggestions, 1):
            priority_color = "red" if suggestion['priority'] == 'high' else "yellow" if suggestion['priority'] == 'medium' else "green"
            
            console.print(f"\n[{priority_color}]{i}. {suggestion['type'].replace('_', ' ').title()}[/{priority_color}]")
            console.print(f"   {suggestion['suggestion']}")
            console.print(f"   [dim]Impact: {suggestion['estimated_impact']}[/dim]")


def apply_optimizations(story_id: str, edit_suggestions: List[Dict]):
    """Apply safe optimizations automatically"""
    console.print(f"\n[yellow]🔄 Applying optimizations to {story_id}...[/yellow]")
    
    # This would actually apply the optimizations
    # For now, just simulate
    for suggestion in edit_suggestions:
        if suggestion['priority'] == 'high':
            console.print(f"[green]✅ Applied: {suggestion['type']}[/green]")
        else:
            console.print(f"[yellow]⏭️  Skipped: {suggestion['type']} (manual review needed)[/yellow]")


def load_story(story_id: str):
    """Load story from file"""
    try:
        story_file = Path(f"data/storylines/{story_id}.json")
        if not story_file.exists():
            return None
        
        with open(story_file) as f:
            story_data = json.load(f)
        
        return StoryWeb.from_dict(story_data)
    except Exception as e:
        console.print(f"[red]Error loading story: {e}[/red]")
        return None


def load_post_mappings(story_id: str) -> Dict[str, str]:
    """Load node_id -> post_id mappings"""
    try:
        mappings_file = Path(f"data/post_mappings/{story_id}.json")
        if not mappings_file.exists():
            return {}
        
        with open(mappings_file) as f:
            return json.load(f)
    except Exception:
        return {}


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path) as f:
            return json.load(f)
    except Exception:
        return {}


if __name__ == "__main__":
    app()
