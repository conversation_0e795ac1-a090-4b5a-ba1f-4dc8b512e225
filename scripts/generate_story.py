#!/usr/bin/env python3
"""
Story Generation Script
Command-line interface for generating CYOA stories
"""

import sys
import json
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.lmstudio_client import LMStudioClient
from story.story_generator import StoryGenerator
from story.inventory_system import InventoryManager
from story.class_system import ClassManager
from story.scoring_system import ScoringSystem
from story.rating_system import RatingSystem


def load_config():
    """Load configuration"""
    config_path = Path("config.json")
    if not config_path.exists():
        print("Error: config.json not found")
        sys.exit(1)
    
    with open(config_path) as f:
        return json.load(f)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Generate CYOA stories")
    parser.add_argument("--input", "-i", help="Input text file")
    parser.add_argument("--title", "-t", help="Story title")
    parser.add_argument("--class", "-c", dest="player_class", 
                       choices=["Mage", "Ranger", "Charmer"],
                       default="Ranger", help="Player class")
    parser.add_argument("--output", "-o", help="Output file path")
    parser.add_argument("--theme", help="Story theme (for standalone generation)")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config()
    
    # Initialize components
    lmstudio_client = LMStudioClient(config)
    
    if not lmstudio_client.is_available():
        print("Error: LM Studio server not available")
        sys.exit(1)

    story_generator = StoryGenerator(config, lmstudio_client)
    scoring_system = ScoringSystem(config)
    rating_system = RatingSystem(config)
    
    # Generate story
    if args.input:
        # Import from text file
        if not args.title:
            args.title = Path(args.input).stem.replace('_', ' ').title()
        
        print(f"Importing storyline from {args.input}...")
        
        with open(args.input, 'r', encoding='utf-8') as f:
            source_text = f.read()
        
        story = story_generator.import_storyline_from_text(
            source_text, args.title, args.player_class
        )
    
    elif args.theme:
        # Generate standalone story
        print(f"Generating standalone story with theme: {args.theme}")
        
        story = story_generator.generate_standalone_web(
            args.theme, args.player_class
        )
    
    else:
        print("Error: Either --input or --theme must be specified")
        sys.exit(1)
    
    if not story:
        print("Error: Failed to generate story")
        sys.exit(1)
    
    # Update scores and ratings
    print("Calculating scores and ratings...")
    scoring_system.update_story_scores(story)
    rating_system.update_story_ratings(story)
    
    # Validate story
    is_valid, errors = story.validate_structure()
    if not is_valid:
        print("Warning: Story validation issues:")
        for error in errors:
            print(f"  - {error}")
    
    # Save story
    if args.output:
        output_path = args.output
    else:
        output_path = f"data/storylines/{args.title.lower().replace(' ', '_')}.json"
    
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    
    if story.save_to_file(output_path):
        print(f"Story saved to: {output_path}")
        
        # Print summary
        print(f"\nStory Summary:")
        print(f"  Title: {story.metadata.get('title', 'Untitled')}")
        print(f"  Nodes: {len(story.nodes)}")
        print(f"  Entry Points: {len(story.entry_points)}")
        print(f"  Endings: {len(story.endings)}")
        
        # Rating summary
        rating_summary = rating_system.get_rating_summary(story)
        print(f"  Safe Nodes: {rating_summary.get('safe_nodes', 0)}")
        print(f"  Spicy Nodes: {rating_summary.get('spicy_nodes', 0)}")
        
        # Score summary
        score_summary = scoring_system.get_score_summary(story)
        print(f"  Death Endings: {score_summary.get('death_endings', 0)}")
        print(f"  Success Endings: {score_summary.get('success_endings', 0)}")
        
        if score_summary.get('average_death_score', 0) > 0:
            print(f"  Average Death Score: {score_summary['average_death_score']:.1f}%")
    
    else:
        print("Error: Failed to save story")
        sys.exit(1)


if __name__ == "__main__":
    main()
