#!/usr/bin/env python3
"""
CYOA Automation System - Main Entry Point
Choose Your Own Adventure automation for X (Twitter) with local AI models
"""

import sys
import os
import json
import argparse
from pathlib import Path
from typing import Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import logging system first
from utils.logging_config import setup_logging, get_logger, LogLevel
from utils.logging_decorators import logged, performance_logged, error_logged

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer
import dotenv

# Import our modules
from utils.lmstudio_client import LMStudioClient
from story.story_generator import StoryGenerator
from story.inventory_system import InventoryManager
from story.class_system import ClassManager
from story.scoring_system import ScoringSystem
from story.rating_system import RatingSystem
from gui.main_window import MainWindow


@logged('main.config')
def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='CYOA Automation System')
    parser.add_argument('--verbosity', '-v', type=int, default=LogLevel.INFO,
                       choices=[0, 5, 10, 20, 30, 40, 50],
                       help='Logging verbosity (0=silent, 5=trace, 10=debug, 20=info, 30=warning, 40=error, 50=critical)')
    parser.add_argument('--log-format', choices=['simple', 'detailed', 'json', 'rich'],
                       default='rich', help='Log output format')
    parser.add_argument('--no-file-logging', action='store_true',
                       help='Disable file logging')
    parser.add_argument('--config', default='config.json',
                       help='Configuration file path')
    return parser.parse_args()


@logged('main.config')
def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """Load configuration from config.json"""
    logger = get_logger('main.config')

    try:
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"{config_path} not found")

        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        logger.info("Configuration loaded successfully")
        return config

    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        # Return default configuration
        return {
            "story_generation": {
                "num_entry_points": 3,
                "min_endings": 5,
                "max_nodes": 100
            },
            "inventory_system": {
                "initial_inventory": {"gold": 100}
            },
            "class_system": {
                "classes": ["Mage", "Ranger", "Charmer"]
            },
            "lmstudio": {
                "base_url": "http://localhost:1234",
                "default_model": "local-model",
                "timeout": 120
            }
        }


@logged('main.env')
def load_environment() -> None:
    """Load environment variables"""
    logger = get_logger('main.env')

    env_path = Path(".env")
    if env_path.exists():
        dotenv.load_dotenv(env_path)
        logger.info("Environment variables loaded from .env")
    else:
        logger.warning(".env file not found, using system environment variables")


@logged('main.setup')
def create_directories(config: Dict[str, Any]) -> None:
    """Create necessary directories"""
    logger = get_logger('main.setup')

    paths = config.get("paths", {})

    directories = [
        paths.get("videos_dir", "videos"),
        paths.get("logs_dir", "logs"),
        paths.get("data_dir", "data"),
        paths.get("storylines_dir", "data/storylines"),
        paths.get("workflows_dir", "workflows")
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.debug(f"Created directory: {directory}")

    logger.info("Created necessary directories")


@logged('main.dependencies')
def check_dependencies(config: Dict[str, Any]) -> bool:
    """Check if required dependencies are available"""
    logger = get_logger('main.dependencies')
    errors = []

    # Check LM Studio
    try:
        lmstudio_client = LMStudioClient(config)

        if not lmstudio_client.is_available():
            errors.append("LM Studio server is not available. Please start LM Studio and load a model")
        else:
            logger.info("LM Studio connection verified")
    except Exception as e:
        errors.append(f"Error checking LM Studio: {e}")

    # Check ComfyUI (optional for now)
    try:
        import requests
        comfyui_config = config.get("comfyui", {})
        comfyui_url = comfyui_config.get("base_url", "http://127.0.0.1:8188")

        response = requests.get(f"{comfyui_url}/system_stats", timeout=5)
        if response.status_code != 200:
            logger.warning("ComfyUI server not available (optional)")
        else:
            logger.info("ComfyUI connection verified")
    except Exception:
        logger.warning("ComfyUI server not available (optional)")

    if errors:
        for error in errors:
            logger.error(error)
        return False

    return True


class CYOAApplication:
    """Main application class"""

    def __init__(self):
        self.config = None
        self.lmstudio_client = None
        self.story_generator = None
        self.inventory_manager = None
        self.class_manager = None
        self.scoring_system = None
        self.rating_system = None
        self.main_window = None
        self.logger = None

    @logged('main.app')
    def initialize(self, args) -> bool:
        """Initialize the application"""
        try:
            # Setup logging first
            logging_config = {
                'logging': {
                    'verbosity': args.verbosity,
                    'log_to_file': not args.no_file_logging,
                    'format': args.log_format
                }
            }
            setup_logging(logging_config)
            self.logger = get_logger('main.app')

            # Log system information
            logging_manager = setup_logging(logging_config)
            logging_manager.log_system_info()

            # Load configuration and environment
            self.config = load_config(args.config)
            load_environment()

            # Log configuration (sanitized)
            logging_manager.log_config(self.config)

            # Create directories
            create_directories(self.config)

            # Check dependencies
            if not check_dependencies(self.config):
                return False

            # Initialize core components
            self._initialize_components()

            self.logger.info("🚀 CYOA Automation System initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error initializing application: {e}")
            else:
                print(f"Error initializing application: {e}")
            return False

    @performance_logged('main.components')
    def _initialize_components(self):
        """Initialize core system components"""
        self.logger.debug("🔧 Initializing core components...")

        # Initialize LM Studio client
        self.lmstudio_client = LMStudioClient(self.config)

        # Initialize story systems
        self.story_generator = StoryGenerator(self.config, self.lmstudio_client)
        self.inventory_manager = InventoryManager(self.config)
        self.class_manager = ClassManager(self.config)
        self.scoring_system = ScoringSystem(self.config)
        self.rating_system = RatingSystem(self.config)

        self.logger.info("✅ Core components initialized")
    
    @logged('main.gui')
    def run_gui(self, app: QApplication) -> int:
        """Run the GUI application"""
        try:
            self.logger.info("🎨 Starting GUI application...")

            # Create main window
            self.main_window = MainWindow(
                config=self.config,
                lmstudio_client=self.lmstudio_client,
                story_generator=self.story_generator,
                inventory_manager=self.inventory_manager,
                class_manager=self.class_manager,
                scoring_system=self.scoring_system,
                rating_system=self.rating_system
            )

            self.main_window.show()

            # Setup periodic tasks
            self._setup_periodic_tasks()

            self.logger.info("✅ GUI application started")
            return app.exec()

        except Exception as e:
            self.logger.error(f"Error running GUI: {e}")
            QMessageBox.critical(None, "Error", f"Failed to start GUI: {e}")
            return 1

    @logged('main.tasks')
    def _setup_periodic_tasks(self):
        """Setup periodic background tasks"""
        self.logger.debug("⏰ Setting up periodic tasks...")

        # Check LM Studio connection every 30 seconds
        self.lmstudio_timer = QTimer()
        self.lmstudio_timer.timeout.connect(self._check_lmstudio_connection)
        self.lmstudio_timer.start(30000)  # 30 seconds

        self.logger.debug("✅ Periodic tasks configured")

    def _check_lmstudio_connection(self):
        """Periodically check LM Studio connection"""
        try:
            if not self.lmstudio_client.is_available():
                self.logger.warning("⚠️ LM Studio connection lost")
                if self.main_window:
                    self.main_window.update_status("LM Studio connection lost", "error")
            else:
                current_model = self.lmstudio_client.get_current_model()
                if self.main_window:
                    if current_model:
                        self.main_window.update_status(f"LM Studio connected: {current_model}", "success")
                        self.logger.trace(f"🔗 LM Studio connected with model: {current_model}")
                    else:
                        self.main_window.update_status("LM Studio connected (no model)", "warning")
                        self.logger.trace("🔗 LM Studio connected but no model loaded")
        except Exception as e:
            self.logger.error(f"Error checking LM Studio connection: {e}")


@error_logged('main', reraise=False, default_return=1)
def main():
    """Main entry point"""
    logger = None

    try:
        # Parse command line arguments
        args = parse_arguments()

        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("CYOA Automation System")
        app.setApplicationVersion("0.1.0")

        # Create and initialize application
        cyoa_app = CYOAApplication()

        if not cyoa_app.initialize(args):
            QMessageBox.critical(
                None,
                "Initialization Error",
                "Failed to initialize CYOA Automation System. Check logs for details."
            )
            return 1

        logger = cyoa_app.logger

        # Run the application
        return cyoa_app.run_gui(app)

    except KeyboardInterrupt:
        if logger:
            logger.info("👋 Application interrupted by user")
        else:
            print("Application interrupted by user")
        return 0
    except Exception as e:
        if logger:
            logger.error(f"💥 Unexpected error: {e}")
        else:
            print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
