#!/usr/bin/env python3
"""
Live LM Studio Integration Test
Tests actual API calls to the running LM Studio server
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_generation():
    """Test basic text generation"""
    print("🤖 Testing Basic Text Generation")
    print("-" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://127.0.0.1:1234",
                "default_model": "google/gemma-3-12b",
                "timeout": 60
            }
        }
        
        client = LMStudioClient(config)
        
        # Test basic text generation
        print("Testing simple text generation...")
        response = client.generate_text("Tell me a very short story about a brave knight.")
        
        if response.success:
            print("✅ Text generation successful!")
            print(f"Response: {response.text[:100]}...")
            return True
        else:
            print(f"❌ Text generation failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Basic generation test failed: {e}")
        return False

def test_story_generation():
    """Test story-specific generation methods"""
    print("\n📚 Testing Story Generation Methods")
    print("-" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://127.0.0.1:1234",
                "default_model": "google/gemma-3-12b",
                "timeout": 60
            }
        }
        
        client = LMStudioClient(config)
        
        # Test story node generation
        print("Testing story node generation...")
        response = client.generate_story_node(
            context="A brave knight enters a dark forest",
            inventory={"sword": 1, "gold": 50},
            player_class="Knight",
            node_type="story"
        )
        
        if response.success:
            print("✅ Story node generation successful!")
            print(f"Response: {response.text[:100]}...")
            
            # Test audio script generation
            print("\nTesting audio script generation...")
            audio_response = client.generate_audio_script(
                "The knight draws his sword as shadows move between the trees.",
                is_spicy=False
            )
            
            if audio_response.success:
                print("✅ Audio script generation successful!")
                print(f"Audio script: {audio_response.text[:100]}...")
                return True
            else:
                print(f"❌ Audio script generation failed: {audio_response.error}")
                return False
        else:
            print(f"❌ Story node generation failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Story generation test failed: {e}")
        return False

def test_model_info():
    """Test model information retrieval"""
    print("\n🔍 Testing Model Information")
    print("-" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://127.0.0.1:1234",
                "default_model": "google/gemma-3-12b",
                "timeout": 60
            }
        }
        
        client = LMStudioClient(config)
        
        # Test getting available models
        print("Getting available models...")
        models = client.get_available_models()
        print(f"✅ Found {len(models)} available models")
        for model in models[:3]:  # Show first 3
            print(f"   - {model}")
        
        # Test getting current model
        print("\nGetting current model...")
        current_model = client.get_current_model()
        if current_model:
            print(f"✅ Current model: {current_model}")
        else:
            print("⚠️  No current model detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Model info test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Live LM Studio Integration Test")
    print("=" * 50)
    print("Testing with running LM Studio server at http://127.0.0.1:1234")
    print("=" * 50)
    
    tests = [
        ("Basic Generation", test_basic_generation),
        ("Story Generation", test_story_generation),
        ("Model Information", test_model_info),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 LIVE TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL LIVE TESTS PASSED!")
        print("\nLM Studio integration is fully functional!")
        print("\n📋 System is ready for:")
        print("1. Story generation")
        print("2. Quiz creation")
        print("3. Audio script generation")
        print("4. Video prompt generation")
        print("\n🚀 You can now run the main application!")
        return 0
    else:
        print(f"\n⚠️  {total - passed} TESTS FAILED")
        print("\nSome functionality may not work correctly.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
