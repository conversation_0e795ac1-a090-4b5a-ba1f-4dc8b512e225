#!/usr/bin/env python3
"""
Test story generation methods specifically
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    print("📚 Testing Story Generation Methods")
    print("=" * 40)
    
    try:
        from utils.lmstudio_client import LMStudioClient
        
        config = {
            "lmstudio": {
                "base_url": "http://127.0.0.1:1234",
                "default_model": "google/gemma-3-12b",
                "timeout": 300  # 5 minutes for laptop performance
            }
        }
        
        client = LMStudioClient(config)
        
        print("1. Testing story node generation...")
        response = client.generate_story_node(
            context="A brave knight enters a dark forest",
            inventory={"sword": 1, "gold": 50},
            player_class="Knight",
            node_type="story"
        )
        
        if response.success:
            print("   ✅ Story node generation successful!")
            print(f"   Response length: {len(response.text)} characters")
            print(f"   Response time: {response.response_time:.2f}s")
            print(f"   Preview: {response.text[:100]}...")
        else:
            print(f"   ❌ Story node generation failed: {response.error}")
            return 1
        
        print("\n2. Testing audio script generation...")
        audio_response = client.generate_audio_script(
            "The knight draws his sword as shadows move between the trees.",
            is_spicy=False
        )
        
        if audio_response.success:
            print("   ✅ Audio script generation successful!")
            print(f"   Response length: {len(audio_response.text)} characters")
            print(f"   Response time: {audio_response.response_time:.2f}s")
            print(f"   Preview: {audio_response.text[:100]}...")
        else:
            print(f"   ❌ Audio script generation failed: {audio_response.error}")
            return 1
        
        print("\n3. Testing character extraction...")
        char_response = client.extract_characters_from_text(
            "Sir Galahad was a noble knight known for his purity. The evil sorcerer Morgana opposed him at every turn.",
            "The Quest"
        )
        
        if char_response.success:
            print("   ✅ Character extraction successful!")
            print(f"   Response length: {len(char_response.text)} characters")
            print(f"   Response time: {char_response.response_time:.2f}s")
            print(f"   Preview: {char_response.text[:100]}...")
        else:
            print(f"   ❌ Character extraction failed: {char_response.error}")
            return 1
        
        print("\n🎉 All story generation methods are working!")
        print("\n📋 Summary:")
        print(f"   - Story node generation: ✅ ({response.response_time:.1f}s)")
        print(f"   - Audio script generation: ✅ ({audio_response.response_time:.1f}s)")
        print(f"   - Character extraction: ✅ ({char_response.response_time:.1f}s)")
        
        print("\n🚀 LM Studio integration is fully functional for story generation!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
