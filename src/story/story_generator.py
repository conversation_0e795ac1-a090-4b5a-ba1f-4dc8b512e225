"""
Story Generator - Creates CYOA stories using LLM
Handles storyline import, web generation, and content creation
"""

import json
import uuid
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path

from utils.lmstudio_client import LMStudioClient
from story.story_web import StoryW<PERSON>, StoryNode, Choice, NodeType, EndingType
from story.character_system import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Character<PERSON><PERSON>, Character<PERSON><PERSON><PERSON>ce, CharacterVoice, CharacterPersonality

logger = logging.getLogger(__name__)


class StoryGenerator:
    """Generates CYOA stories using local LLM"""
    
    def __init__(self, config: Dict[str, Any], lmstudio_client: LMStudioClient):
        self.config = config
        self.lmstudio = lmstudio_client
        self.story_config = config.get('story_generation', {})
        self.inventory_config = config.get('inventory_system', {})
        self.class_config = config.get('class_system', {})
        
    def import_storyline_from_text(self, source_text: str, title: str,
                                 player_class: str = "Ranger") -> Optional[StoryWeb]:
        """Import and convert text to CYOA storyline"""
        try:
            logger.info(f"Importing storyline: {title}")
            
            initial_inventory = self.inventory_config.get('initial_inventory', {"gold": 100})
            
            # Generate storyline using LLM
            response = self.lmstudio.generate_storyline_from_text(
                source_text, title, initial_inventory, player_class
            )
            
            if not response.success:
                logger.error(f"Failed to generate storyline: {response.error}")
                return None
            
            # Parse the response
            storyline_data = self._parse_storyline_response(response.text)
            if not storyline_data:
                logger.error("Failed to parse storyline response")
                return None
            
            # Create story web
            web = StoryWeb(self.config)
            web.metadata.update({
                "title": title,
                "source": "imported_text",
                "player_class": player_class
            })

            # Initialize character manager and extract characters
            char_manager = web.get_character_manager()
            characters = self._extract_characters_from_text(source_text, title, player_class)
            for character in characters:
                char_manager.add_character(character)
            
            # Add nodes to web
            for node_data in storyline_data:
                node = self._create_node_from_data(node_data)
                if node:
                    web.add_node(node)

                    # Add character information to node
                    self._add_characters_to_node(web, node, node_data)

                    # Add choices
                    for choice_data in node_data.get('choices', []):
                        choice = self._create_choice_from_data(choice_data)
                        if choice:
                            web.add_choice(node.id, choice)
            
            # Validate and calculate scores
            is_valid, errors = web.validate_structure()
            if not is_valid:
                logger.warning(f"Storyline validation issues: {errors}")
            
            web.calculate_scores()
            
            logger.info(f"Successfully imported storyline with {len(web.nodes)} nodes")
            return web
            
        except Exception as e:
            logger.error(f"Error importing storyline: {e}")
            return None
    
    def combine_storylines(self, storylines: List[StoryWeb], 
                         title: str = "Combined Story") -> Optional[StoryWeb]:
        """Combine multiple storylines into a story web"""
        try:
            logger.info(f"Combining {len(storylines)} storylines")
            
            if not storylines:
                return None
            
            # Create new combined web
            combined_web = StoryWeb(self.config)
            combined_web.metadata.update({
                "title": title,
                "source": "combined_storylines",
                "component_count": len(storylines)
            })
            
            # Add all nodes from storylines
            node_id_mapping = {}
            for i, storyline in enumerate(storylines):
                for node_id, node in storyline.nodes.items():
                    # Create new unique ID to avoid conflicts
                    new_id = f"story_{i}_{node_id}"
                    node_id_mapping[node_id] = new_id
                    
                    # Update node ID and add to combined web
                    node.id = new_id
                    combined_web.add_node(node)
            
            # Update choice target IDs and add choices
            for i, storyline in enumerate(storylines):
                for node_id, node in storyline.nodes.items():
                    new_node_id = f"story_{i}_{node_id}"
                    for choice in node.choices:
                        # Update target ID
                        old_target = choice.target_node_id
                        choice.target_node_id = f"story_{i}_{old_target}"
                        combined_web.add_choice(new_node_id, choice)
            
            # Generate intermediary nodes to connect storylines
            self._add_intermediary_nodes(combined_web, storylines)
            
            # Add side missions
            self._add_side_missions(combined_web)
            
            # Validate and calculate scores
            is_valid, errors = combined_web.validate_structure()
            if not is_valid:
                logger.warning(f"Combined web validation issues: {errors}")
            
            combined_web.calculate_scores()
            
            logger.info(f"Successfully combined storylines into web with {len(combined_web.nodes)} nodes")
            return combined_web
            
        except Exception as e:
            logger.error(f"Error combining storylines: {e}")
            return None
    
    def generate_standalone_web(self, theme: str, player_class: str = "Ranger") -> Optional[StoryWeb]:
        """Generate a standalone story web"""
        try:
            logger.info(f"Generating standalone web with theme: {theme}")
            
            web = StoryWeb(self.config)
            web.metadata.update({
                "title": f"Adventure: {theme}",
                "source": "generated",
                "theme": theme,
                "player_class": player_class
            })
            
            initial_inventory = self.inventory_config.get('initial_inventory', {"gold": 100})
            
            # Generate entry points
            num_entries = self.story_config.get('num_entry_points', 3)
            for i in range(num_entries):
                entry_node = self._generate_entry_node(theme, player_class, initial_inventory, i)
                if entry_node:
                    web.add_node(entry_node)
            
            # Generate story nodes
            max_nodes = self.story_config.get('max_nodes', 100)
            current_nodes = len(web.nodes)
            
            while current_nodes < max_nodes:
                # Select a random non-ending node to expand
                expandable_nodes = [
                    node for node in web.nodes.values() 
                    if not node.is_ending and len(node.choices) < self.story_config.get('max_choices_per_node', 5)
                ]
                
                if not expandable_nodes:
                    break
                
                # Generate new node connected to existing one
                parent_node = expandable_nodes[0]  # Simple selection for now
                new_node = self._generate_connected_node(parent_node, theme, player_class)
                
                if new_node:
                    web.add_node(new_node)
                    current_nodes += 1
                else:
                    break
            
            # Ensure minimum endings
            self._ensure_minimum_endings(web, theme, player_class)
            
            # Add side missions
            self._add_side_missions(web)
            
            # Validate and calculate scores
            is_valid, errors = web.validate_structure()
            if not is_valid:
                logger.warning(f"Generated web validation issues: {errors}")
            
            web.calculate_scores()
            
            logger.info(f"Successfully generated web with {len(web.nodes)} nodes")
            return web
            
        except Exception as e:
            logger.error(f"Error generating standalone web: {e}")
            return None
    
    def _parse_storyline_response(self, response_text: str) -> Optional[List[Dict]]:
        """Parse LLM response into storyline data"""
        try:
            # Find JSON array in response
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse storyline JSON: {e}")
            return None
    
    def _create_node_from_data(self, node_data: Dict) -> Optional[StoryNode]:
        """Create StoryNode from parsed data"""
        try:
            node_type = NodeType.ENTRY if node_data.get('is_entry') else NodeType.STORY
            if node_data.get('is_ending'):
                node_type = NodeType.ENDING
            
            ending_type = None
            if node_data.get('ending_type'):
                ending_type = EndingType(node_data['ending_type'])
            
            return StoryNode(
                id=node_data.get('id', str(uuid.uuid4())),
                text=node_data.get('text', ''),
                node_type=node_type,
                is_entry=node_data.get('is_entry', False),
                is_ending=node_data.get('is_ending', False),
                ending_type=ending_type,
                rating=node_data.get('rating', 'safe'),
                inventory_state=node_data.get('inventory_state', {}),
                class_context=node_data.get('class_context')
            )
        except Exception as e:
            logger.error(f"Error creating node from data: {e}")
            return None
    
    def _create_choice_from_data(self, choice_data: Dict) -> Optional[Choice]:
        """Create Choice from parsed data"""
        try:
            return Choice(
                id=choice_data.get('id', str(uuid.uuid4())),
                text=choice_data.get('text', ''),
                target_node_id=choice_data.get('target_node_id', ''),
                inventory_requirements=choice_data.get('inventory_requirements', {}),
                class_requirements=choice_data.get('class_requirements', []),
                inventory_changes=choice_data.get('inventory_changes', {}),
                is_premium=choice_data.get('is_premium', False),
                is_spicy=choice_data.get('is_spicy', False)
            )
        except Exception as e:
            logger.error(f"Error creating choice from data: {e}")
            return None
    
    def _generate_entry_node(self, theme: str, player_class: str, 
                           inventory: Dict[str, int], index: int) -> Optional[StoryNode]:
        """Generate an entry node"""
        try:
            context = f"Create entry point {index + 1} for a {theme} adventure. Player is a {player_class}."
            
            response = self.lmstudio.generate_story_node(
                context, inventory, player_class, "entry"
            )
            
            if response.success and response.metadata and 'parsed_json' in response.metadata:
                node_data = response.metadata['parsed_json']
                node_data['id'] = f"entry_{index}"
                node_data['is_entry'] = True
                return self._create_node_from_data(node_data)
            
            return None
        except Exception as e:
            logger.error(f"Error generating entry node: {e}")
            return None
    
    def _generate_connected_node(self, parent_node: StoryNode, theme: str, 
                               player_class: str) -> Optional[StoryNode]:
        """Generate a node connected to an existing node"""
        try:
            context = f"Continue from: {parent_node.text[:100]}... Theme: {theme}"
            
            response = self.lmstudio.generate_story_node(
                context, parent_node.inventory_state, player_class
            )
            
            if response.success and response.metadata and 'parsed_json' in response.metadata:
                node_data = response.metadata['parsed_json']
                node_data['id'] = str(uuid.uuid4())
                new_node = self._create_node_from_data(node_data)
                
                if new_node:
                    # Create choice from parent to new node
                    choice = Choice(
                        id=str(uuid.uuid4()),
                        text=f"Continue the adventure",
                        target_node_id=new_node.id
                    )
                    parent_node.choices.append(choice)
                
                return new_node
            
            return None
        except Exception as e:
            logger.error(f"Error generating connected node: {e}")
            return None
    
    def _add_intermediary_nodes(self, web: StoryWeb, storylines: List[StoryWeb]) -> None:
        """Add intermediary nodes to connect storylines"""
        try:
            # Generate connection points between storylines
            for i in range(len(storylines) - 1):
                storyline1 = storylines[i]
                storyline2 = storylines[i + 1]
                
                # Create summary of each storyline
                summary1 = f"Storyline about {storyline1.metadata.get('title', 'adventure')}"
                summary2 = f"Storyline about {storyline2.metadata.get('title', 'adventure')}"
                
                # Generate intermediary nodes
                response = self.lmstudio.generate_intermediary_nodes(
                    summary1, summary2, "Characters meet at a crossroads"
                )
                
                if response.success:
                    # Parse and add intermediary nodes
                    intermediary_data = self._parse_storyline_response(response.text)
                    if intermediary_data:
                        for node_data in intermediary_data:
                            node_data['id'] = f"intermediary_{uuid.uuid4()}"
                            node = self._create_node_from_data(node_data)
                            if node:
                                web.add_node(node)
                                
                                # Add choices
                                for choice_data in node_data.get('choices', []):
                                    choice = self._create_choice_from_data(choice_data)
                                    if choice:
                                        web.add_choice(node.id, choice)
        except Exception as e:
            logger.error(f"Error adding intermediary nodes: {e}")
    
    def _add_side_missions(self, web: StoryWeb) -> None:
        """Add side mission nodes to the web"""
        try:
            side_mission_prob = self.story_config.get('side_mission_probability', 0.3)
            
            # Add side missions to random nodes
            for node in list(web.nodes.values()):
                if not node.is_ending and len(node.choices) < 5:
                    if hash(node.id) % 100 < side_mission_prob * 100:
                        # Generate side mission
                        side_mission_node = StoryNode(
                            id=f"side_{uuid.uuid4()}",
                            text=f"A side path appears, offering a different adventure...",
                            node_type=NodeType.SIDE_MISSION,
                            inventory_state=node.inventory_state.copy()
                        )
                        
                        web.add_node(side_mission_node)
                        
                        # Add choice to side mission
                        side_choice = Choice(
                            id=str(uuid.uuid4()),
                            text="Explore the side path",
                            target_node_id=side_mission_node.id
                        )
                        
                        web.add_choice(node.id, side_choice)
        except Exception as e:
            logger.error(f"Error adding side missions: {e}")
    
    def _ensure_minimum_endings(self, web: StoryWeb, theme: str, player_class: str) -> None:
        """Ensure web has minimum required endings"""
        try:
            min_deaths = self.story_config.get('min_deaths', 3)
            min_successes = self.story_config.get('min_successes', 2)
            
            current_deaths = sum(1 for node in web.nodes.values() 
                               if node.ending_type == EndingType.DEATH)
            current_successes = sum(1 for node in web.nodes.values() 
                                  if node.ending_type == EndingType.SUCCESS)
            
            # Add death endings if needed
            while current_deaths < min_deaths:
                death_node = StoryNode(
                    id=f"death_{uuid.uuid4()}",
                    text=f"Your adventure ends in tragedy. The End.",
                    node_type=NodeType.ENDING,
                    is_ending=True,
                    ending_type=EndingType.DEATH
                )
                web.add_node(death_node)
                current_deaths += 1
            
            # Add success endings if needed
            while current_successes < min_successes:
                success_node = StoryNode(
                    id=f"success_{uuid.uuid4()}",
                    text=f"You have triumphed! Your {theme} adventure ends in glory. The End.",
                    node_type=NodeType.ENDING,
                    is_ending=True,
                    ending_type=EndingType.SUCCESS
                )
                web.add_node(success_node)
                current_successes += 1
                
        except Exception as e:
            logger.error(f"Error ensuring minimum endings: {e}")

    def _extract_characters_from_text(self, source_text: str, title: str, player_class: str) -> List[Character]:
        """Extract characters from source text using LLM"""
        try:
            logger.info("Extracting characters from source text")

            response = self.lmstudio.extract_characters_from_text(source_text, title)

            if not response.success:
                logger.warning("Failed to extract characters, creating default set")
                return self._create_default_characters(player_class)

            characters = []

            # Parse character data from LLM response
            try:
                # Find JSON array in response
                json_start = response.text.find('[')
                json_end = response.text.rfind(']') + 1

                if json_start >= 0 and json_end > json_start:
                    json_text = response.text[json_start:json_end]
                    character_data_list = json.loads(json_text)

                    for char_data in character_data_list:
                        character = self._create_character_from_data(char_data)
                        if character:
                            characters.append(character)

            except json.JSONDecodeError:
                logger.warning("Failed to parse character JSON, creating defaults")
                return self._create_default_characters(player_class)

            # Ensure we have a protagonist
            if not any(char.role == CharacterRole.PROTAGONIST for char in characters):
                protagonist = self._create_protagonist(player_class)
                characters.insert(0, protagonist)

            logger.info(f"Extracted {len(characters)} characters")
            return characters

        except Exception as e:
            logger.error(f"Error extracting characters: {e}")
            return self._create_default_characters(player_class)

    def _create_character_from_data(self, char_data: Dict[str, Any]) -> Optional[Character]:
        """Create Character from parsed data"""
        try:
            # Parse role
            role_str = char_data.get('role', 'neutral').lower()
            role = CharacterRole.NEUTRAL
            for r in CharacterRole:
                if r.value in role_str:
                    role = r
                    break

            # Create appearance
            appearance = CharacterAppearance(
                physical_description=char_data.get('appearance', ''),
                age_range=char_data.get('age', ''),
                hair_color=char_data.get('hair_color', ''),
                eye_color=char_data.get('eye_color', ''),
                clothing=char_data.get('clothing', ''),
                distinctive_features=char_data.get('distinctive_features', '')
            )

            # Create voice
            voice = CharacterVoice(
                voice_description=char_data.get('voice_description', ''),
                accent=char_data.get('accent', ''),
                speech_patterns=char_data.get('speech_patterns', []),
                vocabulary_style=char_data.get('vocabulary_style', 'casual')
            )

            # Create personality
            personality = CharacterPersonality(
                traits=char_data.get('traits', []),
                motivations=char_data.get('motivations', []),
                fears=char_data.get('fears', []),
                goals=char_data.get('goals', []),
                moral_alignment=char_data.get('moral_alignment', 'neutral'),
                background_story=char_data.get('background', ''),
                quirks=char_data.get('quirks', [])
            )

            character = Character(
                id=str(uuid.uuid4()),
                name=char_data.get('name', 'Unknown'),
                role=role,
                appearance=appearance,
                voice=voice,
                personality=personality,
                importance_level=char_data.get('importance', 3)
            )

            return character

        except Exception as e:
            logger.error(f"Error creating character from data: {e}")
            return None

    def _create_default_characters(self, player_class: str) -> List[Character]:
        """Create default character set"""
        characters = []

        # Protagonist
        protagonist = self._create_protagonist(player_class)
        characters.append(protagonist)

        # Generic antagonist
        antagonist = Character(
            id=str(uuid.uuid4()),
            name="The Shadow",
            role=CharacterRole.ANTAGONIST,
            appearance=CharacterAppearance(
                physical_description="A mysterious figure shrouded in darkness",
                distinctive_features="Glowing red eyes"
            ),
            personality=CharacterPersonality(
                traits=["cunning", "ruthless", "mysterious"],
                motivations=["power", "revenge"],
                moral_alignment="evil"
            ),
            importance_level=5
        )
        characters.append(antagonist)

        # Generic ally
        ally = Character(
            id=str(uuid.uuid4()),
            name="The Guide",
            role=CharacterRole.ALLY,
            appearance=CharacterAppearance(
                physical_description="A wise and weathered traveler"
            ),
            personality=CharacterPersonality(
                traits=["wise", "helpful", "experienced"],
                motivations=["helping others", "justice"],
                moral_alignment="good"
            ),
            importance_level=3
        )
        characters.append(ally)

        return characters

    def _create_protagonist(self, player_class: str) -> Character:
        """Create protagonist character based on player class"""
        class_descriptions = {
            "Mage": {
                "appearance": "A scholarly figure with robes and a staff",
                "traits": ["intelligent", "curious", "powerful"],
                "motivations": ["knowledge", "magical mastery"]
            },
            "Ranger": {
                "appearance": "A rugged outdoorsman with bow and leather armor",
                "traits": ["skilled", "independent", "observant"],
                "motivations": ["freedom", "protecting nature"]
            },
            "Charmer": {
                "appearance": "An attractive and well-dressed individual",
                "traits": ["charismatic", "persuasive", "social"],
                "motivations": ["influence", "relationships"]
            }
        }

        class_info = class_descriptions.get(player_class, class_descriptions["Ranger"])

        return Character(
            id=str(uuid.uuid4()),
            name="You",
            role=CharacterRole.PROTAGONIST,
            is_player_character=True,
            appearance=CharacterAppearance(
                physical_description=class_info["appearance"]
            ),
            personality=CharacterPersonality(
                traits=class_info["traits"],
                motivations=class_info["motivations"],
                moral_alignment="neutral"
            ),
            importance_level=5
        )

    def _add_characters_to_node(self, web: StoryWeb, node: StoryNode, node_data: Dict[str, Any]):
        """Add character information to a story node"""
        try:
            char_manager = web.get_character_manager()

            # Get characters mentioned in node text or data
            mentioned_characters = node_data.get('characters', [])

            # If no explicit character data, try to infer from text
            if not mentioned_characters:
                mentioned_characters = self._infer_characters_from_text(node.text, char_manager)

            # Add characters to node
            for char_info in mentioned_characters:
                if isinstance(char_info, str):
                    # Just character name/ID
                    character = char_manager.get_character_by_name(char_info)
                    if character:
                        web.add_character_to_node(node.id, character.id, {
                            "emotional_state": "neutral",
                            "location": node.location
                        })
                elif isinstance(char_info, dict):
                    # Character with state info
                    char_name = char_info.get('name', '')
                    character = char_manager.get_character_by_name(char_name)
                    if character:
                        web.add_character_to_node(node.id, character.id, {
                            "emotional_state": char_info.get('emotional_state', 'neutral'),
                            "location": node.location,
                            "status": char_info.get('status', 'alive')
                        })

            # Always include protagonist in story nodes
            protagonist = None
            for character in char_manager.characters.values():
                if character.is_player_character:
                    protagonist = character
                    break

            if protagonist and protagonist.id not in node.present_characters:
                web.add_character_to_node(node.id, protagonist.id, {
                    "emotional_state": "determined",
                    "location": node.location
                })

        except Exception as e:
            logger.error(f"Error adding characters to node: {e}")

    def _infer_characters_from_text(self, text: str, char_manager: CharacterManager) -> List[str]:
        """Infer which characters are present from node text"""
        present_characters = []

        # Simple name matching
        for character in char_manager.characters.values():
            if character.name.lower() in text.lower():
                present_characters.append(character.name)

        return present_characters
