"""
Scoring System - Calculates scores for death endings and player performance
Provides metrics for story difficulty and player achievement
"""

import logging
import networkx as nx
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ScoreType(Enum):
    """Types of scores that can be calculated"""
    DEATH_SCORE = "death_score"
    COMPLETION_SCORE = "completion_score"
    EFFICIENCY_SCORE = "efficiency_score"
    EXPLORATION_SCORE = "exploration_score"


@dataclass
class ScoreResult:
    """Result of a score calculation"""
    score_type: ScoreType
    value: float
    max_value: float
    percentage: float
    description: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class ScoringSystem:
    """Manages scoring calculations for CYOA stories"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.story_config = config.get('story_generation', {})
    
    def calculate_death_score(self, story_web, death_node_id: str) -> Optional[ScoreResult]:
        """
        Calculate score for a death ending
        Score = (shortest path to death / shortest path to furthest success) * 100
        """
        try:
            if death_node_id not in story_web.nodes:
                logger.error(f"Death node {death_node_id} not found")
                return None
            
            death_node = story_web.nodes[death_node_id]
            if death_node.ending_type.value != "death":
                logger.error(f"Node {death_node_id} is not a death ending")
                return None
            
            # Find shortest path from any entry to this death
            min_death_path = float('inf')
            death_entry_point = None
            
            for entry_id in story_web.entry_points:
                if nx.has_path(story_web.graph, entry_id, death_node_id):
                    path_length = nx.shortest_path_length(story_web.graph, entry_id, death_node_id)
                    if path_length < min_death_path:
                        min_death_path = path_length
                        death_entry_point = entry_id
            
            if min_death_path == float('inf'):
                logger.warning(f"Death node {death_node_id} is not reachable from any entry")
                return ScoreResult(
                    score_type=ScoreType.DEATH_SCORE,
                    value=0.0,
                    max_value=100.0,
                    percentage=0.0,
                    description="Unreachable death ending",
                    metadata={"reachable": False}
                )
            
            # Find longest path to any success ending
            max_success_path = 0
            furthest_success = None
            
            for node in story_web.nodes.values():
                if node.ending_type and node.ending_type.value == "success":
                    for entry_id in story_web.entry_points:
                        if nx.has_path(story_web.graph, entry_id, node.id):
                            path_length = nx.shortest_path_length(story_web.graph, entry_id, node.id)
                            if path_length > max_success_path:
                                max_success_path = path_length
                                furthest_success = node.id
            
            if max_success_path == 0:
                logger.warning("No reachable success endings found")
                return ScoreResult(
                    score_type=ScoreType.DEATH_SCORE,
                    value=0.0,
                    max_value=100.0,
                    percentage=0.0,
                    description="No success endings available",
                    metadata={"no_success_endings": True}
                )
            
            # Calculate score
            score_value = (min_death_path / max_success_path) * 100
            
            return ScoreResult(
                score_type=ScoreType.DEATH_SCORE,
                value=score_value,
                max_value=100.0,
                percentage=score_value,
                description=f"Death after {min_death_path} steps vs {max_success_path} to success",
                metadata={
                    "death_path_length": min_death_path,
                    "success_path_length": max_success_path,
                    "death_entry_point": death_entry_point,
                    "furthest_success": furthest_success
                }
            )
            
        except Exception as e:
            logger.error(f"Error calculating death score for {death_node_id}: {e}")
            return None
    
    def calculate_completion_score(self, story_web, path_taken: List[str]) -> Optional[ScoreResult]:
        """Calculate score based on story completion and path efficiency"""
        try:
            if not path_taken:
                return ScoreResult(
                    score_type=ScoreType.COMPLETION_SCORE,
                    value=0.0,
                    max_value=100.0,
                    percentage=0.0,
                    description="No path taken"
                )
            
            final_node_id = path_taken[-1]
            final_node = story_web.nodes.get(final_node_id)
            
            if not final_node or not final_node.is_ending:
                return ScoreResult(
                    score_type=ScoreType.COMPLETION_SCORE,
                    value=0.0,
                    max_value=100.0,
                    percentage=0.0,
                    description="Story not completed"
                )
            
            # Base score depends on ending type
            base_score = 0
            if final_node.ending_type.value == "success":
                base_score = 100
            elif final_node.ending_type.value == "death":
                base_score = 25
            else:  # neutral
                base_score = 50
            
            # Efficiency bonus/penalty
            entry_node_id = path_taken[0]
            if nx.has_path(story_web.graph, entry_node_id, final_node_id):
                optimal_length = nx.shortest_path_length(story_web.graph, entry_node_id, final_node_id)
                actual_length = len(path_taken) - 1
                
                if actual_length > 0:
                    efficiency = optimal_length / actual_length
                    efficiency_bonus = (efficiency - 1) * 20  # +/- 20 points max
                    base_score += efficiency_bonus
            
            # Clamp score to 0-100
            final_score = max(0, min(100, base_score))
            
            return ScoreResult(
                score_type=ScoreType.COMPLETION_SCORE,
                value=final_score,
                max_value=100.0,
                percentage=final_score,
                description=f"Completed with {final_node.ending_type.value} ending",
                metadata={
                    "ending_type": final_node.ending_type.value,
                    "path_length": len(path_taken) - 1,
                    "efficiency": efficiency if 'efficiency' in locals() else 1.0
                }
            )
            
        except Exception as e:
            logger.error(f"Error calculating completion score: {e}")
            return None
    
    def calculate_exploration_score(self, story_web, nodes_visited: List[str]) -> Optional[ScoreResult]:
        """Calculate score based on story exploration"""
        try:
            total_nodes = len(story_web.nodes)
            visited_nodes = len(set(nodes_visited))
            
            if total_nodes == 0:
                return ScoreResult(
                    score_type=ScoreType.EXPLORATION_SCORE,
                    value=0.0,
                    max_value=100.0,
                    percentage=0.0,
                    description="No nodes in story"
                )
            
            exploration_percentage = (visited_nodes / total_nodes) * 100
            
            # Bonus for visiting special nodes
            special_bonus = 0
            for node_id in nodes_visited:
                node = story_web.nodes.get(node_id)
                if node:
                    if node.node_type.value == "side_mission":
                        special_bonus += 5
                    elif node.is_premium:
                        special_bonus += 3
                    elif node.rating == "spicy":
                        special_bonus += 2
            
            final_score = min(100, exploration_percentage + special_bonus)
            
            return ScoreResult(
                score_type=ScoreType.EXPLORATION_SCORE,
                value=final_score,
                max_value=100.0,
                percentage=final_score,
                description=f"Explored {visited_nodes}/{total_nodes} nodes",
                metadata={
                    "nodes_visited": visited_nodes,
                    "total_nodes": total_nodes,
                    "special_bonus": special_bonus
                }
            )
            
        except Exception as e:
            logger.error(f"Error calculating exploration score: {e}")
            return None
    
    def calculate_all_death_scores(self, story_web) -> Dict[str, ScoreResult]:
        """Calculate scores for all death endings in the story"""
        death_scores = {}
        
        for node in story_web.nodes.values():
            if node.ending_type and node.ending_type.value == "death":
                score_result = self.calculate_death_score(story_web, node.id)
                if score_result:
                    death_scores[node.id] = score_result
        
        return death_scores
    
    def update_story_scores(self, story_web) -> None:
        """Update all score fields in the story web"""
        try:
            death_scores = self.calculate_all_death_scores(story_web)
            
            for node_id, score_result in death_scores.items():
                if node_id in story_web.nodes:
                    story_web.nodes[node_id].score = score_result.percentage
                    
                    # Update node metadata with score details
                    if not story_web.nodes[node_id].metadata:
                        story_web.nodes[node_id].metadata = {}
                    story_web.nodes[node_id].metadata['score_details'] = score_result.metadata
            
            logger.info(f"Updated scores for {len(death_scores)} death endings")
            
        except Exception as e:
            logger.error(f"Error updating story scores: {e}")
    
    def get_score_summary(self, story_web) -> Dict[str, Any]:
        """Get a summary of all scores in the story"""
        try:
            summary = {
                "total_nodes": len(story_web.nodes),
                "entry_points": len(story_web.entry_points),
                "endings": len(story_web.endings),
                "death_endings": 0,
                "success_endings": 0,
                "neutral_endings": 0,
                "scored_deaths": 0,
                "average_death_score": 0.0,
                "score_range": {"min": 100.0, "max": 0.0}
            }
            
            death_scores = []
            
            for node in story_web.nodes.values():
                if node.ending_type:
                    if node.ending_type.value == "death":
                        summary["death_endings"] += 1
                        if node.score is not None:
                            summary["scored_deaths"] += 1
                            death_scores.append(node.score)
                    elif node.ending_type.value == "success":
                        summary["success_endings"] += 1
                    else:
                        summary["neutral_endings"] += 1
            
            if death_scores:
                summary["average_death_score"] = sum(death_scores) / len(death_scores)
                summary["score_range"]["min"] = min(death_scores)
                summary["score_range"]["max"] = max(death_scores)
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating score summary: {e}")
            return {}
    
    def format_score_for_display(self, score_result: ScoreResult) -> str:
        """Format score result for display in story text"""
        if score_result.score_type == ScoreType.DEATH_SCORE:
            return f"Score: {score_result.percentage:.1f}%"
        elif score_result.score_type == ScoreType.COMPLETION_SCORE:
            return f"Final Score: {score_result.percentage:.1f}%"
        elif score_result.score_type == ScoreType.EXPLORATION_SCORE:
            return f"Exploration: {score_result.percentage:.1f}%"
        else:
            return f"Score: {score_result.percentage:.1f}%"
    
    def get_score_for_ending_text(self, node) -> str:
        """Get formatted score text for ending nodes"""
        if not node.is_ending:
            return ""
        
        if node.ending_type.value == "death" and node.score is not None:
            return f" Score: {node.score:.1f}%"
        
        return ""
    
    def validate_scoring_setup(self, story_web) -> Tuple[bool, List[str]]:
        """Validate that the story is properly set up for scoring"""
        errors = []
        
        # Check for entry points
        if not story_web.entry_points:
            errors.append("No entry points found for scoring")
        
        # Check for success endings
        success_endings = [
            node for node in story_web.nodes.values()
            if node.ending_type and node.ending_type.value == "success"
        ]
        if not success_endings:
            errors.append("No success endings found for scoring reference")
        
        # Check for death endings
        death_endings = [
            node for node in story_web.nodes.values()
            if node.ending_type and node.ending_type.value == "death"
        ]
        if not death_endings:
            errors.append("No death endings found to score")
        
        # Check connectivity
        for death_node in death_endings:
            reachable = False
            for entry_id in story_web.entry_points:
                if nx.has_path(story_web.graph, entry_id, death_node.id):
                    reachable = True
                    break
            if not reachable:
                errors.append(f"Death ending {death_node.id} is not reachable from any entry")
        
        return len(errors) == 0, errors
