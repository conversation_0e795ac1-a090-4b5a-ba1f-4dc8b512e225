"""
Inventory System - Manages player items and their effects on story choices
Tracks items, quantities, and their influence on available paths
"""

import logging
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ItemType(Enum):
    """Types of inventory items"""
    CURRENCY = "currency"
    WEAPON = "weapon"
    CONSUMABLE = "consumable"
    KEY_ITEM = "key_item"
    TOOL = "tool"
    ARMOR = "armor"
    MISC = "misc"


@dataclass
class Item:
    """Represents an inventory item"""
    id: str
    name: str
    item_type: ItemType
    description: str
    value: int = 0
    stackable: bool = True
    max_stack: int = 99
    effects: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.effects is None:
            self.effects = {}


@dataclass
class InventoryState:
    """Represents the current state of player inventory"""
    items: Dict[str, int]  # item_id -> quantity
    capacity: int = 50
    
    def __post_init__(self):
        if self.items is None:
            self.items = {}


class InventoryManager:
    """Manages inventory operations and item effects"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('inventory_system', {})
        self.items_catalog: Dict[str, Item] = {}
        self.initial_inventory = self.config.get('initial_inventory', {"gold": 100})
        self.max_types = self.config.get('max_inventory_types', 5)
        self.common_items = self.config.get('common_items', [])
        
        # Initialize item catalog
        self._initialize_item_catalog()
    
    def _initialize_item_catalog(self):
        """Initialize the catalog of available items"""
        # Currency
        self.items_catalog["gold"] = Item(
            id="gold",
            name="Gold Coins",
            item_type=ItemType.CURRENCY,
            description="Standard currency for transactions",
            value=1,
            stackable=True,
            max_stack=9999
        )
        
        # Weapons
        self.items_catalog["sword"] = Item(
            id="sword",
            name="Iron Sword",
            item_type=ItemType.WEAPON,
            description="A reliable weapon for combat",
            value=50,
            stackable=False,
            effects={"combat_bonus": 2}
        )
        
        self.items_catalog["bow"] = Item(
            id="bow",
            name="Hunting Bow",
            item_type=ItemType.WEAPON,
            description="Useful for ranged combat and hunting",
            value=40,
            stackable=False,
            effects={"ranged_bonus": 2, "hunting_bonus": 1}
        )
        
        self.items_catalog["staff"] = Item(
            id="staff",
            name="Wizard Staff",
            item_type=ItemType.WEAPON,
            description="Enhances magical abilities",
            value=60,
            stackable=False,
            effects={"magic_bonus": 3}
        )
        
        # Consumables
        self.items_catalog["potion"] = Item(
            id="potion",
            name="Health Potion",
            item_type=ItemType.CONSUMABLE,
            description="Restores health when consumed",
            value=20,
            stackable=True,
            max_stack=10,
            effects={"heal": 50}
        )
        
        self.items_catalog["food"] = Item(
            id="food",
            name="Travel Rations",
            item_type=ItemType.CONSUMABLE,
            description="Sustenance for long journeys",
            value=5,
            stackable=True,
            max_stack=20,
            effects={"sustenance": 1}
        )
        
        # Key Items
        self.items_catalog["key"] = Item(
            id="key",
            name="Mysterious Key",
            item_type=ItemType.KEY_ITEM,
            description="Opens locked doors and chests",
            value=0,
            stackable=True,
            max_stack=5,
            effects={"unlock": True}
        )
        
        self.items_catalog["map"] = Item(
            id="map",
            name="Ancient Map",
            item_type=ItemType.KEY_ITEM,
            description="Reveals hidden paths and locations",
            value=30,
            stackable=False,
            effects={"navigation": True, "secret_paths": True}
        )
        
        # Tools
        self.items_catalog["rope"] = Item(
            id="rope",
            name="Climbing Rope",
            item_type=ItemType.TOOL,
            description="Useful for climbing and traversal",
            value=15,
            stackable=True,
            max_stack=3,
            effects={"climbing": True}
        )
        
        self.items_catalog["torch"] = Item(
            id="torch",
            name="Torch",
            item_type=ItemType.TOOL,
            description="Provides light in dark places",
            value=2,
            stackable=True,
            max_stack=10,
            effects={"light": True}
        )
    
    def create_initial_inventory(self) -> InventoryState:
        """Create the starting inventory state"""
        return InventoryState(items=self.initial_inventory.copy())
    
    def get_item_info(self, item_id: str) -> Optional[Item]:
        """Get information about an item"""
        return self.items_catalog.get(item_id)
    
    def has_item(self, inventory: InventoryState, item_id: str, quantity: int = 1) -> bool:
        """Check if inventory has sufficient quantity of an item"""
        return inventory.items.get(item_id, 0) >= quantity
    
    def can_afford(self, inventory: InventoryState, cost: Dict[str, int]) -> bool:
        """Check if inventory can afford a cost"""
        for item_id, required_amount in cost.items():
            if not self.has_item(inventory, item_id, required_amount):
                return False
        return True
    
    def add_item(self, inventory: InventoryState, item_id: str, quantity: int = 1) -> bool:
        """Add items to inventory"""
        try:
            item_info = self.get_item_info(item_id)
            if not item_info:
                logger.warning(f"Unknown item: {item_id}")
                return False
            
            current_quantity = inventory.items.get(item_id, 0)
            
            # Check stack limits
            if item_info.stackable:
                max_add = item_info.max_stack - current_quantity
                quantity = min(quantity, max_add)
            else:
                if current_quantity > 0:
                    logger.warning(f"Cannot stack non-stackable item: {item_id}")
                    return False
                quantity = 1
            
            if quantity <= 0:
                return False
            
            inventory.items[item_id] = current_quantity + quantity
            logger.debug(f"Added {quantity} {item_id} to inventory")
            return True
            
        except Exception as e:
            logger.error(f"Error adding item {item_id}: {e}")
            return False
    
    def remove_item(self, inventory: InventoryState, item_id: str, quantity: int = 1) -> bool:
        """Remove items from inventory"""
        try:
            current_quantity = inventory.items.get(item_id, 0)
            
            if current_quantity < quantity:
                logger.warning(f"Insufficient {item_id}: have {current_quantity}, need {quantity}")
                return False
            
            new_quantity = current_quantity - quantity
            if new_quantity <= 0:
                del inventory.items[item_id]
            else:
                inventory.items[item_id] = new_quantity
            
            logger.debug(f"Removed {quantity} {item_id} from inventory")
            return True
            
        except Exception as e:
            logger.error(f"Error removing item {item_id}: {e}")
            return False
    
    def apply_transaction(self, inventory: InventoryState, 
                         costs: Dict[str, int], gains: Dict[str, int]) -> bool:
        """Apply a transaction (costs and gains) to inventory"""
        try:
            # Check if transaction is possible
            if not self.can_afford(inventory, costs):
                return False
            
            # Apply costs
            for item_id, cost in costs.items():
                if not self.remove_item(inventory, item_id, cost):
                    # Rollback on failure (simplified - in production, use proper transaction)
                    logger.error(f"Failed to apply cost for {item_id}")
                    return False
            
            # Apply gains
            for item_id, gain in gains.items():
                if not self.add_item(inventory, item_id, gain):
                    logger.warning(f"Failed to add gain for {item_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying transaction: {e}")
            return False
    
    def get_inventory_effects(self, inventory: InventoryState) -> Dict[str, Any]:
        """Get combined effects from all items in inventory"""
        combined_effects = {}
        
        for item_id, quantity in inventory.items.items():
            item_info = self.get_item_info(item_id)
            if item_info and item_info.effects:
                for effect, value in item_info.effects.items():
                    if effect in combined_effects:
                        # Combine effects (simple addition for now)
                        if isinstance(value, (int, float)):
                            combined_effects[effect] += value * quantity
                        else:
                            combined_effects[effect] = value
                    else:
                        if isinstance(value, (int, float)):
                            combined_effects[effect] = value * quantity
                        else:
                            combined_effects[effect] = value
        
        return combined_effects
    
    def get_available_items_for_class(self, player_class: str) -> List[str]:
        """Get items that are particularly useful for a class"""
        class_items = {
            "Mage": ["staff", "potion", "torch", "map"],
            "Ranger": ["bow", "rope", "food", "map"],
            "Charmer": ["gold", "potion", "key", "map"]
        }
        
        return class_items.get(player_class, self.common_items)
    
    def generate_shop_inventory(self, player_class: str, 
                              location_type: str = "general") -> Dict[str, Dict[str, Any]]:
        """Generate shop inventory based on context"""
        shop_items = {}
        
        # Get class-appropriate items
        class_items = self.get_available_items_for_class(player_class)
        
        # Add location-specific items
        location_items = {
            "general": ["food", "torch", "rope"],
            "weapon_shop": ["sword", "bow", "staff"],
            "magic_shop": ["staff", "potion", "map"],
            "tavern": ["food", "potion"]
        }
        
        available_items = class_items + location_items.get(location_type, [])
        
        for item_id in set(available_items):  # Remove duplicates
            item_info = self.get_item_info(item_id)
            if item_info:
                shop_items[item_id] = {
                    "name": item_info.name,
                    "price": item_info.value,
                    "description": item_info.description,
                    "in_stock": True
                }
        
        return shop_items
    
    def get_inventory_summary(self, inventory: InventoryState) -> str:
        """Get a human-readable summary of inventory"""
        if not inventory.items:
            return "Empty inventory"
        
        summary_parts = []
        for item_id, quantity in inventory.items.items():
            item_info = self.get_item_info(item_id)
            name = item_info.name if item_info else item_id
            if quantity > 1:
                summary_parts.append(f"{name} x{quantity}")
            else:
                summary_parts.append(name)
        
        return ", ".join(summary_parts)
    
    def validate_inventory_state(self, inventory: InventoryState) -> Tuple[bool, List[str]]:
        """Validate inventory state for consistency"""
        errors = []
        
        # Check for unknown items
        for item_id in inventory.items:
            if item_id not in self.items_catalog:
                errors.append(f"Unknown item in inventory: {item_id}")
        
        # Check stack limits
        for item_id, quantity in inventory.items.items():
            item_info = self.get_item_info(item_id)
            if item_info:
                if not item_info.stackable and quantity > 1:
                    errors.append(f"Non-stackable item has quantity > 1: {item_id}")
                elif quantity > item_info.max_stack:
                    errors.append(f"Item exceeds max stack: {item_id} ({quantity}/{item_info.max_stack})")
        
        # Check negative quantities
        for item_id, quantity in inventory.items.items():
            if quantity < 0:
                errors.append(f"Negative quantity for item: {item_id}")
        
        return len(errors) == 0, errors
    
    def to_dict(self, inventory: InventoryState) -> Dict[str, Any]:
        """Convert inventory state to dictionary"""
        return asdict(inventory)
    
    def from_dict(self, data: Dict[str, Any]) -> InventoryState:
        """Create inventory state from dictionary"""
        return InventoryState(**data)
