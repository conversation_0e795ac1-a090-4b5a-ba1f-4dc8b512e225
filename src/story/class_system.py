"""
Class System - Manages player classes and their effects on story choices
Defines class abilities, bonuses, and narrative modifications
"""

import logging
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


@dataclass
class ClassAbility:
    """Represents a class-specific ability"""
    id: str
    name: str
    description: str
    cooldown: int = 0
    resource_cost: Dict[str, int] = None
    effects: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.resource_cost is None:
            self.resource_cost = {}
        if self.effects is None:
            self.effects = {}


@dataclass
class PlayerClass:
    """Represents a player class with abilities and bonuses"""
    id: str
    name: str
    description: str
    abilities: List[ClassAbility]
    stat_bonuses: Dict[str, int] = None
    preferred_items: List[str] = None
    narrative_style: str = "neutral"
    spicy_tendency: float = 0.0  # 0.0 = never spicy, 1.0 = always spicy
    
    def __post_init__(self):
        if self.stat_bonuses is None:
            self.stat_bonuses = {}
        if self.preferred_items is None:
            self.preferred_items = []


class ClassManager:
    """Manages player classes and their interactions with the story"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('class_system', {})
        self.classes: Dict[str, PlayerClass] = {}
        self.available_classes = self.config.get('classes', ["Mage", "Ranger", "Charmer"])
        
        # Initialize class definitions
        self._initialize_classes()
    
    def _initialize_classes(self):
        """Initialize the available player classes"""
        
        # Mage Class
        mage_abilities = [
            ClassAbility(
                id="cast_spell",
                name="Cast Spell",
                description="Use magic to solve problems or attack enemies",
                effects={"magic_damage": 3, "problem_solving": 2}
            ),
            ClassAbility(
                id="enchant_item",
                name="Enchant Item",
                description="Enhance items with magical properties",
                resource_cost={"gold": 20},
                effects={"item_enhancement": True}
            ),
            ClassAbility(
                id="detect_magic",
                name="Detect Magic",
                description="Sense magical auras and hidden enchantments",
                effects={"magic_detection": True, "secret_discovery": 1}
            )
        ]
        
        self.classes["Mage"] = PlayerClass(
            id="mage",
            name="Mage",
            description="A wielder of arcane magic with powerful spells",
            abilities=mage_abilities,
            stat_bonuses={"intelligence": 3, "wisdom": 2, "strength": -1},
            preferred_items=["staff", "potion", "torch", "map"],
            narrative_style="intellectual",
            spicy_tendency=0.2
        )
        
        # Ranger Class
        ranger_abilities = [
            ClassAbility(
                id="track",
                name="Track",
                description="Follow tracks and find hidden paths",
                effects={"tracking": True, "pathfinding": 2}
            ),
            ClassAbility(
                id="hunt",
                name="Hunt",
                description="Hunt animals for food and materials",
                effects={"hunting": True, "survival": 2}
            ),
            ClassAbility(
                id="survival",
                name="Survival",
                description="Survive in wilderness and harsh conditions",
                effects={"wilderness_survival": True, "endurance": 2}
            )
        ]
        
        self.classes["Ranger"] = PlayerClass(
            id="ranger",
            name="Ranger",
            description="A skilled tracker and wilderness survivor",
            abilities=ranger_abilities,
            stat_bonuses={"dexterity": 3, "constitution": 2, "charisma": -1},
            preferred_items=["bow", "rope", "food", "map"],
            narrative_style="practical",
            spicy_tendency=0.1
        )
        
        # Charmer Class
        charmer_abilities = [
            ClassAbility(
                id="persuade",
                name="Persuade",
                description="Convince others through eloquent speech",
                effects={"persuasion": 3, "social_bonus": 2}
            ),
            ClassAbility(
                id="seduce",
                name="Seduce",
                description="Use charm and allure to influence others",
                effects={"seduction": True, "social_bonus": 3},
                resource_cost={"gold": 10}
            ),
            ClassAbility(
                id="negotiate",
                name="Negotiate",
                description="Broker deals and resolve conflicts peacefully",
                effects={"negotiation": 2, "conflict_resolution": True}
            )
        ]
        
        self.classes["Charmer"] = PlayerClass(
            id="charmer",
            name="Charmer",
            description="A charismatic individual who excels at social interaction",
            abilities=charmer_abilities,
            stat_bonuses={"charisma": 3, "intelligence": 1, "strength": -1},
            preferred_items=["gold", "potion", "key", "map"],
            narrative_style="social",
            spicy_tendency=0.7  # Charmers tend toward romantic content
        )
    
    def get_class(self, class_name: str) -> Optional[PlayerClass]:
        """Get class information by name"""
        return self.classes.get(class_name)
    
    def get_available_classes(self) -> List[str]:
        """Get list of available class names"""
        return list(self.classes.keys())
    
    def get_class_abilities(self, class_name: str) -> List[ClassAbility]:
        """Get abilities for a specific class"""
        player_class = self.get_class(class_name)
        return player_class.abilities if player_class else []
    
    def can_use_ability(self, class_name: str, ability_id: str, 
                       inventory: Dict[str, int]) -> bool:
        """Check if player can use a specific ability"""
        player_class = self.get_class(class_name)
        if not player_class:
            return False
        
        ability = next((a for a in player_class.abilities if a.id == ability_id), None)
        if not ability:
            return False
        
        # Check resource requirements
        for resource, cost in ability.resource_cost.items():
            if inventory.get(resource, 0) < cost:
                return False
        
        return True
    
    def get_class_choices_for_situation(self, class_name: str, situation: str,
                                      inventory: Dict[str, int]) -> List[Dict[str, Any]]:
        """Generate class-specific choices for a situation"""
        player_class = self.get_class(class_name)
        if not player_class:
            return []
        
        choices = []
        
        # Generate ability-based choices
        for ability in player_class.abilities:
            if self.can_use_ability(class_name, ability.id, inventory):
                choice_text = self._generate_ability_choice_text(ability, situation)
                if choice_text:
                    choices.append({
                        "text": choice_text,
                        "ability_id": ability.id,
                        "resource_cost": ability.resource_cost,
                        "class_specific": True,
                        "is_spicy": self._is_choice_spicy(ability, player_class)
                    })
        
        return choices
    
    def _generate_ability_choice_text(self, ability: ClassAbility, situation: str) -> str:
        """Generate choice text for an ability in a specific situation"""
        ability_templates = {
            "cast_spell": [
                "Cast a spell to resolve the situation",
                "Use magic to overcome the obstacle",
                "Weave arcane energy to solve the problem"
            ],
            "track": [
                "Use tracking skills to find a path",
                "Follow tracks to discover the truth",
                "Search for signs and clues"
            ],
            "persuade": [
                "Try to persuade them with words",
                "Use your charm to convince them",
                "Appeal to their better nature"
            ],
            "seduce": [
                "Use your allure to influence them",
                "Employ seductive charm",
                "Captivate them with your presence"
            ]
        }
        
        templates = ability_templates.get(ability.id, [ability.description])
        return templates[0] if templates else ability.description
    
    def _is_choice_spicy(self, ability: ClassAbility, player_class: PlayerClass) -> bool:
        """Determine if an ability choice should be marked as spicy"""
        spicy_abilities = ["seduce"]
        return ability.id in spicy_abilities or player_class.spicy_tendency > 0.5
    
    def get_class_narrative_style(self, class_name: str) -> str:
        """Get the narrative style for a class"""
        player_class = self.get_class(class_name)
        return player_class.narrative_style if player_class else "neutral"
    
    def get_class_stat_bonuses(self, class_name: str) -> Dict[str, int]:
        """Get stat bonuses for a class"""
        player_class = self.get_class(class_name)
        return player_class.stat_bonuses.copy() if player_class else {}
    
    def get_preferred_items(self, class_name: str) -> List[str]:
        """Get preferred items for a class"""
        player_class = self.get_class(class_name)
        return player_class.preferred_items.copy() if player_class else []
    
    def should_generate_spicy_content(self, class_name: str) -> bool:
        """Determine if spicy content should be generated for this class"""
        player_class = self.get_class(class_name)
        if not player_class:
            return False
        
        # Use class tendency and some randomness
        import random
        return random.random() < player_class.spicy_tendency
    
    def get_class_description_for_prompt(self, class_name: str) -> str:
        """Get class description formatted for LLM prompts"""
        player_class = self.get_class(class_name)
        if not player_class:
            return ""
        
        abilities_text = ", ".join([ability.name for ability in player_class.abilities])
        
        return f"""
Class: {player_class.name}
Description: {player_class.description}
Abilities: {abilities_text}
Narrative Style: {player_class.narrative_style}
Preferred Items: {', '.join(player_class.preferred_items)}
"""
    
    def apply_class_effects_to_choice(self, class_name: str, choice_data: Dict[str, Any],
                                    ability_id: Optional[str] = None) -> Dict[str, Any]:
        """Apply class-specific effects to a choice"""
        player_class = self.get_class(class_name)
        if not player_class:
            return choice_data
        
        # Apply class requirements
        choice_data["class_requirements"] = [class_name]
        
        # If using a specific ability, apply its effects
        if ability_id:
            ability = next((a for a in player_class.abilities if a.id == ability_id), None)
            if ability:
                choice_data["inventory_changes"] = choice_data.get("inventory_changes", {})
                for resource, cost in ability.resource_cost.items():
                    choice_data["inventory_changes"][resource] = -cost
                
                # Mark spicy if appropriate
                if self._is_choice_spicy(ability, player_class):
                    choice_data["is_spicy"] = True
        
        return choice_data
    
    def get_class_context_for_generation(self, class_name: str, 
                                       situation: str) -> Dict[str, Any]:
        """Get context information for story generation"""
        player_class = self.get_class(class_name)
        if not player_class:
            return {}
        
        return {
            "class_name": player_class.name,
            "class_description": player_class.description,
            "narrative_style": player_class.narrative_style,
            "available_abilities": [ability.name for ability in player_class.abilities],
            "spicy_tendency": player_class.spicy_tendency,
            "preferred_items": player_class.preferred_items
        }
    
    def validate_class_choice(self, class_name: str, choice_data: Dict[str, Any],
                            inventory: Dict[str, int]) -> Tuple[bool, List[str]]:
        """Validate that a choice is valid for the given class and inventory"""
        errors = []
        
        # Check if class exists
        if class_name not in self.classes:
            errors.append(f"Unknown class: {class_name}")
            return False, errors
        
        # Check class requirements
        class_requirements = choice_data.get("class_requirements", [])
        if class_requirements and class_name not in class_requirements:
            errors.append(f"Choice not available for class {class_name}")
        
        # Check ability requirements
        ability_id = choice_data.get("ability_id")
        if ability_id:
            if not self.can_use_ability(class_name, ability_id, inventory):
                errors.append(f"Cannot use ability {ability_id}")
        
        return len(errors) == 0, errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert class system to dictionary"""
        return {
            "classes": {
                class_id: {
                    **asdict(player_class),
                    "abilities": [asdict(ability) for ability in player_class.abilities]
                }
                for class_id, player_class in self.classes.items()
            },
            "available_classes": self.available_classes
        }
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any], data: Dict[str, Any]) -> 'ClassManager':
        """Create class manager from dictionary"""
        manager = cls(config)
        
        # Override with loaded data if provided
        if "classes" in data:
            manager.classes = {}
            for class_id, class_data in data["classes"].items():
                abilities_data = class_data.pop("abilities", [])
                player_class = PlayerClass(**class_data)
                
                # Reconstruct abilities
                player_class.abilities = []
                for ability_data in abilities_data:
                    ability = ClassAbility(**ability_data)
                    player_class.abilities.append(ability)
                
                manager.classes[class_id] = player_class
        
        if "available_classes" in data:
            manager.available_classes = data["available_classes"]
        
        return manager
