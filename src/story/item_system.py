"""
Item System - Manages story items, inventory, and item-based choices
Handles item effects, requirements, and story branching based on inventory
"""

import logging
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ItemType(Enum):
    """Types of items in the story"""
    WEAPON = "weapon"
    ARMOR = "armor"
    TOOL = "tool"
    CONSUMABLE = "consumable"
    KEY_ITEM = "key_item"
    CURRENCY = "currency"
    DOCUMENT = "document"
    MAGICAL = "magical"
    QUEST_ITEM = "quest_item"
    COLLECTIBLE = "collectible"


class ItemRarity(Enum):
    """Item rarity levels"""
    COMMON = "common"
    UNCOMMON = "uncommon"
    RARE = "rare"
    EPIC = "epic"
    LEGENDARY = "legendary"
    UNIQUE = "unique"


@dataclass
class ItemEffect:
    """Effect that an item can have"""
    effect_type: str  # "stat_boost", "unlock_choice", "story_branch", etc.
    effect_value: Any  # The value/data for the effect
    description: str = ""
    duration: Optional[str] = None  # "permanent", "temporary", "single_use"
    conditions: List[str] = field(default_factory=list)  # Conditions for effect to trigger


@dataclass
class Item:
    """Complete item definition"""
    id: str
    name: str
    item_type: ItemType
    description: str = ""
    
    # Properties
    rarity: ItemRarity = ItemRarity.COMMON
    value: int = 0  # Monetary value or importance
    weight: float = 0.0
    stackable: bool = False
    max_stack: int = 1
    
    # Effects and abilities
    effects: List[ItemEffect] = field(default_factory=list)
    requirements: Dict[str, Any] = field(default_factory=dict)  # Requirements to use
    
    # Story integration
    unlocks_choices: List[str] = field(default_factory=list)  # Choice IDs this item unlocks
    blocks_choices: List[str] = field(default_factory=list)  # Choice IDs this item blocks
    story_flags: List[str] = field(default_factory=list)  # Story flags this item sets
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    image_path: Optional[str] = None
    created_at: str = ""
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_at:
            from datetime import datetime
            self.created_at = datetime.now().isoformat()
    
    def can_use(self, character_stats: Dict[str, Any] = None) -> bool:
        """Check if item can be used based on requirements"""
        if not self.requirements:
            return True
        
        if not character_stats:
            return True  # No stats to check against
        
        for req_type, req_value in self.requirements.items():
            if req_type in character_stats:
                if character_stats[req_type] < req_value:
                    return False
        
        return True
    
    def get_choice_modifications(self) -> Dict[str, List[str]]:
        """Get choice modifications this item provides"""
        return {
            "unlocks": self.unlocks_choices,
            "blocks": self.blocks_choices
        }


@dataclass
class InventorySlot:
    """A slot in an inventory containing an item"""
    item_id: str
    quantity: int = 1
    acquired_at_node: Optional[str] = None
    notes: str = ""


class ItemManager:
    """Manages all items in a story"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.items: Dict[str, Item] = {}
        self.item_config = config.get('item_system', {})
        
        # Item tracking
        self.node_items: Dict[str, Set[str]] = {}  # node_id -> item_ids available
        self.choice_item_requirements: Dict[str, List[str]] = {}  # choice_id -> required_item_ids
        
        # Load default items
        self._load_default_items()
    
    def _load_default_items(self):
        """Load default item set"""
        default_items = [
            # Weapons
            Item(
                id="sword_basic", name="Iron Sword", item_type=ItemType.WEAPON,
                description="A sturdy iron sword. Reliable in combat.",
                value=50, weight=3.0, rarity=ItemRarity.COMMON,
                effects=[ItemEffect("combat_bonus", 2, "Increases combat effectiveness")],
                unlocks_choices=["fight_with_sword", "intimidate_with_weapon"]
            ),
            Item(
                id="bow_hunting", name="Hunting Bow", item_type=ItemType.WEAPON,
                description="A well-crafted bow for hunting and ranged combat.",
                value=75, weight=2.0, rarity=ItemRarity.COMMON,
                effects=[ItemEffect("ranged_bonus", 3, "Enables ranged attacks")],
                unlocks_choices=["shoot_arrow", "hunt_game"]
            ),
            
            # Tools
            Item(
                id="lockpicks", name="Lockpicks", item_type=ItemType.TOOL,
                description="A set of fine lockpicks for opening locked doors.",
                value=25, weight=0.1, rarity=ItemRarity.UNCOMMON,
                unlocks_choices=["pick_lock", "break_into_building"],
                tags=["stealth", "thievery"]
            ),
            Item(
                id="rope", name="Rope", item_type=ItemType.TOOL,
                description="50 feet of strong rope. Useful for climbing and binding.",
                value=10, weight=5.0, rarity=ItemRarity.COMMON,
                unlocks_choices=["climb_down", "tie_up_enemy", "cross_chasm"]
            ),
            
            # Key Items
            Item(
                id="royal_seal", name="Royal Seal", item_type=ItemType.KEY_ITEM,
                description="The official seal of the kingdom. Grants authority.",
                value=1000, weight=0.5, rarity=ItemRarity.UNIQUE,
                unlocks_choices=["command_guards", "enter_palace", "royal_decree"],
                story_flags=["has_royal_authority"]
            ),
            Item(
                id="ancient_key", name="Ancient Key", item_type=ItemType.KEY_ITEM,
                description="An ornate key of unknown origin. It hums with magical energy.",
                value=500, weight=0.2, rarity=ItemRarity.RARE,
                unlocks_choices=["open_ancient_door", "activate_mechanism"],
                tags=["magical", "ancient"]
            ),
            
            # Consumables
            Item(
                id="health_potion", name="Health Potion", item_type=ItemType.CONSUMABLE,
                description="A red potion that restores health when consumed.",
                value=20, weight=0.5, rarity=ItemRarity.COMMON,
                stackable=True, max_stack=5,
                effects=[ItemEffect("heal", 25, "Restores 25 health points", "single_use")]
            ),
            Item(
                id="magic_scroll", name="Magic Scroll", item_type=ItemType.CONSUMABLE,
                description="A scroll containing a powerful spell.",
                value=100, weight=0.1, rarity=ItemRarity.RARE,
                stackable=True, max_stack=3,
                effects=[ItemEffect("cast_spell", "fireball", "Casts fireball spell", "single_use")],
                unlocks_choices=["cast_fireball", "burn_obstacle"]
            ),
            
            # Currency
            Item(
                id="gold_coins", name="Gold Coins", item_type=ItemType.CURRENCY,
                description="Standard currency of the realm.",
                value=1, weight=0.01, rarity=ItemRarity.COMMON,
                stackable=True, max_stack=9999,
                unlocks_choices=["buy_item", "bribe_guard", "pay_toll"]
            ),
            
            # Documents
            Item(
                id="treasure_map", name="Treasure Map", item_type=ItemType.DOCUMENT,
                description="A weathered map showing the location of hidden treasure.",
                value=200, weight=0.1, rarity=ItemRarity.UNCOMMON,
                unlocks_choices=["follow_map", "find_treasure", "navigate_dungeon"],
                story_flags=["knows_treasure_location"]
            )
        ]
        
        for item in default_items:
            self.items[item.id] = item
        
        logger.info(f"Loaded {len(default_items)} default items")
    
    def create_item(self, name: str, item_type: ItemType, **kwargs) -> Item:
        """Create a new item"""
        item = Item(
            id=str(uuid.uuid4()),
            name=name,
            item_type=item_type,
            **kwargs
        )
        
        self.items[item.id] = item
        logger.info(f"Created item: {name} ({item_type.value})")
        
        return item
    
    def get_item(self, item_id: str) -> Optional[Item]:
        """Get item by ID"""
        return self.items.get(item_id)
    
    def get_item_by_name(self, name: str) -> Optional[Item]:
        """Get item by name"""
        for item in self.items.values():
            if item.name.lower() == name.lower():
                return item
        return None
    
    def get_items_by_type(self, item_type: ItemType) -> List[Item]:
        """Get all items of a specific type"""
        return [item for item in self.items.values() if item.item_type == item_type]
    
    def get_items_by_rarity(self, rarity: ItemRarity) -> List[Item]:
        """Get all items of a specific rarity"""
        return [item for item in self.items.values() if item.rarity == rarity]
    
    def add_item_to_node(self, node_id: str, item_id: str):
        """Add an item as available at a specific node"""
        if node_id not in self.node_items:
            self.node_items[node_id] = set()
        self.node_items[node_id].add(item_id)
    
    def get_items_at_node(self, node_id: str) -> List[Item]:
        """Get all items available at a node"""
        item_ids = self.node_items.get(node_id, set())
        return [self.items[item_id] for item_id in item_ids if item_id in self.items]
    
    def get_choice_modifications_for_inventory(self, inventory: List[str]) -> Dict[str, List[str]]:
        """Get choice modifications based on current inventory"""
        unlocked_choices = set()
        blocked_choices = set()
        
        for item_id in inventory:
            item = self.get_item(item_id)
            if item:
                unlocked_choices.update(item.unlocks_choices)
                blocked_choices.update(item.blocks_choices)
        
        return {
            "unlocks": list(unlocked_choices),
            "blocks": list(blocked_choices)
        }
    
    def export_item_data(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Export item data for external use"""
        item = self.get_item(item_id)
        if not item:
            return None
        
        return asdict(item)
    
    def import_item_data(self, item_data: Dict[str, Any]) -> bool:
        """Import item data"""
        try:
            # Convert enum strings back to enums
            if 'item_type' in item_data:
                item_data['item_type'] = ItemType(item_data['item_type'])
            
            if 'rarity' in item_data:
                item_data['rarity'] = ItemRarity(item_data['rarity'])
            
            # Reconstruct effects
            if 'effects' in item_data:
                effects = []
                for effect_data in item_data['effects']:
                    effects.append(ItemEffect(**effect_data))
                item_data['effects'] = effects
            
            item = Item(**item_data)
            self.items[item.id] = item
            
            logger.info(f"Imported item: {item.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing item data: {e}")
            return False
    
    def get_item_summary(self) -> Dict[str, Any]:
        """Get summary of all items"""
        summary = {
            "total_items": len(self.items),
            "by_type": {},
            "by_rarity": {},
            "item_list": []
        }
        
        # Count by type and rarity
        for item in self.items.values():
            item_type = item.item_type.value
            summary["by_type"][item_type] = summary["by_type"].get(item_type, 0) + 1
            
            rarity = item.rarity.value
            summary["by_rarity"][rarity] = summary["by_rarity"].get(rarity, 0) + 1
            
            # Item info
            summary["item_list"].append({
                "name": item.name,
                "type": item_type,
                "rarity": rarity,
                "value": item.value,
                "unlocks_choices": len(item.unlocks_choices)
            })
        
        # Sort by value
        summary["item_list"].sort(key=lambda x: x["value"], reverse=True)
        
        return summary

    def to_dict(self) -> Dict[str, Any]:
        """Convert item manager to dictionary for serialization"""
        return {
            "items": {item_id: asdict(item) for item_id, item in self.items.items()},
            "node_items": {node_id: list(item_ids) for node_id, item_ids in self.node_items.items()},
            "choice_item_requirements": self.choice_item_requirements
        }

    @classmethod
    def from_dict(cls, config: Dict[str, Any], data: Dict[str, Any]) -> 'ItemManager':
        """Create item manager from dictionary"""
        manager = cls(config)

        # Clear default items if loading from data
        manager.items.clear()

        # Load items
        for item_id, item_data in data.get("items", {}).items():
            # Convert enum strings back to enums
            if 'item_type' in item_data:
                item_data['item_type'] = ItemType(item_data['item_type'])
            if 'rarity' in item_data:
                item_data['rarity'] = ItemRarity(item_data['rarity'])

            # Reconstruct effects
            if 'effects' in item_data:
                effects = []
                for effect_data in item_data['effects']:
                    effects.append(ItemEffect(**effect_data))
                item_data['effects'] = effects

            item = Item(**item_data)
            manager.items[item_id] = item

        # Load node items
        manager.node_items = {
            node_id: set(item_ids)
            for node_id, item_ids in data.get("node_items", {}).items()
        }

        # Load choice requirements
        manager.choice_item_requirements = data.get("choice_item_requirements", {})

        return manager
