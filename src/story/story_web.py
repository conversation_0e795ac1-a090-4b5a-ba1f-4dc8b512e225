"""
Story Web - Core DAG structure for CYOA stories
Manages nodes, connections, and story flow with inventory and class systems
"""

import json
import uuid
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import networkx as nx
import logging

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Types of story nodes"""
    ENTRY = "entry"
    STORY = "story"
    CHOICE = "choice"
    ENDING = "ending"
    SIDE_MISSION = "side_mission"
    INTERMEDIARY = "intermediary"


class EndingType(Enum):
    """Types of story endings"""
    DEATH = "death"
    SUCCESS = "success"
    NEUTRAL = "neutral"


@dataclass
class Choice:
    """Represents a choice option in a story node"""
    id: str
    text: str
    target_node_id: str
    inventory_requirements: Dict[str, int] = None
    class_requirements: List[str] = None
    inventory_changes: Dict[str, int] = None
    is_premium: bool = False
    is_spicy: bool = False
    
    def __post_init__(self):
        if self.inventory_requirements is None:
            self.inventory_requirements = {}
        if self.class_requirements is None:
            self.class_requirements = []
        if self.inventory_changes is None:
            self.inventory_changes = {}


@dataclass
class StoryNode:
    """Represents a single node in the story web"""
    id: str
    text: str
    node_type: NodeType
    choices: List[Choice] = None
    is_entry: bool = False
    is_ending: bool = False
    ending_type: Optional[EndingType] = None
    is_premium: bool = False
    rating: str = "safe"  # "safe", "spicy"
    score: Optional[float] = None
    inventory_state: Dict[str, int] = None
    class_context: Optional[str] = None

    # Character tracking
    present_characters: Set[str] = None  # character IDs present at this node
    character_states: Dict[str, Dict[str, Any]] = None  # character_id -> state_data
    location: str = ""  # current location/setting

    metadata: Dict[str, Any] = None

    # Layer system for organization (like Photoshop layers)
    layer: str = "main"  # main, secondary, tertiary, background, etc.

    def __post_init__(self):
        if self.choices is None:
            self.choices = []
        if self.inventory_state is None:
            self.inventory_state = {}
        if self.present_characters is None:
            self.present_characters = set()
        if self.character_states is None:
            self.character_states = {}
        if self.metadata is None:
            self.metadata = {}


class StoryWeb:
    """
    Manages the complete story web as a directed acyclic graph (DAG)
    Handles inventory tracking, class systems, and story flow
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.nodes: Dict[str, StoryNode] = {}
        self.graph = nx.DiGraph()
        self.entry_points: List[str] = []
        self.endings: List[str] = []

        # Character management
        self.character_manager = None  # Will be initialized when needed

        # Item management
        self.item_manager = None  # Will be initialized when needed

        self.metadata = {
            "title": "",
            "description": "",
            "created_at": "",
            "version": "1.0"
        }
    
    def add_node(self, node: StoryNode) -> bool:
        """Add a node to the story web"""
        try:
            if node.id in self.nodes:
                logger.warning(f"Node {node.id} already exists, updating")
            
            self.nodes[node.id] = node
            self.graph.add_node(node.id, **asdict(node))
            
            # Track entry points and endings
            if node.is_entry:
                if node.id not in self.entry_points:
                    self.entry_points.append(node.id)
            
            if node.is_ending:
                if node.id not in self.endings:
                    self.endings.append(node.id)
            
            return True
        except Exception as e:
            logger.error(f"Error adding node {node.id}: {e}")
            return False
    
    def add_choice(self, from_node_id: str, choice: Choice) -> bool:
        """Add a choice connection between nodes"""
        try:
            if from_node_id not in self.nodes:
                logger.error(f"Source node {from_node_id} not found")
                return False
            
            if choice.target_node_id not in self.nodes:
                logger.error(f"Target node {choice.target_node_id} not found")
                return False
            
            # Add choice to source node
            self.nodes[from_node_id].choices.append(choice)
            
            # Add edge to graph
            self.graph.add_edge(
                from_node_id, 
                choice.target_node_id,
                choice_id=choice.id,
                choice_text=choice.text,
                is_premium=choice.is_premium,
                is_spicy=choice.is_spicy
            )
            
            return True
        except Exception as e:
            logger.error(f"Error adding choice from {from_node_id}: {e}")
            return False
    
    def validate_structure(self) -> Tuple[bool, List[str]]:
        """Validate the story web structure"""
        errors = []
        
        # Check for cycles
        if not nx.is_directed_acyclic_graph(self.graph):
            errors.append("Story web contains cycles")
        
        # Check entry points
        if len(self.entry_points) < self.config.get('num_entry_points', 1):
            errors.append(f"Insufficient entry points: {len(self.entry_points)}")
        
        # Check endings
        min_endings = self.config.get('min_endings', 1)
        if len(self.endings) < min_endings:
            errors.append(f"Insufficient endings: {len(self.endings)}")
        
        # Check connectivity
        for node_id in self.nodes:
            if not self.is_reachable_from_entries(node_id):
                errors.append(f"Node {node_id} is not reachable from entry points")
        
        # Check choice limits
        max_choices = self.config.get('max_choices_per_node', 5)
        for node in self.nodes.values():
            if len(node.choices) > max_choices:
                errors.append(f"Node {node.id} has too many choices: {len(node.choices)}")
        
        return len(errors) == 0, errors
    
    def is_reachable_from_entries(self, node_id: str) -> bool:
        """Check if a node is reachable from any entry point"""
        for entry_id in self.entry_points:
            if nx.has_path(self.graph, entry_id, node_id):
                return True
        return False
    
    def calculate_scores(self) -> None:
        """Calculate scores for death endings"""
        try:
            for node in self.nodes.values():
                if node.ending_type == EndingType.DEATH:
                    node.score = self._calculate_death_score(node.id)
        except Exception as e:
            logger.error(f"Error calculating scores: {e}")
    
    def _calculate_death_score(self, death_node_id: str) -> float:
        """Calculate score for a death ending"""
        try:
            # Find shortest path from any entry to this death
            min_death_path = float('inf')
            for entry_id in self.entry_points:
                if nx.has_path(self.graph, entry_id, death_node_id):
                    path_length = nx.shortest_path_length(self.graph, entry_id, death_node_id)
                    min_death_path = min(min_death_path, path_length)
            
            # Find longest path to any success ending
            max_success_path = 0
            for node in self.nodes.values():
                if node.ending_type == EndingType.SUCCESS:
                    for entry_id in self.entry_points:
                        if nx.has_path(self.graph, entry_id, node.id):
                            path_length = nx.shortest_path_length(self.graph, entry_id, node.id)
                            max_success_path = max(max_success_path, path_length)
            
            if max_success_path == 0:
                return 0.0
            
            return (min_death_path / max_success_path) * 100
        except Exception as e:
            logger.error(f"Error calculating death score for {death_node_id}: {e}")
            return 0.0
    
    def get_available_choices(self, node_id: str, inventory: Dict[str, int], 
                            player_class: str) -> List[Choice]:
        """Get choices available to player based on inventory and class"""
        if node_id not in self.nodes:
            return []
        
        available_choices = []
        for choice in self.nodes[node_id].choices:
            # Check inventory requirements
            if self._meets_inventory_requirements(inventory, choice.inventory_requirements):
                # Check class requirements
                if not choice.class_requirements or player_class in choice.class_requirements:
                    available_choices.append(choice)
        
        return available_choices
    
    def _meets_inventory_requirements(self, inventory: Dict[str, int], 
                                    requirements: Dict[str, int]) -> bool:
        """Check if inventory meets requirements"""
        for item, required_amount in requirements.items():
            if inventory.get(item, 0) < required_amount:
                return False
        return True
    
    def apply_choice_effects(self, inventory: Dict[str, int], 
                           choice: Choice) -> Dict[str, int]:
        """Apply inventory changes from a choice"""
        new_inventory = inventory.copy()
        for item, change in choice.inventory_changes.items():
            new_inventory[item] = new_inventory.get(item, 0) + change
            # Ensure non-negative values
            new_inventory[item] = max(0, new_inventory[item])
        return new_inventory

    def get_available_layers(self) -> List[str]:
        """Get all available layers in the story"""
        layers = set()
        for node in self.nodes.values():
            layers.add(node.layer)
        return sorted(list(layers))

    def get_nodes_by_layer(self, layer: str) -> List[StoryNode]:
        """Get all nodes in a specific layer"""
        return [node for node in self.nodes.values() if node.layer == layer]

    def set_node_layer(self, node_id: str, layer: str) -> bool:
        """Set the layer for a specific node"""
        if node_id in self.nodes:
            self.nodes[node_id].layer = layer
            return True
        return False

    def get_layer_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for each layer"""
        layer_stats = {}

        for layer in self.get_available_layers():
            nodes = self.get_nodes_by_layer(layer)
            layer_stats[layer] = {
                "node_count": len(nodes),
                "entry_points": len([n for n in nodes if n.is_entry]),
                "endings": len([n for n in nodes if n.is_ending]),
                "premium_nodes": len([n for n in nodes if n.is_premium]),
                "spicy_nodes": len([n for n in nodes if n.rating == "spicy"])
            }

        return layer_stats

    def get_character_manager(self):
        """Get or create character manager"""
        if self.character_manager is None:
            from .character_system import CharacterManager
            self.character_manager = CharacterManager(self.config)
        return self.character_manager

    def get_item_manager(self):
        """Get or create item manager"""
        if self.item_manager is None:
            from .item_system import ItemManager
            self.item_manager = ItemManager(self.config)
        return self.item_manager

    def add_character_to_node(self, node_id: str, character_id: str,
                            character_state: Dict[str, Any] = None) -> bool:
        """Add a character to a specific node"""
        try:
            if node_id not in self.nodes:
                logger.error(f"Node {node_id} not found")
                return False

            node = self.nodes[node_id]
            node.present_characters.add(character_id)

            if character_state:
                node.character_states[character_id] = character_state

            # Update character manager if available
            char_manager = self.get_character_manager()
            char_manager.update_character_state(character_id, node_id, {
                "is_present": True,
                "location": node.location,
                **character_state
            })

            logger.debug(f"Added character {character_id} to node {node_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding character to node: {e}")
            return False

    def remove_character_from_node(self, node_id: str, character_id: str) -> bool:
        """Remove a character from a specific node"""
        try:
            if node_id not in self.nodes:
                logger.error(f"Node {node_id} not found")
                return False

            node = self.nodes[node_id]
            node.present_characters.discard(character_id)
            node.character_states.pop(character_id, None)

            # Update character manager if available
            char_manager = self.get_character_manager()
            char_manager.update_character_state(character_id, node_id, {
                "is_present": False
            })

            logger.debug(f"Removed character {character_id} from node {node_id}")
            return True

        except Exception as e:
            logger.error(f"Error removing character from node: {e}")
            return False

    def get_characters_at_node(self, node_id: str) -> List[str]:
        """Get list of character IDs present at a node"""
        if node_id not in self.nodes:
            return []
        return list(self.nodes[node_id].present_characters)

    def get_character_context_for_generation(self, node_id: str) -> Dict[str, Any]:
        """Get character context for story generation"""
        try:
            char_manager = self.get_character_manager()
            return char_manager.get_character_context_for_node(node_id)
        except Exception as e:
            logger.error(f"Error getting character context: {e}")
            return {"present_characters": []}

    def validate_character_consistency(self) -> Tuple[bool, List[str]]:
        """Validate character consistency across the story"""
        try:
            char_manager = self.get_character_manager()
            issues = char_manager.analyze_character_consistency(self)

            all_issues = []
            for character_name, char_issues in issues.items():
                for issue in char_issues:
                    all_issues.append(f"{character_name}: {issue}")

            return len(all_issues) == 0, all_issues

        except Exception as e:
            logger.error(f"Error validating character consistency: {e}")
            return False, [f"Character validation error: {e}"]

    def to_dict(self) -> Dict[str, Any]:
        """Convert story web to dictionary for serialization"""
        def serialize_node(node):
            node_dict = asdict(node)
            # Convert enums to strings
            if 'node_type' in node_dict and node_dict['node_type']:
                node_dict['node_type'] = node_dict['node_type'].value
            if 'ending_type' in node_dict and node_dict['ending_type']:
                node_dict['ending_type'] = node_dict['ending_type'].value
            # Convert sets to lists for JSON serialization
            if 'present_characters' in node_dict and isinstance(node_dict['present_characters'], set):
                node_dict['present_characters'] = list(node_dict['present_characters'])
            return node_dict

        story_data = {
            "metadata": self.metadata,
            "config": self.config,
            "entry_points": self.entry_points,
            "endings": self.endings,
            "nodes": {
                node_id: {
                    **serialize_node(node),
                    "choices": [asdict(choice) for choice in node.choices]
                }
                for node_id, node in self.nodes.items()
            }
        }

        # Add character data if character manager exists
        if self.character_manager:
            story_data["characters"] = self.character_manager.to_dict()

        # Add item data if item manager exists
        if self.item_manager:
            story_data["items"] = self.item_manager.to_dict()

        return story_data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryWeb':
        """Create story web from dictionary"""
        web = cls(data.get("config", {}))
        web.metadata = data.get("metadata", {})
        web.entry_points = data.get("entry_points", [])
        web.endings = data.get("endings", [])
        
        # Reconstruct nodes
        for node_id, node_data in data.get("nodes", {}).items():
            choices_data = node_data.pop("choices", [])
            
            # Convert enum strings back to enums
            if "node_type" in node_data:
                node_data["node_type"] = NodeType(node_data["node_type"])
            if "ending_type" in node_data and node_data["ending_type"]:
                node_data["ending_type"] = EndingType(node_data["ending_type"])
            
            node = StoryNode(**node_data)

            # Convert lists back to sets for present_characters
            if hasattr(node, 'present_characters') and isinstance(node.present_characters, list):
                node.present_characters = set(node.present_characters)

            # Reconstruct choices
            for choice_data in choices_data:
                choice = Choice(**choice_data)
                node.choices.append(choice)

            web.add_node(node)

        # Rebuild graph connections
        for node in web.nodes.values():
            for choice in node.choices:
                web.graph.add_edge(node.id, choice.target_node_id)

        # Load character data if present
        if "characters" in data:
            from .character_system import CharacterManager
            web.character_manager = CharacterManager.from_dict(web.config, data["characters"])

        # Load item data if present
        if "items" in data:
            from .item_system import ItemManager
            web.item_manager = ItemManager.from_dict(web.config, data["items"])

        return web
    
    def save_to_file(self, filepath: str) -> bool:
        """Save story web to JSON file"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"Story web saved to {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error saving story web to {filepath}: {e}")
            return False
    
    @classmethod
    def load_from_file(cls, filepath: str) -> Optional['StoryWeb']:
        """Load story web from JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            web = cls.from_dict(data)
            logger.info(f"Story web loaded from {filepath}")
            return web
        except Exception as e:
            logger.error(f"Error loading story web from {filepath}: {e}")
            return None
