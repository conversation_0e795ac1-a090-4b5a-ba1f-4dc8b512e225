"""
Rating System - Manages content ratings for CYOA stories
Handles "Spicy" content detection, inheritance, and warnings
"""

import logging
import re
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ContentRating(Enum):
    """Content rating levels"""
    SAFE = "safe"
    SPICY = "spicy"
    MATURE = "mature"  # Future expansion
    EXPLICIT = "explicit"  # Future expansion


@dataclass
class RatingAnalysis:
    """Result of content rating analysis"""
    rating: ContentRating
    confidence: float
    reasons: List[str]
    keywords_found: List[str]
    context_factors: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context_factors is None:
            self.context_factors = {}


class RatingSystem:
    """Manages content rating for CYOA stories"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('rating_system', {})
        self.spicy_keywords = self.config.get('spicy_keywords', [
            "romance", "intimate", "seductive", "passionate", "alluring",
            "sensual", "desire", "attraction", "kiss", "embrace", "caress",
            "tempting", "enchanting", "captivating", "charming", "flirtatious"
        ])
        self.spicy_inheritance = self.config.get('spicy_inheritance', True)
        self.content_warnings = self.config.get('content_warnings', True)
        
        # Compile regex patterns for efficient matching
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for content detection"""
        self.spicy_patterns = []
        
        for keyword in self.spicy_keywords:
            # Create pattern that matches word boundaries
            pattern = re.compile(r'\b' + re.escape(keyword) + r'\b', re.IGNORECASE)
            self.spicy_patterns.append((keyword, pattern))
        
        # Additional contextual patterns
        self.romantic_patterns = [
            re.compile(r'\b(love|romance|romantic)\b', re.IGNORECASE),
            re.compile(r'\b(kiss|kissing|kissed)\b', re.IGNORECASE),
            re.compile(r'\b(embrace|embracing|embraced)\b', re.IGNORECASE),
            re.compile(r'\b(seduce|seducing|seduced|seduction)\b', re.IGNORECASE),
            re.compile(r'\b(intimate|intimacy)\b', re.IGNORECASE),
            re.compile(r'\b(passionate|passion)\b', re.IGNORECASE)
        ]
        
        self.suggestive_patterns = [
            re.compile(r'\b(bedroom|bed chamber)\b', re.IGNORECASE),
            re.compile(r'\b(undress|undressing|undressed)\b', re.IGNORECASE),
            re.compile(r'\b(naked|nude|bare)\b', re.IGNORECASE),
            re.compile(r'\b(desire|desires|desiring)\b', re.IGNORECASE),
            re.compile(r'\b(tempt|tempting|tempted|temptation)\b', re.IGNORECASE)
        ]
    
    def analyze_content(self, text: str, context: Dict[str, Any] = None) -> RatingAnalysis:
        """Analyze text content for rating"""
        try:
            if not text:
                return RatingAnalysis(
                    rating=ContentRating.SAFE,
                    confidence=1.0,
                    reasons=["Empty content"],
                    keywords_found=[]
                )
            
            context = context or {}
            keywords_found = []
            reasons = []
            spicy_score = 0.0
            
            # Check for spicy keywords
            for keyword, pattern in self.spicy_patterns:
                matches = pattern.findall(text)
                if matches:
                    keywords_found.extend(matches)
                    spicy_score += len(matches) * 0.2
                    reasons.append(f"Contains keyword: {keyword}")
            
            # Check romantic patterns
            romantic_matches = 0
            for pattern in self.romantic_patterns:
                matches = pattern.findall(text)
                if matches:
                    romantic_matches += len(matches)
                    keywords_found.extend(matches)
                    reasons.append(f"Romantic content detected")
            
            spicy_score += romantic_matches * 0.3
            
            # Check suggestive patterns
            suggestive_matches = 0
            for pattern in self.suggestive_patterns:
                matches = pattern.findall(text)
                if matches:
                    suggestive_matches += len(matches)
                    keywords_found.extend(matches)
                    reasons.append(f"Suggestive content detected")
            
            spicy_score += suggestive_matches * 0.4
            
            # Context factors
            player_class = context.get('player_class', '')
            if player_class.lower() == 'charmer':
                spicy_score += 0.1
                reasons.append("Charmer class context")
            
            choice_type = context.get('choice_type', '')
            if 'seduce' in choice_type.lower():
                spicy_score += 0.5
                reasons.append("Seduction choice context")
            
            # Determine rating
            if spicy_score >= 0.5:
                rating = ContentRating.SPICY
                confidence = min(1.0, spicy_score)
            else:
                rating = ContentRating.SAFE
                confidence = 1.0 - spicy_score
            
            return RatingAnalysis(
                rating=rating,
                confidence=confidence,
                reasons=reasons,
                keywords_found=list(set(keywords_found)),  # Remove duplicates
                context_factors={
                    "spicy_score": spicy_score,
                    "romantic_matches": romantic_matches,
                    "suggestive_matches": suggestive_matches,
                    "player_class": player_class
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing content rating: {e}")
            return RatingAnalysis(
                rating=ContentRating.SAFE,
                confidence=0.0,
                reasons=[f"Analysis error: {e}"],
                keywords_found=[]
            )
    
    def rate_story_node(self, node, context: Dict[str, Any] = None) -> RatingAnalysis:
        """Rate a story node's content"""
        try:
            # Combine node text and choice texts for analysis
            full_text = node.text
            
            for choice in node.choices:
                full_text += " " + choice.text
            
            # Add context from node
            node_context = context or {}
            node_context.update({
                "node_type": node.node_type.value,
                "is_ending": node.is_ending,
                "class_context": node.class_context
            })
            
            analysis = self.analyze_content(full_text, node_context)
            
            # Override with explicit rating if set
            if hasattr(node, 'rating') and node.rating:
                if node.rating == "spicy":
                    analysis.rating = ContentRating.SPICY
                    analysis.reasons.append("Explicitly marked as spicy")
                elif node.rating == "safe":
                    analysis.rating = ContentRating.SAFE
                    analysis.reasons.append("Explicitly marked as safe")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error rating story node: {e}")
            return RatingAnalysis(
                rating=ContentRating.SAFE,
                confidence=0.0,
                reasons=[f"Rating error: {e}"],
                keywords_found=[]
            )
    
    def apply_rating_inheritance(self, story_web) -> None:
        """Apply rating inheritance through the story web"""
        if not self.spicy_inheritance:
            return
        
        try:
            # Find all spicy nodes
            spicy_nodes = set()
            for node in story_web.nodes.values():
                analysis = self.rate_story_node(node)
                if analysis.rating == ContentRating.SPICY:
                    spicy_nodes.add(node.id)
                    node.rating = "spicy"
            
            # Propagate spicy rating to connected nodes
            changed = True
            iterations = 0
            max_iterations = len(story_web.nodes)  # Prevent infinite loops
            
            while changed and iterations < max_iterations:
                changed = False
                iterations += 1
                
                for node_id in list(spicy_nodes):
                    node = story_web.nodes[node_id]
                    
                    # Check choices from this node
                    for choice in node.choices:
                        target_node = story_web.nodes.get(choice.target_node_id)
                        if target_node and target_node.id not in spicy_nodes:
                            # Inherit spicy rating if choice is spicy
                            if choice.is_spicy or "seduce" in choice.text.lower():
                                spicy_nodes.add(target_node.id)
                                target_node.rating = "spicy"
                                changed = True
                    
                    # Check nodes that lead to this node
                    for other_node in story_web.nodes.values():
                        for choice in other_node.choices:
                            if choice.target_node_id == node_id and choice.is_spicy:
                                if other_node.id not in spicy_nodes:
                                    spicy_nodes.add(other_node.id)
                                    other_node.rating = "spicy"
                                    changed = True
            
            logger.info(f"Applied rating inheritance to {len(spicy_nodes)} nodes")
            
        except Exception as e:
            logger.error(f"Error applying rating inheritance: {e}")
    
    def get_content_warnings(self, story_web) -> List[str]:
        """Generate content warnings for the story"""
        warnings = []
        
        if not self.content_warnings:
            return warnings
        
        try:
            spicy_count = 0
            romantic_themes = False
            suggestive_content = False
            
            for node in story_web.nodes.values():
                analysis = self.rate_story_node(node)
                
                if analysis.rating == ContentRating.SPICY:
                    spicy_count += 1
                
                if any("romantic" in reason.lower() for reason in analysis.reasons):
                    romantic_themes = True
                
                if any("suggestive" in reason.lower() for reason in analysis.reasons):
                    suggestive_content = True
            
            if spicy_count > 0:
                warnings.append(f"Contains {spicy_count} nodes with spicy content")
            
            if romantic_themes:
                warnings.append("Contains romantic themes")
            
            if suggestive_content:
                warnings.append("Contains suggestive content")
            
            # Check for class-specific warnings
            charmer_nodes = [
                node for node in story_web.nodes.values()
                if node.class_context == "Charmer"
            ]
            if charmer_nodes:
                warnings.append("Contains content tailored for Charmer class (may include romantic elements)")
            
        except Exception as e:
            logger.error(f"Error generating content warnings: {e}")
            warnings.append("Unable to analyze content for warnings")
        
        return warnings
    
    def format_rating_for_choice(self, choice) -> str:
        """Format rating information for choice display"""
        if choice.is_spicy:
            return "[Spicy] "
        return ""
    
    def format_rating_for_node(self, node) -> str:
        """Format rating information for node display"""
        if hasattr(node, 'rating') and node.rating == "spicy":
            return "[Spicy] "
        return ""
    
    def get_rating_summary(self, story_web) -> Dict[str, Any]:
        """Get a summary of ratings in the story"""
        try:
            summary = {
                "total_nodes": len(story_web.nodes),
                "safe_nodes": 0,
                "spicy_nodes": 0,
                "spicy_choices": 0,
                "content_warnings": self.get_content_warnings(story_web),
                "rating_distribution": {}
            }
            
            for node in story_web.nodes.values():
                analysis = self.rate_story_node(node)
                rating_str = analysis.rating.value
                
                summary["rating_distribution"][rating_str] = summary["rating_distribution"].get(rating_str, 0) + 1
                
                if analysis.rating == ContentRating.SAFE:
                    summary["safe_nodes"] += 1
                elif analysis.rating == ContentRating.SPICY:
                    summary["spicy_nodes"] += 1
                
                # Count spicy choices
                for choice in node.choices:
                    if choice.is_spicy:
                        summary["spicy_choices"] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating rating summary: {e}")
            return {}
    
    def validate_ratings(self, story_web) -> Tuple[bool, List[str]]:
        """Validate rating consistency in the story"""
        errors = []
        
        try:
            for node in story_web.nodes.values():
                # Check for inconsistent ratings
                analysis = self.rate_story_node(node)
                
                if hasattr(node, 'rating'):
                    if node.rating == "spicy" and analysis.rating == ContentRating.SAFE:
                        if analysis.confidence > 0.8:
                            errors.append(f"Node {node.id} marked as spicy but content appears safe")
                    elif node.rating == "safe" and analysis.rating == ContentRating.SPICY:
                        if analysis.confidence > 0.8:
                            errors.append(f"Node {node.id} marked as safe but content appears spicy")
                
                # Check choice consistency
                for choice in node.choices:
                    if choice.is_spicy:
                        choice_analysis = self.analyze_content(choice.text)
                        if choice_analysis.rating == ContentRating.SAFE and choice_analysis.confidence > 0.8:
                            errors.append(f"Choice '{choice.text}' marked as spicy but content appears safe")
            
        except Exception as e:
            logger.error(f"Error validating ratings: {e}")
            errors.append(f"Rating validation error: {e}")
        
        return len(errors) == 0, errors
    
    def update_story_ratings(self, story_web) -> None:
        """Update all rating fields in the story web"""
        try:
            for node in story_web.nodes.values():
                analysis = self.rate_story_node(node)
                node.rating = analysis.rating.value
                
                # Update node metadata with rating details
                if not node.metadata:
                    node.metadata = {}
                node.metadata['rating_analysis'] = {
                    "confidence": analysis.confidence,
                    "reasons": analysis.reasons,
                    "keywords_found": analysis.keywords_found
                }
                
                # Update choice ratings
                for choice in node.choices:
                    choice_analysis = self.analyze_content(choice.text)
                    if choice_analysis.rating == ContentRating.SPICY:
                        choice.is_spicy = True
            
            # Apply inheritance
            self.apply_rating_inheritance(story_web)
            
            logger.info("Updated ratings for all nodes and choices")
            
        except Exception as e:
            logger.error(f"Error updating story ratings: {e}")
