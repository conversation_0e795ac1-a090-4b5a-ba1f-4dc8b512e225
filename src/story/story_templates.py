"""
Story Templates - Pre-built story structures and examples
Provides templates for common story types and genres
"""

import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

from .story_web import StoryWeb, StoryNode, Choice, NodeType, EndingType
from .character_system import CharacterManager, Character, CharacterRole, CharacterAppearance, CharacterPersonality

logger = logging.getLogger(__name__)


class StoryTemplate:
    """A story template with structure and content"""
    
    def __init__(self, name: str, description: str, genre: str, 
                 difficulty: str = "beginner", estimated_time: str = "10-15 min"):
        self.name = name
        self.description = description
        self.genre = genre
        self.difficulty = difficulty
        self.estimated_time = estimated_time
        self.nodes = []
        self.characters = []
        self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary"""
        return {
            'name': self.name,
            'description': self.description,
            'genre': self.genre,
            'difficulty': self.difficulty,
            'estimated_time': self.estimated_time,
            'nodes': self.nodes,
            'characters': self.characters,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryTemplate':
        """Create template from dictionary"""
        template = cls(
            name=data['name'],
            description=data['description'],
            genre=data['genre'],
            difficulty=data.get('difficulty', 'beginner'),
            estimated_time=data.get('estimated_time', '10-15 min')
        )
        template.nodes = data.get('nodes', [])
        template.characters = data.get('characters', [])
        template.metadata = data.get('metadata', {})
        return template


class StoryTemplateManager:
    """Manages story templates"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.templates = {}
        self.load_built_in_templates()
    
    def load_built_in_templates(self):
        """Load built-in story templates"""
        # Fantasy Adventure Template
        self.templates['fantasy_adventure'] = self._create_fantasy_adventure_template()
        
        # Mystery Template
        self.templates['mystery'] = self._create_mystery_template()
        
        # Sci-Fi Template
        self.templates['scifi'] = self._create_scifi_template()
        
        # Romance Template
        self.templates['romance'] = self._create_romance_template()
        
        # Horror Template
        self.templates['horror'] = self._create_horror_template()
    
    def get_template(self, template_id: str) -> Optional[StoryTemplate]:
        """Get a specific template"""
        return self.templates.get(template_id)
    
    def list_templates(self) -> List[Dict[str, str]]:
        """List all available templates"""
        return [
            {
                'id': template_id,
                'name': template.name,
                'description': template.description,
                'genre': template.genre,
                'difficulty': template.difficulty,
                'estimated_time': template.estimated_time
            }
            for template_id, template in self.templates.items()
        ]
    
    def create_story_from_template(self, template_id: str, title: str, 
                                 player_class: str = "Ranger") -> Optional[StoryWeb]:
        """Create a story web from a template"""
        template = self.get_template(template_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            return None
        
        try:
            # Create story web
            story = StoryWeb(self.config)
            story.metadata.update({
                'title': title,
                'template_id': template_id,
                'template_name': template.name,
                'genre': template.genre,
                'player_class': player_class,
                'source': 'template'
            })
            story.metadata.update(template.metadata)
            
            # Create character manager and add template characters
            char_manager = story.get_character_manager()
            character_id_map = {}
            
            for char_data in template.characters:
                character = self._create_character_from_template(char_data, player_class)
                char_manager.add_character(character)
                character_id_map[char_data['template_id']] = character.id
            
            # Create nodes from template
            node_id_map = {}
            for node_data in template.nodes:
                node = self._create_node_from_template(node_data, character_id_map)
                story.add_node(node)
                node_id_map[node_data['template_id']] = node.id
            
            # Update choice targets to use real node IDs
            for node in story.nodes.values():
                for choice in node.choices:
                    if choice.target_node_id in node_id_map:
                        choice.target_node_id = node_id_map[choice.target_node_id]
            
            # Set entry points and endings
            story.entry_points = [node_id_map[tid] for tid in template.metadata.get('entry_points', [])]
            story.endings = [node_id_map[tid] for tid in template.metadata.get('endings', [])]
            
            logger.info(f"Created story from template {template_id}: {len(story.nodes)} nodes")
            return story
            
        except Exception as e:
            logger.error(f"Error creating story from template: {e}")
            return None
    
    def _create_character_from_template(self, char_data: Dict[str, Any], 
                                      player_class: str) -> Character:
        """Create character from template data"""
        # Handle player character class substitution
        if char_data.get('is_player_character'):
            char_data = self._adapt_player_character(char_data, player_class)
        
        character = Character(
            id=char_data.get('id', ''),
            name=char_data['name'],
            role=CharacterRole(char_data['role']),
            appearance=CharacterAppearance(**char_data.get('appearance', {})),
            personality=CharacterPersonality(**char_data.get('personality', {})),
            is_player_character=char_data.get('is_player_character', False),
            importance_level=char_data.get('importance_level', 3)
        )
        
        return character
    
    def _create_node_from_template(self, node_data: Dict[str, Any], 
                                 character_id_map: Dict[str, str]) -> StoryNode:
        """Create story node from template data"""
        node = StoryNode(
            id=node_data.get('id', ''),
            text=node_data['text'],
            node_type=NodeType(node_data.get('node_type', 'story')),
            is_entry=node_data.get('is_entry', False),
            is_ending=node_data.get('is_ending', False),
            ending_type=EndingType(node_data['ending_type']) if node_data.get('ending_type') else None,
            rating=node_data.get('rating', 'safe'),
            location=node_data.get('location', ''),
            inventory_state=node_data.get('inventory_state', {}),
            class_context=node_data.get('class_context')
        )
        
        # Add choices
        for choice_data in node_data.get('choices', []):
            choice = Choice(
                text=choice_data['text'],
                target_node_id=choice_data['target_node_id'],  # Will be mapped later
                requirements=choice_data.get('requirements', {}),
                inventory_changes=choice_data.get('inventory_changes', {}),
                class_requirements=choice_data.get('class_requirements', [])
            )
            node.choices.append(choice)
        
        # Add character information
        for char_template_id in node_data.get('present_characters', []):
            if char_template_id in character_id_map:
                real_char_id = character_id_map[char_template_id]
                node.present_characters.add(real_char_id)
        
        return node
    
    def _adapt_player_character(self, char_data: Dict[str, Any], player_class: str) -> Dict[str, Any]:
        """Adapt player character based on chosen class"""
        class_adaptations = {
            'Mage': {
                'appearance': {
                    'physical_description': 'A scholarly figure in flowing robes',
                    'clothing': 'mystical robes and a staff',
                    'distinctive_features': 'eyes that glow with magical energy'
                },
                'personality': {
                    'traits': ['intelligent', 'curious', 'powerful', 'studious'],
                    'motivations': ['seek knowledge', 'master magic', 'protect the innocent'],
                    'goals': ['become a master wizard', 'unlock ancient secrets']
                }
            },
            'Ranger': {
                'appearance': {
                    'physical_description': 'A rugged outdoorsman with keen eyes',
                    'clothing': 'leather armor and a longbow',
                    'distinctive_features': 'weathered hands and alert posture'
                },
                'personality': {
                    'traits': ['skilled', 'independent', 'observant', 'practical'],
                    'motivations': ['protect nature', 'seek freedom', 'help others'],
                    'goals': ['master survival skills', 'protect the wilderness']
                }
            },
            'Charmer': {
                'appearance': {
                    'physical_description': 'An attractive and well-dressed individual',
                    'clothing': 'fine clothes and elegant accessories',
                    'distinctive_features': 'captivating smile and confident bearing'
                },
                'personality': {
                    'traits': ['charismatic', 'persuasive', 'social', 'clever'],
                    'motivations': ['gain influence', 'build relationships', 'achieve fame'],
                    'goals': ['become a leader', 'win hearts and minds']
                }
            }
        }
        
        adaptation = class_adaptations.get(player_class, class_adaptations['Ranger'])
        
        # Update character data with class-specific information
        adapted_data = char_data.copy()
        adapted_data['appearance'].update(adaptation['appearance'])
        adapted_data['personality'].update(adaptation['personality'])
        
        return adapted_data
    
    def _create_fantasy_adventure_template(self) -> StoryTemplate:
        """Create fantasy adventure template"""
        template = StoryTemplate(
            name="Fantasy Adventure",
            description="A classic fantasy quest with magic, monsters, and treasure",
            genre="Fantasy",
            difficulty="beginner",
            estimated_time="15-20 min"
        )
        
        # Template characters
        template.characters = [
            {
                'template_id': 'player',
                'name': 'Hero',
                'role': 'protagonist',
                'is_player_character': True,
                'importance_level': 5,
                'appearance': {
                    'physical_description': 'A brave adventurer',
                    'age_range': 'young adult'
                },
                'personality': {
                    'traits': ['brave', 'determined', 'loyal'],
                    'motivations': ['save the kingdom', 'find treasure'],
                    'moral_alignment': 'good'
                }
            },
            {
                'template_id': 'wizard',
                'name': 'Eldric the Wise',
                'role': 'mentor',
                'importance_level': 4,
                'appearance': {
                    'physical_description': 'An elderly wizard with a long white beard',
                    'clothing': 'star-covered robes',
                    'distinctive_features': 'twinkling eyes'
                },
                'personality': {
                    'traits': ['wise', 'mysterious', 'helpful'],
                    'motivations': ['guide heroes', 'protect knowledge'],
                    'moral_alignment': 'good'
                }
            },
            {
                'template_id': 'dragon',
                'name': 'Shadowfang',
                'role': 'antagonist',
                'importance_level': 5,
                'appearance': {
                    'physical_description': 'A massive black dragon with glowing red eyes',
                    'distinctive_features': 'razor-sharp claws and smoking nostrils'
                },
                'personality': {
                    'traits': ['fierce', 'greedy', 'ancient'],
                    'motivations': ['hoard treasure', 'dominate territory'],
                    'moral_alignment': 'evil'
                }
            }
        ]
        
        # Template nodes
        template.nodes = [
            {
                'template_id': 'village_start',
                'text': 'You stand in the village square as panicked villagers run past. The wise wizard Eldric approaches you with urgent news.',
                'node_type': 'entry',
                'is_entry': True,
                'location': 'Village Square',
                'present_characters': ['player', 'wizard'],
                'choices': [
                    {
                        'text': 'Listen to the wizard\'s urgent message',
                        'target_node_id': 'wizard_quest'
                    },
                    {
                        'text': 'Ask the villagers what\'s happening',
                        'target_node_id': 'villager_info'
                    },
                    {
                        'text': 'Head straight to the source of trouble',
                        'target_node_id': 'forest_path'
                    }
                ]
            },
            {
                'template_id': 'wizard_quest',
                'text': '"The ancient dragon Shadowfang has awakened!" Eldric exclaims. "Only the Crystal of Light can stop it, but it lies deep in the Forbidden Forest."',
                'node_type': 'story',
                'location': 'Village Square',
                'present_characters': ['player', 'wizard'],
                'choices': [
                    {
                        'text': 'Accept the quest to find the Crystal',
                        'target_node_id': 'forest_path'
                    },
                    {
                        'text': 'Ask for magical assistance',
                        'target_node_id': 'magic_aid',
                        'class_requirements': ['Mage']
                    },
                    {
                        'text': 'Refuse and seek another solution',
                        'target_node_id': 'alternative_path'
                    }
                ]
            },
            {
                'template_id': 'forest_path',
                'text': 'The Forbidden Forest looms before you, dark and mysterious. Ancient trees whisper secrets in the wind.',
                'node_type': 'story',
                'location': 'Forest Entrance',
                'present_characters': ['player'],
                'choices': [
                    {
                        'text': 'Enter the forest cautiously',
                        'target_node_id': 'forest_deep'
                    },
                    {
                        'text': 'Use ranger skills to find the safest path',
                        'target_node_id': 'safe_path',
                        'class_requirements': ['Ranger']
                    },
                    {
                        'text': 'Turn back and prepare better',
                        'target_node_id': 'village_start'
                    }
                ]
            },
            {
                'template_id': 'dragon_lair',
                'text': 'You face the mighty dragon Shadowfang in its lair. The Crystal of Light glows nearby, but the dragon blocks your path.',
                'node_type': 'story',
                'location': 'Dragon Lair',
                'present_characters': ['player', 'dragon'],
                'choices': [
                    {
                        'text': 'Fight the dragon with courage',
                        'target_node_id': 'dragon_battle'
                    },
                    {
                        'text': 'Try to charm the dragon',
                        'target_node_id': 'dragon_charm',
                        'class_requirements': ['Charmer']
                    },
                    {
                        'text': 'Use magic to distract it',
                        'target_node_id': 'magic_distraction',
                        'class_requirements': ['Mage']
                    }
                ]
            },
            {
                'template_id': 'victory_ending',
                'text': 'With the Crystal of Light in hand, you banish the dragon and save the kingdom! The villagers celebrate your heroic deed.',
                'node_type': 'ending',
                'is_ending': True,
                'ending_type': 'success',
                'location': 'Village Square',
                'present_characters': ['player', 'wizard'],
                'choices': []
            }
        ]
        
        # Template metadata
        template.metadata = {
            'entry_points': ['village_start'],
            'endings': ['victory_ending'],
            'recommended_class': 'any',
            'themes': ['heroism', 'magic', 'adventure'],
            'content_rating': 'safe'
        }
        
        return template
    
    def _create_mystery_template(self) -> StoryTemplate:
        """Create mystery template"""
        template = StoryTemplate(
            name="Murder Mystery",
            description="Solve a puzzling murder case with clues and suspects",
            genre="Mystery",
            difficulty="intermediate",
            estimated_time="20-25 min"
        )
        
        # Add mystery-specific structure
        template.characters = [
            {
                'template_id': 'detective',
                'name': 'Detective',
                'role': 'protagonist',
                'is_player_character': True,
                'importance_level': 5
            },
            {
                'template_id': 'butler',
                'name': 'James the Butler',
                'role': 'neutral',
                'importance_level': 3
            },
            {
                'template_id': 'victim',
                'name': 'Lord Blackwood',
                'role': 'background',
                'importance_level': 4
            }
        ]
        
        template.nodes = [
            {
                'template_id': 'crime_scene',
                'text': 'You arrive at the mansion to find Lord Blackwood dead in his study. The room shows signs of struggle.',
                'node_type': 'entry',
                'is_entry': True,
                'location': 'Study',
                'present_characters': ['detective'],
                'choices': [
                    {'text': 'Examine the body', 'target_node_id': 'examine_body'},
                    {'text': 'Search the room for clues', 'target_node_id': 'search_room'},
                    {'text': 'Interview the butler', 'target_node_id': 'butler_interview'}
                ]
            }
        ]
        
        template.metadata = {
            'entry_points': ['crime_scene'],
            'themes': ['investigation', 'deduction', 'suspense'],
            'content_rating': 'safe'
        }
        
        return template
    
    def _create_scifi_template(self) -> StoryTemplate:
        """Create sci-fi template"""
        return StoryTemplate(
            name="Space Station Crisis",
            description="Survive a crisis aboard a space station in the far future",
            genre="Sci-Fi",
            difficulty="intermediate"
        )
    
    def _create_romance_template(self) -> StoryTemplate:
        """Create romance template"""
        return StoryTemplate(
            name="Royal Romance",
            description="Navigate court intrigue and find true love",
            genre="Romance",
            difficulty="beginner"
        )
    
    def _create_horror_template(self) -> StoryTemplate:
        """Create horror template"""
        return StoryTemplate(
            name="Haunted Manor",
            description="Escape from a terrifying haunted house",
            genre="Horror",
            difficulty="advanced"
        )
