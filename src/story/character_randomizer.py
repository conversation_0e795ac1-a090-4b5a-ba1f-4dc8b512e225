"""
Character Randomizer - AI-powered and preset-based character generation
Provides randomization for character creation like video game character creators
"""

import logging
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class RandomizationPresets:
    """Presets for character randomization"""
    
    # Physical appearance presets
    HAIR_COLORS = [
        "black", "brown", "blonde", "red", "auburn", "chestnut", "platinum blonde",
        "strawberry blonde", "dirty blonde", "dark brown", "light brown", "silver",
        "white", "gray", "jet black", "honey blonde", "copper", "mahogany"
    ]
    
    EYE_COLORS = [
        "brown", "blue", "green", "hazel", "gray", "amber", "violet", "black",
        "dark brown", "light brown", "steel blue", "emerald green", "sea green",
        "ice blue", "honey brown", "golden brown", "deep blue", "bright green"
    ]
    
    AGE_RANGES = [
        "child (8-12)", "teenager (13-17)", "young adult (18-25)", "adult (26-35)",
        "middle-aged (36-50)", "mature (51-65)", "elderly (65+)", "ancient (100+)",
        "young (20-30)", "middle-aged (40-50)", "senior (60-70)"
    ]
    
    CLOTHING_STYLES = [
        "casual modern", "formal business", "elegant evening wear", "rugged outdoor",
        "bohemian artistic", "punk rock", "vintage classic", "futuristic tech",
        "medieval fantasy", "steampunk", "gothic dark", "preppy academic",
        "athletic sporty", "minimalist chic", "colorful eccentric", "professional"
    ]
    
    # Personality traits
    PERSONALITY_TRAITS = [
        "brave", "cowardly", "kind", "cruel", "intelligent", "naive", "wise", "foolish",
        "loyal", "treacherous", "honest", "deceptive", "patient", "impulsive", "calm", "anxious",
        "confident", "insecure", "optimistic", "pessimistic", "generous", "selfish", "humble", "arrogant",
        "creative", "practical", "adventurous", "cautious", "social", "introverted", "empathetic", "cold",
        "determined", "lazy", "organized", "chaotic", "forgiving", "vengeful", "curious", "indifferent"
    ]
    
    MOTIVATIONS = [
        "seeking revenge", "protecting loved ones", "gaining power", "finding truth",
        "achieving fame", "accumulating wealth", "helping others", "surviving danger",
        "discovering knowledge", "creating art", "building legacy", "finding love",
        "proving worth", "escaping past", "fulfilling destiny", "maintaining peace",
        "exploring unknown", "mastering skills", "solving mysteries", "bringing justice"
    ]
    
    FEARS = [
        "death", "failure", "abandonment", "betrayal", "losing control", "being forgotten",
        "public speaking", "heights", "darkness", "confined spaces", "deep water", "fire",
        "rejection", "commitment", "change", "responsibility", "intimacy", "authority",
        "poverty", "illness", "aging", "loneliness", "being judged", "making mistakes"
    ]
    
    GOALS = [
        "save the world", "find true love", "become wealthy", "gain recognition",
        "master a skill", "solve a mystery", "defeat an enemy", "protect family",
        "discover truth", "create something lasting", "achieve peace", "gain power",
        "help others", "explore new places", "overcome weakness", "fulfill prophecy",
        "build an empire", "find redemption", "unlock secrets", "bring justice"
    ]
    
    MORAL_ALIGNMENTS = [
        "lawful good", "neutral good", "chaotic good", "lawful neutral", "true neutral",
        "chaotic neutral", "lawful evil", "neutral evil", "chaotic evil", "heroic",
        "villainous", "anti-hero", "morally gray", "righteous", "corrupt", "pragmatic"
    ]
    
    # Voice characteristics
    ACCENTS = [
        "American", "British", "Irish", "Scottish", "Australian", "Canadian", "Southern",
        "New York", "Boston", "French", "German", "Italian", "Spanish", "Russian",
        "posh", "working class", "rural", "urban", "educated", "street smart"
    ]
    
    VOCABULARY_STYLES = [
        "formal", "casual", "archaic", "modern", "technical", "poetic", "blunt",
        "eloquent", "simple", "complex", "street slang", "academic", "military",
        "artistic", "scientific", "philosophical", "humorous", "dramatic"
    ]
    
    SPEECH_PATTERNS = [
        "speaks slowly and deliberately", "talks rapidly when excited", "uses lots of gestures",
        "has a nervous laugh", "clears throat often", "speaks in whispers", "very loud voice",
        "uses big words", "speaks in short sentences", "asks lots of questions",
        "interrupts others", "pauses frequently", "has a stutter", "uses catchphrases",
        "quotes literature", "makes puns", "tells stories", "gives compliments"
    ]


class CharacterRandomizer:
    """AI-powered character randomizer with preset fallbacks"""
    
    def __init__(self, config: Dict[str, Any], lmstudio_client=None):
        self.config = config
        self.lmstudio_client = lmstudio_client
        self.presets = RandomizationPresets()
        
        logger.info("Character randomizer initialized")
    
    def randomize_basic_info(self) -> Dict[str, Any]:
        """Randomize basic character information"""
        names = self._generate_names()
        
        return {
            "name": random.choice(names),
            "importance_level": random.randint(1, 5),
            "is_player_character": random.choice([True, False]) if random.random() < 0.1 else False,
            "tags": self._generate_tags()
        }
    
    def randomize_appearance(self) -> Dict[str, Any]:
        """Randomize character appearance"""
        return {
            "physical_description": self._generate_physical_description(),
            "age_range": random.choice(self.presets.AGE_RANGES),
            "hair_color": random.choice(self.presets.HAIR_COLORS),
            "eye_color": random.choice(self.presets.EYE_COLORS),
            "clothing": random.choice(self.presets.CLOTHING_STYLES),
            "distinctive_features": self._generate_distinctive_features()
        }
    
    def randomize_voice(self) -> Dict[str, Any]:
        """Randomize character voice characteristics"""
        return {
            "voice_description": self._generate_voice_description(),
            "accent": random.choice(self.presets.ACCENTS),
            "vocabulary_style": random.choice(self.presets.VOCABULARY_STYLES),
            "speech_patterns": random.sample(self.presets.SPEECH_PATTERNS, random.randint(1, 3))
        }
    
    def randomize_personality(self) -> Dict[str, Any]:
        """Randomize character personality"""
        return {
            "traits": random.sample(self.presets.PERSONALITY_TRAITS, random.randint(3, 6)),
            "motivations": random.sample(self.presets.MOTIVATIONS, random.randint(1, 3)),
            "fears": random.sample(self.presets.FEARS, random.randint(1, 3)),
            "goals": random.sample(self.presets.GOALS, random.randint(1, 2)),
            "moral_alignment": random.choice(self.presets.MORAL_ALIGNMENTS),
            "background_story": self._generate_background_story()
        }
    
    def randomize_all(self) -> Dict[str, Any]:
        """Randomize all character aspects"""
        return {
            "basic": self.randomize_basic_info(),
            "appearance": self.randomize_appearance(),
            "voice": self.randomize_voice(),
            "personality": self.randomize_personality()
        }
    
    def _generate_names(self) -> List[str]:
        """Generate character names"""
        # Preset names as fallback
        preset_names = [
            "Alex", "Morgan", "Jordan", "Casey", "Riley", "Avery", "Quinn", "Sage",
            "Aria", "Luna", "Nova", "Zara", "Kai", "Phoenix", "River", "Sky",
            "Elena", "Marcus", "Sophia", "Gabriel", "Isabella", "Alexander", "Olivia", "Sebastian",
            "Amelia", "Theodore", "Charlotte", "Benjamin", "Harper", "William", "Evelyn", "James"
        ]
        
        if self.lmstudio_client:
            try:
                # Try AI generation
                prompt = "Generate 5 unique character names for a story. Return only the names, one per line."
                response = self.lmstudio_client.generate_text(prompt, max_tokens=100)
                ai_names = [name.strip() for name in response.split('\n') if name.strip()]
                if ai_names:
                    return ai_names
            except Exception as e:
                logger.warning(f"AI name generation failed: {e}")
        
        return preset_names
    
    def _generate_tags(self) -> List[str]:
        """Generate character tags"""
        tag_options = [
            "protagonist", "antagonist", "mentor", "comic relief", "love interest",
            "mysterious", "powerful", "vulnerable", "wise", "young", "old",
            "magical", "warrior", "scholar", "artist", "leader", "follower"
        ]
        return random.sample(tag_options, random.randint(1, 3))
    
    def _generate_physical_description(self) -> str:
        """Generate physical description"""
        if self.lmstudio_client:
            try:
                prompt = "Write a brief physical description of a fictional character in 1-2 sentences. Focus on distinctive features."
                response = self.lmstudio_client.generate_text(prompt, max_tokens=150)
                if response and len(response.strip()) > 10:
                    return response.strip()
            except Exception as e:
                logger.warning(f"AI description generation failed: {e}")
        
        # Fallback preset descriptions
        descriptions = [
            "A tall figure with striking features and an athletic build.",
            "Medium height with expressive eyes and a warm smile.",
            "Petite and graceful with delicate features and quick movements.",
            "Imposing presence with broad shoulders and weathered hands.",
            "Youthful appearance with bright eyes and energetic demeanor."
        ]
        return random.choice(descriptions)
    
    def _generate_distinctive_features(self) -> str:
        """Generate distinctive features"""
        features = [
            "scar across left cheek", "heterochromia (different colored eyes)", "dimples when smiling",
            "freckles across nose", "birthmark on forehead", "missing pinky finger",
            "tattoo on wrist", "pierced ears", "crooked nose", "gap between front teeth",
            "unusually tall", "very short stature", "muscular build", "graceful movements",
            "distinctive laugh", "always wears a pendant", "walks with a limp", "perfect posture"
        ]
        return random.choice(features)
    
    def _generate_voice_description(self) -> str:
        """Generate voice description"""
        if self.lmstudio_client:
            try:
                prompt = "Describe a character's voice in 1-2 sentences. Include tone, pitch, and distinctive qualities."
                response = self.lmstudio_client.generate_text(prompt, max_tokens=100)
                if response and len(response.strip()) > 10:
                    return response.strip()
            except Exception as e:
                logger.warning(f"AI voice description failed: {e}")
        
        # Fallback descriptions
        descriptions = [
            "Deep and resonant with a slight rasp from years of use.",
            "Light and melodic with perfect enunciation.",
            "Warm and comforting with a hint of laughter always present.",
            "Sharp and precise, cutting through noise effortlessly.",
            "Soft and gentle, requiring others to lean in to listen."
        ]
        return random.choice(descriptions)
    
    def _generate_background_story(self) -> str:
        """Generate background story"""
        if self.lmstudio_client:
            try:
                prompt = "Write a brief character background story in 2-3 sentences. Include their origin and a defining moment."
                response = self.lmstudio_client.generate_text(prompt, max_tokens=200)
                if response and len(response.strip()) > 20:
                    return response.strip()
            except Exception as e:
                logger.warning(f"AI background generation failed: {e}")
        
        # Fallback backgrounds
        backgrounds = [
            "Grew up in a small village where everyone knew each other. Left home after a tragic event changed everything.",
            "Born into nobility but chose to live among common people. Learned the value of hard work and genuine friendship.",
            "Orphaned at a young age and raised by mentors who taught important life lessons. Now seeks to help others in similar situations.",
            "Traveled extensively in youth, collecting stories and experiences. Uses this wisdom to guide others through difficult times.",
            "Overcame significant challenges in early life through determination and support from unexpected allies."
        ]
        return random.choice(backgrounds)
