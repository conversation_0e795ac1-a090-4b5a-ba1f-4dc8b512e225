"""
Quiz System - Personality tests, knowledge quizzes, and assessment tools
Extends the story system for quiz-specific functionality
"""

import json
import uuid
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

from ..story.story_web import <PERSON><PERSON>eb, StoryNode, Choice, NodeType

logger = logging.getLogger(__name__)


class QuizType(Enum):
    """Types of quizzes"""
    PERSONALITY = "personality"  # "What Harry Potter house are you?"
    KNOWLEDGE = "knowledge"      # "How well do you know <PERSON>?"
    COMPATIBILITY = "compatibility"  # "Which character are you most like?"
    ASSESSMENT = "assessment"    # "What's your learning style?"
    TRIVIA = "trivia"           # "Can you answer these questions?"


class QuizResultType(Enum):
    """Types of quiz results"""
    CATEGORY = "category"        # Gryffindor, <PERSON>fflepuff, etc.
    PERCENTAGE = "percentage"    # 85% match with <PERSON> = "score"             # 8/10 correct
    PERSONALITY = "personality"  # "You are a natural leader"


@dataclass
class QuizOutcome:
    """Represents a possible quiz outcome/result"""
    id: str
    name: str
    title: str
    description: str
    result_type: QuizResultType
    
    # Visual elements
    image_prompt: str = ""
    color_scheme: str = "#3498db"
    emoji: str = "🎯"
    
    # Scoring
    min_score: float = 0.0
    max_score: float = 100.0
    
    # Social sharing
    share_text: str = ""
    hashtags: List[str] = field(default_factory=list)
    
    # Metadata
    rarity: str = "common"  # common, uncommon, rare, legendary
    fun_fact: str = ""


@dataclass
class QuizQuestion:
    """Represents a quiz question with scoring weights"""
    id: str
    text: str
    question_number: int
    
    # Answer choices with outcome weights
    choices: List[Dict[str, Any]] = field(default_factory=list)  # {text, outcome_weights}
    
    # Question metadata
    category: str = ""
    difficulty: str = "medium"
    explanation: str = ""
    
    # Media
    image_prompt: str = ""
    video_prompt: str = ""


class QuizWeb(StoryWeb):
    """
    Quiz-specific extension of StoryWeb
    Handles quiz logic, scoring, and result calculation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        
        # Quiz-specific properties
        self.quiz_type: QuizType = QuizType.PERSONALITY
        self.outcomes: Dict[str, QuizOutcome] = {}
        self.questions: Dict[str, QuizQuestion] = {}
        
        # Scoring system
        self.outcome_scores: Dict[str, float] = {}  # outcome_id -> current_score
        self.question_weights: Dict[str, Dict[str, float]] = {}  # question_id -> {outcome_id: weight}
        
        # Quiz metadata
        self.metadata.update({
            "quiz_type": "personality",
            "total_questions": 0,
            "estimated_time": "2-3 minutes",
            "viral_potential": "high"
        })
    
    def add_outcome(self, outcome: QuizOutcome) -> bool:
        """Add a possible quiz outcome"""
        try:
            self.outcomes[outcome.id] = outcome
            self.outcome_scores[outcome.id] = 0.0
            logger.info(f"Added quiz outcome: {outcome.name}")
            return True
        except Exception as e:
            logger.error(f"Error adding outcome: {e}")
            return False
    
    def add_question(self, question: QuizQuestion) -> bool:
        """Add a quiz question"""
        try:
            self.questions[question.id] = question
            
            # Initialize weights for this question
            self.question_weights[question.id] = {}
            
            # Process choice weights
            for choice in question.choices:
                outcome_weights = choice.get('outcome_weights', {})
                for outcome_id, weight in outcome_weights.items():
                    if outcome_id in self.outcomes:
                        self.question_weights[question.id][outcome_id] = weight
            
            logger.info(f"Added quiz question: {question.text[:50]}...")
            return True
        except Exception as e:
            logger.error(f"Error adding question: {e}")
            return False
    
    def generate_quiz_nodes(self) -> bool:
        """Generate story nodes from quiz questions and outcomes"""
        try:
            # Clear existing nodes
            self.nodes.clear()
            self.entry_points.clear()
            self.endings.clear()
            
            # Create entry node
            entry_node = StoryNode(
                id="quiz_start",
                text=f"Welcome to: {self.metadata.get('title', 'Quiz')}!\n\n{self.metadata.get('description', 'Answer the questions to discover your result!')}",
                node_type=NodeType.ENTRY,
                is_entry=True,
                metadata={"quiz_progress": 0, "scores": {outcome_id: 0.0 for outcome_id in self.outcomes}}
            )
            self.add_node(entry_node)
            
            # Generate question nodes with state tracking
            self._generate_question_nodes()
            
            # Generate result nodes
            self._generate_result_nodes()
            
            # Connect nodes
            self._connect_quiz_nodes()
            
            logger.info(f"Generated quiz with {len(self.nodes)} nodes")
            return True
            
        except Exception as e:
            logger.error(f"Error generating quiz nodes: {e}")
            return False
    
    def _generate_question_nodes(self):
        """Generate nodes for each question with state combinations"""
        questions = sorted(self.questions.values(), key=lambda q: q.question_number)
        
        for i, question in enumerate(questions):
            # For each question, we need nodes for different score states
            # This creates the "redundant" nodes you mentioned - each represents a different path
            
            if i == 0:
                # First question - only one state (from entry)
                self._create_question_node(question, i, {})
            else:
                # Subsequent questions - multiple states based on previous answers
                self._create_question_nodes_for_states(question, i)
    
    def _create_question_node(self, question: QuizQuestion, question_index: int, 
                            current_scores: Dict[str, float], state_suffix: str = ""):
        """Create a single question node for a specific score state"""
        node_id = f"q{question.question_number}_{question.id}{state_suffix}"
        
        # Calculate progress
        total_questions = len(self.questions)
        progress = (question_index / total_questions) * 100
        
        # Create question text with progress
        question_text = f"Question {question.question_number} of {total_questions}\n\n"
        question_text += question.text
        
        if question.explanation:
            question_text += f"\n\n💡 {question.explanation}"
        
        # Create node
        node = StoryNode(
            id=node_id,
            text=question_text,
            node_type=NodeType.STORY,
            metadata={
                "quiz_progress": progress,
                "question_number": question.question_number,
                "current_scores": current_scores.copy(),
                "question_id": question.id
            }
        )
        
        # Add choices
        for choice_data in question.choices:
            choice_id = f"{node_id}_choice_{len(node.choices)}"
            
            # Calculate next scores
            next_scores = current_scores.copy()
            outcome_weights = choice_data.get('outcome_weights', {})
            for outcome_id, weight in outcome_weights.items():
                if outcome_id in next_scores:
                    next_scores[outcome_id] += weight
            
            # Determine target node
            if question_index == len(self.questions) - 1:
                # Last question - go to result
                target_node_id = self._get_result_node_id(next_scores)
            else:
                # Next question with new state
                next_question = sorted(self.questions.values(), key=lambda q: q.question_number)[question_index + 1]
                target_node_id = self._get_next_question_node_id(next_question, next_scores)
            
            choice = Choice(
                id=choice_id,
                text=choice_data['text'],
                target_node_id=target_node_id,
                metadata={
                    "outcome_weights": outcome_weights,
                    "next_scores": next_scores
                }
            )
            node.choices.append(choice)
        
        self.add_node(node)
        return node_id
    
    def _create_question_nodes_for_states(self, question: QuizQuestion, question_index: int):
        """Create question nodes for different score states"""
        # For simplicity, we'll create a limited number of state combinations
        # In a full implementation, you might want to optimize this
        
        # Create representative states based on leading outcomes
        states_to_create = self._get_representative_states()
        
        for i, state in enumerate(states_to_create):
            state_suffix = f"_state{i}" if i > 0 else ""
            self._create_question_node(question, question_index, state, state_suffix)
    
    def _get_representative_states(self) -> List[Dict[str, float]]:
        """Get representative score states to create nodes for"""
        # Create a few representative states
        states = []
        
        # Balanced state
        balanced_state = {outcome_id: 0.0 for outcome_id in self.outcomes}
        states.append(balanced_state)
        
        # States where each outcome is leading
        for outcome_id in self.outcomes:
            leading_state = {oid: 0.0 for oid in self.outcomes}
            leading_state[outcome_id] = 10.0  # Give this outcome a lead
            states.append(leading_state)
        
        return states
    
    def _get_next_question_node_id(self, question: QuizQuestion, scores: Dict[str, float]) -> str:
        """Get the node ID for the next question given current scores"""
        # Determine which state this score combination represents
        leading_outcome = max(scores.items(), key=lambda x: x[1])[0] if scores else list(self.outcomes.keys())[0]
        
        # Find the corresponding state index
        states = self._get_representative_states()
        state_index = 0
        
        for i, state in enumerate(states):
            if i > 0:  # Skip balanced state
                state_leader = max(state.items(), key=lambda x: x[1])[0]
                if state_leader == leading_outcome:
                    state_index = i
                    break
        
        state_suffix = f"_state{state_index}" if state_index > 0 else ""
        return f"q{question.question_number}_{question.id}{state_suffix}"
    
    def _generate_result_nodes(self):
        """Generate result nodes for each outcome"""
        for outcome in self.outcomes.values():
            node_id = f"result_{outcome.id}"
            
            # Create engaging result text
            result_text = f"🎉 Your Result: {outcome.title}\n\n"
            result_text += f"{outcome.description}\n\n"
            
            if outcome.fun_fact:
                result_text += f"💡 Fun Fact: {outcome.fun_fact}\n\n"
            
            result_text += f"Share your result and challenge your friends! {' '.join(outcome.hashtags)}"
            
            node = StoryNode(
                id=node_id,
                text=result_text,
                node_type=NodeType.ENDING,
                is_ending=True,
                metadata={
                    "outcome_id": outcome.id,
                    "outcome_name": outcome.name,
                    "share_text": outcome.share_text,
                    "color_scheme": outcome.color_scheme,
                    "emoji": outcome.emoji,
                    "rarity": outcome.rarity
                }
            )
            
            self.add_node(node)
    
    def _get_result_node_id(self, final_scores: Dict[str, float]) -> str:
        """Determine which result node to go to based on final scores"""
        if not final_scores:
            # Default to first outcome
            return f"result_{list(self.outcomes.keys())[0]}"
        
        # Find the outcome with the highest score
        winning_outcome = max(final_scores.items(), key=lambda x: x[1])[0]
        return f"result_{winning_outcome}"
    
    def _connect_quiz_nodes(self):
        """Connect quiz nodes (choices are already set up in node creation)"""
        # Entry node connects to first question
        if self.questions:
            first_question = min(self.questions.values(), key=lambda q: q.question_number)
            first_node_id = f"q{first_question.question_number}_{first_question.id}"
            
            start_choice = Choice(
                id="start_quiz",
                text="Start Quiz! 🚀",
                target_node_id=first_node_id
            )
            
            if "quiz_start" in self.nodes:
                self.nodes["quiz_start"].choices.append(start_choice)
    
    def calculate_quiz_result(self, answers: List[Dict[str, Any]]) -> Optional[QuizOutcome]:
        """Calculate quiz result based on answers"""
        try:
            scores = {outcome_id: 0.0 for outcome_id in self.outcomes}
            
            for answer in answers:
                question_id = answer.get('question_id')
                choice_index = answer.get('choice_index')
                
                if question_id in self.questions:
                    question = self.questions[question_id]
                    if 0 <= choice_index < len(question.choices):
                        choice = question.choices[choice_index]
                        outcome_weights = choice.get('outcome_weights', {})
                        
                        for outcome_id, weight in outcome_weights.items():
                            if outcome_id in scores:
                                scores[outcome_id] += weight
            
            # Find winning outcome
            if scores:
                winning_outcome_id = max(scores.items(), key=lambda x: x[1])[0]
                return self.outcomes.get(winning_outcome_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating quiz result: {e}")
            return None
    
    def get_quiz_stats(self) -> Dict[str, Any]:
        """Get quiz statistics"""
        return {
            "quiz_type": self.quiz_type.value,
            "total_questions": len(self.questions),
            "total_outcomes": len(self.outcomes),
            "total_nodes": len(self.nodes),
            "estimated_posts": self._estimate_social_posts(),
            "viral_potential": self.metadata.get("viral_potential", "medium")
        }
    
    def _estimate_social_posts(self) -> int:
        """Estimate number of social media posts this quiz will generate"""
        # Each question might have multiple state nodes
        # Plus entry and result nodes
        base_posts = len(self.questions) * len(self._get_representative_states())
        result_posts = len(self.outcomes)
        entry_posts = 1
        
        return base_posts + result_posts + entry_posts
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert quiz to dictionary"""
        data = super().to_dict()
        
        # Add quiz-specific data
        data.update({
            "quiz_type": self.quiz_type.value,
            "outcomes": {oid: {
                "id": outcome.id,
                "name": outcome.name,
                "title": outcome.title,
                "description": outcome.description,
                "result_type": outcome.result_type.value,
                "image_prompt": outcome.image_prompt,
                "color_scheme": outcome.color_scheme,
                "emoji": outcome.emoji,
                "min_score": outcome.min_score,
                "max_score": outcome.max_score,
                "share_text": outcome.share_text,
                "hashtags": outcome.hashtags,
                "rarity": outcome.rarity,
                "fun_fact": outcome.fun_fact
            } for oid, outcome in self.outcomes.items()},
            "questions": {qid: {
                "id": question.id,
                "text": question.text,
                "question_number": question.question_number,
                "choices": question.choices,
                "category": question.category,
                "difficulty": question.difficulty,
                "explanation": question.explanation,
                "image_prompt": question.image_prompt,
                "video_prompt": question.video_prompt
            } for qid, question in self.questions.items()},
            "outcome_scores": self.outcome_scores,
            "question_weights": self.question_weights
        })
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuizWeb':
        """Create quiz from dictionary"""
        quiz = cls(data.get("config", {}))
        quiz.metadata = data.get("metadata", {})
        
        # Load quiz type
        quiz_type_str = data.get("quiz_type", "personality")
        quiz.quiz_type = QuizType(quiz_type_str)
        
        # Load outcomes
        for outcome_data in data.get("outcomes", {}).values():
            outcome = QuizOutcome(
                id=outcome_data["id"],
                name=outcome_data["name"],
                title=outcome_data["title"],
                description=outcome_data["description"],
                result_type=QuizResultType(outcome_data["result_type"]),
                image_prompt=outcome_data.get("image_prompt", ""),
                color_scheme=outcome_data.get("color_scheme", "#3498db"),
                emoji=outcome_data.get("emoji", "🎯"),
                min_score=outcome_data.get("min_score", 0.0),
                max_score=outcome_data.get("max_score", 100.0),
                share_text=outcome_data.get("share_text", ""),
                hashtags=outcome_data.get("hashtags", []),
                rarity=outcome_data.get("rarity", "common"),
                fun_fact=outcome_data.get("fun_fact", "")
            )
            quiz.add_outcome(outcome)
        
        # Load questions
        for question_data in data.get("questions", {}).values():
            question = QuizQuestion(
                id=question_data["id"],
                text=question_data["text"],
                question_number=question_data["question_number"],
                choices=question_data.get("choices", []),
                category=question_data.get("category", ""),
                difficulty=question_data.get("difficulty", "medium"),
                explanation=question_data.get("explanation", ""),
                image_prompt=question_data.get("image_prompt", ""),
                video_prompt=question_data.get("video_prompt", "")
            )
            quiz.add_question(question)
        
        # Load scores and weights
        quiz.outcome_scores = data.get("outcome_scores", {})
        quiz.question_weights = data.get("question_weights", {})
        
        # Load nodes using parent class method
        for node_id, node_data in data.get("nodes", {}).items():
            # Use parent class node loading
            pass  # This would be handled by parent class
        
        return quiz
