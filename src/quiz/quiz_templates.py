"""
Quiz Templates - Pre-built quiz structures for viral content
Popular quiz formats that drive engagement
"""

import logging
from typing import Dict, List, Optional, Any
import uuid

from .quiz_system import QuizWeb, QuizType, QuizOutcome, QuizQuestion, QuizResultType

logger = logging.getLogger(__name__)


class QuizTemplateManager:
    """Manages quiz templates"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.templates = {}
        self.load_built_in_templates()
    
    def load_built_in_templates(self):
        """Load built-in quiz templates"""
        self.templates['harry_potter_house'] = self._create_harry_potter_house_template()
        self.templates['marvel_character'] = self._create_marvel_character_template()
        self.templates['personality_type'] = self._create_personality_type_template()
        self.templates['disney_princess'] = self._create_disney_princess_template()
        self.templates['hogwarts_subject'] = self._create_hogwarts_subject_template()
    
    def get_template(self, template_id: str) -> Optional[QuizWeb]:
        """Get a specific template"""
        return self.templates.get(template_id)
    
    def list_templates(self) -> List[Dict[str, str]]:
        """List all available templates"""
        template_info = []
        
        for template_id, quiz in self.templates.items():
            template_info.append({
                'id': template_id,
                'title': quiz.metadata.get('title', 'Unknown Quiz'),
                'description': quiz.metadata.get('description', ''),
                'quiz_type': quiz.quiz_type.value,
                'questions': len(quiz.questions),
                'outcomes': len(quiz.outcomes),
                'estimated_time': quiz.metadata.get('estimated_time', '2-3 minutes'),
                'viral_potential': quiz.metadata.get('viral_potential', 'high')
            })
        
        return template_info
    
    def create_quiz_from_template(self, template_id: str, title: str = None) -> Optional[QuizWeb]:
        """Create a quiz from a template"""
        template = self.get_template(template_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            return None
        
        try:
            # Create a copy of the template
            quiz_data = template.to_dict()
            quiz = QuizWeb.from_dict(quiz_data)
            
            # Customize title if provided
            if title:
                quiz.metadata['title'] = title
            
            # Generate the quiz nodes
            quiz.generate_quiz_nodes()
            
            logger.info(f"Created quiz from template {template_id}: {len(quiz.nodes)} nodes")
            return quiz
            
        except Exception as e:
            logger.error(f"Error creating quiz from template: {e}")
            return None
    
    def _create_harry_potter_house_template(self) -> QuizWeb:
        """Create Harry Potter House sorting quiz"""
        quiz = QuizWeb(self.config)
        quiz.quiz_type = QuizType.PERSONALITY
        quiz.metadata.update({
            'title': 'Which Hogwarts House Do You Belong In?',
            'description': 'Discover your true Hogwarts house with this magical sorting quiz!',
            'estimated_time': '3-4 minutes',
            'viral_potential': 'very_high',
            'hashtags': ['#HarryPotter', '#Hogwarts', '#SortingHat', '#Quiz']
        })
        
        # Define outcomes (houses)
        houses = [
            {
                'id': 'gryffindor',
                'name': 'Gryffindor',
                'title': '🦁 You belong in Gryffindor!',
                'description': 'You are brave, daring, and chivalrous. You stand up for what\'s right and aren\'t afraid to face danger when protecting others.',
                'color_scheme': '#740001',
                'emoji': '🦁',
                'share_text': 'I got Gryffindor! Which Hogwarts house are you?',
                'hashtags': ['#Gryffindor', '#Brave', '#HarryPotter'],
                'fun_fact': 'Famous Gryffindors include Harry Potter, Hermione Granger, and Albus Dumbledore!',
                'rarity': 'common'
            },
            {
                'id': 'hufflepuff',
                'name': 'Hufflepuff',
                'title': '🦡 You belong in Hufflepuff!',
                'description': 'You are loyal, patient, and hardworking. You value friendship and fairness above all else.',
                'color_scheme': '#ffdb00',
                'emoji': '🦡',
                'share_text': 'I got Hufflepuff! Which Hogwarts house are you?',
                'hashtags': ['#Hufflepuff', '#Loyal', '#HarryPotter'],
                'fun_fact': 'Hufflepuff produced the fewest dark wizards of any house!',
                'rarity': 'uncommon'
            },
            {
                'id': 'ravenclaw',
                'name': 'Ravenclaw',
                'title': '🦅 You belong in Ravenclaw!',
                'description': 'You are intelligent, creative, and wise. You love learning and solving complex problems.',
                'color_scheme': '#0e1a40',
                'emoji': '🦅',
                'share_text': 'I got Ravenclaw! Which Hogwarts house are you?',
                'hashtags': ['#Ravenclaw', '#Wise', '#HarryPotter'],
                'fun_fact': 'Ravenclaw\'s common room has a riddle instead of a password!',
                'rarity': 'uncommon'
            },
            {
                'id': 'slytherin',
                'name': 'Slytherin',
                'title': '🐍 You belong in Slytherin!',
                'description': 'You are ambitious, cunning, and resourceful. You know what you want and will work hard to achieve it.',
                'color_scheme': '#1a472a',
                'emoji': '🐍',
                'share_text': 'I got Slytherin! Which Hogwarts house are you?',
                'hashtags': ['#Slytherin', '#Ambitious', '#HarryPotter'],
                'fun_fact': 'Slytherin has produced many successful wizards, including Merlin!',
                'rarity': 'rare'
            }
        ]
        
        for house_data in houses:
            outcome = QuizOutcome(
                id=house_data['id'],
                name=house_data['name'],
                title=house_data['title'],
                description=house_data['description'],
                result_type=QuizResultType.CATEGORY,
                color_scheme=house_data['color_scheme'],
                emoji=house_data['emoji'],
                share_text=house_data['share_text'],
                hashtags=house_data['hashtags'],
                fun_fact=house_data['fun_fact'],
                rarity=house_data['rarity']
            )
            quiz.add_outcome(outcome)
        
        # Define questions
        questions_data = [
            {
                'text': 'You\'re walking through a dark forest and come to a fork in the path. Which way do you go?',
                'choices': [
                    {'text': 'The well-lit path that looks safe', 'weights': {'hufflepuff': 3, 'ravenclaw': 1}},
                    {'text': 'The mysterious dark path', 'weights': {'slytherin': 3, 'gryffindor': 1}},
                    {'text': 'I\'d study the paths first to make the best choice', 'weights': {'ravenclaw': 3, 'hufflepuff': 1}},
                    {'text': 'The path that looks most challenging', 'weights': {'gryffindor': 3, 'slytherin': 1}}
                ]
            },
            {
                'text': 'What quality do you most value in others?',
                'choices': [
                    {'text': 'Loyalty and trustworthiness', 'weights': {'hufflepuff': 4}},
                    {'text': 'Intelligence and wit', 'weights': {'ravenclaw': 4}},
                    {'text': 'Courage and bravery', 'weights': {'gryffindor': 4}},
                    {'text': 'Ambition and determination', 'weights': {'slytherin': 4}}
                ]
            },
            {
                'text': 'You discover a powerful magical artifact. What do you do?',
                'choices': [
                    {'text': 'Research it thoroughly before touching it', 'weights': {'ravenclaw': 3, 'hufflepuff': 1}},
                    {'text': 'Use it to help others immediately', 'weights': {'gryffindor': 3, 'hufflepuff': 1}},
                    {'text': 'Keep it safe and tell trusted friends', 'weights': {'hufflepuff': 3, 'gryffindor': 1}},
                    {'text': 'Use it to achieve your goals', 'weights': {'slytherin': 3, 'ravenclaw': 1}}
                ]
            },
            {
                'text': 'What\'s your ideal way to spend a weekend?',
                'choices': [
                    {'text': 'Reading a fascinating book', 'weights': {'ravenclaw': 3, 'hufflepuff': 1}},
                    {'text': 'Going on an adventure with friends', 'weights': {'gryffindor': 3, 'hufflepuff': 1}},
                    {'text': 'Working on a personal project', 'weights': {'slytherin': 2, 'ravenclaw': 2}},
                    {'text': 'Spending quality time with loved ones', 'weights': {'hufflepuff': 4}}
                ]
            },
            {
                'text': 'In a group project, you typically:',
                'choices': [
                    {'text': 'Take charge and lead the team', 'weights': {'gryffindor': 2, 'slytherin': 2}},
                    {'text': 'Research and provide the best information', 'weights': {'ravenclaw': 4}},
                    {'text': 'Make sure everyone contributes and feels included', 'weights': {'hufflepuff': 4}},
                    {'text': 'Focus on the strategy to get the best results', 'weights': {'slytherin': 3, 'ravenclaw': 1}}
                ]
            }
        ]
        
        for i, q_data in enumerate(questions_data):
            question = QuizQuestion(
                id=f"hp_q{i+1}",
                text=q_data['text'],
                question_number=i + 1,
                choices=[
                    {
                        'text': choice['text'],
                        'outcome_weights': choice['weights']
                    }
                    for choice in q_data['choices']
                ]
            )
            quiz.add_question(question)
        
        return quiz
    
    def _create_marvel_character_template(self) -> QuizWeb:
        """Create Marvel character quiz"""
        quiz = QuizWeb(self.config)
        quiz.quiz_type = QuizType.COMPATIBILITY
        quiz.metadata.update({
            'title': 'Which Marvel Superhero Are You?',
            'description': 'Discover which Marvel hero matches your personality!',
            'estimated_time': '2-3 minutes',
            'viral_potential': 'high',
            'hashtags': ['#Marvel', '#Superhero', '#Quiz', '#MCU']
        })
        
        # Define outcomes (heroes)
        heroes = [
            {
                'id': 'iron_man',
                'name': 'Iron Man',
                'title': '🤖 You are Iron Man!',
                'description': 'You\'re a genius inventor with a sharp wit and a big heart. You use technology and intelligence to solve problems.',
                'emoji': '🤖',
                'fun_fact': 'Tony Stark has an IQ of 186!'
            },
            {
                'id': 'captain_america',
                'name': 'Captain America',
                'title': '🛡️ You are Captain America!',
                'description': 'You\'re a natural leader with strong moral principles. You always do what\'s right, even when it\'s difficult.',
                'emoji': '🛡️',
                'fun_fact': 'Steve Rogers was originally rejected by the military for being too weak!'
            },
            {
                'id': 'spider_man',
                'name': 'Spider-Man',
                'title': '🕷️ You are Spider-Man!',
                'description': 'You\'re young, energetic, and always trying to help others. You balance responsibility with a sense of humor.',
                'emoji': '🕷️',
                'fun_fact': 'Peter Parker was bitten by a radioactive spider at age 15!'
            },
            {
                'id': 'black_widow',
                'name': 'Black Widow',
                'title': '🕸️ You are Black Widow!',
                'description': 'You\'re strategic, skilled, and fiercely loyal to those you care about. You can handle any situation.',
                'emoji': '🕸️',
                'fun_fact': 'Natasha Romanoff speaks over 10 languages!'
            }
        ]
        
        for hero_data in heroes:
            outcome = QuizOutcome(
                id=hero_data['id'],
                name=hero_data['name'],
                title=hero_data['title'],
                description=hero_data['description'],
                result_type=QuizResultType.COMPATIBILITY,
                emoji=hero_data['emoji'],
                fun_fact=hero_data['fun_fact'],
                share_text=f"I got {hero_data['name']}! Which Marvel hero are you?"
            )
            quiz.add_outcome(outcome)
        
        # Add questions (simplified for brevity)
        questions_data = [
            {
                'text': 'What\'s your greatest strength?',
                'choices': [
                    {'text': 'My intelligence and problem-solving skills', 'weights': {'iron_man': 4}},
                    {'text': 'My moral compass and leadership', 'weights': {'captain_america': 4}},
                    {'text': 'My agility and quick thinking', 'weights': {'spider_man': 4}},
                    {'text': 'My strategic mind and adaptability', 'weights': {'black_widow': 4}}
                ]
            },
            {
                'text': 'How do you handle conflict?',
                'choices': [
                    {'text': 'Use technology and innovation', 'weights': {'iron_man': 3, 'spider_man': 1}},
                    {'text': 'Stand up for what\'s right', 'weights': {'captain_america': 3, 'spider_man': 1}},
                    {'text': 'Use humor to defuse tension', 'weights': {'spider_man': 3, 'iron_man': 1}},
                    {'text': 'Plan carefully and strike precisely', 'weights': {'black_widow': 3, 'captain_america': 1}}
                ]
            }
        ]
        
        for i, q_data in enumerate(questions_data):
            question = QuizQuestion(
                id=f"marvel_q{i+1}",
                text=q_data['text'],
                question_number=i + 1,
                choices=[
                    {
                        'text': choice['text'],
                        'outcome_weights': choice['weights']
                    }
                    for choice in q_data['choices']
                ]
            )
            quiz.add_question(question)
        
        return quiz
    
    def _create_personality_type_template(self) -> QuizWeb:
        """Create personality type quiz"""
        quiz = QuizWeb(self.config)
        quiz.quiz_type = QuizType.PERSONALITY
        quiz.metadata.update({
            'title': 'What\'s Your Personality Type?',
            'description': 'Discover your core personality traits and what makes you unique!',
            'estimated_time': '3-4 minutes',
            'viral_potential': 'high'
        })
        
        # Define personality types
        types = [
            {
                'id': 'leader',
                'name': 'The Leader',
                'title': '👑 You are The Leader!',
                'description': 'You naturally take charge and inspire others to follow your vision.',
                'emoji': '👑'
            },
            {
                'id': 'creative',
                'name': 'The Creative',
                'title': '🎨 You are The Creative!',
                'description': 'You see the world differently and express yourself through art and innovation.',
                'emoji': '🎨'
            },
            {
                'id': 'helper',
                'name': 'The Helper',
                'title': '🤝 You are The Helper!',
                'description': 'You find joy in supporting others and making their lives better.',
                'emoji': '🤝'
            },
            {
                'id': 'thinker',
                'name': 'The Thinker',
                'title': '🧠 You are The Thinker!',
                'description': 'You love analyzing problems and finding logical solutions.',
                'emoji': '🧠'
            }
        ]
        
        for type_data in types:
            outcome = QuizOutcome(
                id=type_data['id'],
                name=type_data['name'],
                title=type_data['title'],
                description=type_data['description'],
                result_type=QuizResultType.PERSONALITY,
                emoji=type_data['emoji']
            )
            quiz.add_outcome(outcome)
        
        return quiz
    
    def _create_disney_princess_template(self) -> QuizWeb:
        """Create Disney Princess quiz"""
        quiz = QuizWeb(self.config)
        quiz.quiz_type = QuizType.COMPATIBILITY
        quiz.metadata.update({
            'title': 'Which Disney Princess Are You?',
            'description': 'Find out which Disney Princess matches your personality!',
            'viral_potential': 'very_high'
        })
        
        return quiz  # Simplified for brevity
    
    def _create_hogwarts_subject_template(self) -> QuizWeb:
        """Create Hogwarts subject quiz"""
        quiz = QuizWeb(self.config)
        quiz.quiz_type = QuizType.ASSESSMENT
        quiz.metadata.update({
            'title': 'What Would Be Your Best Subject at Hogwarts?',
            'description': 'Discover which magical subject you\'d excel at!',
            'viral_potential': 'high'
        })
        
        return quiz  # Simplified for brevity
