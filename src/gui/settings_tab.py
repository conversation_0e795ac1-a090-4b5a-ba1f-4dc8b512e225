"""
Settings Tab
Tab for configuring application settings including AI models
"""

import logging
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QFormLayout, QComboBox, QLineEdit, QPushButton, QSpinBox,
    QCheckBox, QLabel, QTextEdit, QSlider, QFrame, QMessageBox,
    QDoubleSpinBox, QFileDialog
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)

class ModelDiscoveryWorker(QThread):
    """Background worker for discovering available models"""
    
    models_discovered = pyqtSignal(dict)  # {service: [models]}
    
    def __init__(self, lmstudio_client=None, comfyui_client=None):
        super().__init__()
        self.lmstudio_client = lmstudio_client
        self.comfyui_client = comfyui_client
    
    def run(self):
        """Discover available models"""
        models = {}
        
        # Discover LM Studio models
        if self.lmstudio_client:
            try:
                lm_models = self.lmstudio_client.get_available_models()
                models['lmstudio'] = lm_models if lm_models else []
            except Exception as e:
                logger.error(f"Error discovering LM Studio models: {e}")
                models['lmstudio'] = []
        
        # Discover ComfyUI models (if available)
        if self.comfyui_client:
            try:
                # This would need to be implemented in ComfyUI client
                comfy_models = []  # self.comfyui_client.get_available_models()
                models['comfyui'] = comfy_models
            except Exception as e:
                logger.error(f"Error discovering ComfyUI models: {e}")
                models['comfyui'] = []
        
        self.models_discovered.emit(models)

class SettingsTab(QWidget):
    """Tab for application settings"""
    
    def __init__(self, config: Dict[str, Any], lmstudio_client=None, comfyui_client=None):
        super().__init__()
        self.config = config
        self.lmstudio_client = lmstudio_client
        self.comfyui_client = comfyui_client
        self.available_models = {}
        
        self._setup_ui()
        self._load_settings()
        self._discover_models()
        
        logger.info("Settings tab initialized")
    
    def _setup_ui(self):
        """Setup the settings interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Settings tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create setting tabs
        self._create_ai_models_tab()
        self._create_generation_tab()
        self._create_interface_tab()
        self._create_performance_tab()
        self._create_logging_tab()
        
        # Save/Reset buttons
        buttons = self._create_buttons()
        layout.addWidget(buttons)
    
    def _create_header(self) -> QWidget:
        """Create settings header"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("⚙️ Application Settings")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Refresh models button
        self.refresh_models_btn = QPushButton("🔄 Refresh Models")
        self.refresh_models_btn.clicked.connect(self._discover_models)
        layout.addWidget(self.refresh_models_btn)
        
        return header
    
    def _create_ai_models_tab(self):
        """Create AI models configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # LM Studio Models
        lm_group = QGroupBox("🤖 LM Studio Models")
        lm_layout = QFormLayout(lm_group)
        
        self.chat_model_combo = QComboBox()
        self.chat_model_combo.setEditable(True)
        lm_layout.addRow("Chat Model:", self.chat_model_combo)
        
        self.story_generation_model_combo = QComboBox()
        self.story_generation_model_combo.setEditable(True)
        lm_layout.addRow("Story Generation:", self.story_generation_model_combo)
        
        self.character_generation_model_combo = QComboBox()
        self.character_generation_model_combo.setEditable(True)
        lm_layout.addRow("Character Generation:", self.character_generation_model_combo)
        
        self.quiz_generation_model_combo = QComboBox()
        self.quiz_generation_model_combo.setEditable(True)
        lm_layout.addRow("Quiz Generation:", self.quiz_generation_model_combo)
        
        layout.addWidget(lm_group)
        
        # Model Parameters
        params_group = QGroupBox("🎛️ Model Parameters")
        params_layout = QFormLayout(params_group)
        
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.7)
        params_layout.addRow("Temperature:", self.temperature_spin)
        
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 8192)
        self.max_tokens_spin.setValue(2048)
        params_layout.addRow("Max Tokens:", self.max_tokens_spin)
        
        self.top_p_spin = QDoubleSpinBox()
        self.top_p_spin.setRange(0.0, 1.0)
        self.top_p_spin.setSingleStep(0.05)
        self.top_p_spin.setValue(0.9)
        params_layout.addRow("Top P:", self.top_p_spin)
        
        layout.addWidget(params_group)
        
        # ComfyUI Models (if available)
        comfy_group = QGroupBox("🎨 ComfyUI Models")
        comfy_layout = QFormLayout(comfy_group)
        
        self.image_generation_model_combo = QComboBox()
        self.image_generation_model_combo.setEditable(True)
        comfy_layout.addRow("Image Generation:", self.image_generation_model_combo)
        
        self.video_generation_model_combo = QComboBox()
        self.video_generation_model_combo.setEditable(True)
        comfy_layout.addRow("Video Generation:", self.video_generation_model_combo)
        
        layout.addWidget(comfy_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "🤖 AI Models")
    
    def _create_generation_tab(self):
        """Create content generation settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Story Generation
        story_group = QGroupBox("📖 Story Generation")
        story_layout = QFormLayout(story_group)
        
        self.auto_generate_descriptions_check = QCheckBox()
        story_layout.addRow("Auto-generate descriptions:", self.auto_generate_descriptions_check)
        
        self.default_node_length_spin = QSpinBox()
        self.default_node_length_spin.setRange(50, 1000)
        self.default_node_length_spin.setValue(200)
        self.default_node_length_spin.setSuffix(" words")
        story_layout.addRow("Default node length:", self.default_node_length_spin)
        
        self.choice_count_spin = QSpinBox()
        self.choice_count_spin.setRange(2, 6)
        self.choice_count_spin.setValue(3)
        story_layout.addRow("Default choice count:", self.choice_count_spin)
        
        layout.addWidget(story_group)
        
        # Character Generation
        char_group = QGroupBox("👥 Character Generation")
        char_layout = QFormLayout(char_group)
        
        self.auto_generate_character_images_check = QCheckBox()
        char_layout.addRow("Auto-generate character images:", self.auto_generate_character_images_check)
        
        self.auto_generate_voices_check = QCheckBox()
        char_layout.addRow("Auto-generate voices:", self.auto_generate_voices_check)
        
        layout.addWidget(char_group)
        
        # Media Generation
        media_group = QGroupBox("🎬 Media Generation")
        media_layout = QFormLayout(media_group)
        
        self.auto_generate_node_images_check = QCheckBox()
        media_layout.addRow("Auto-generate node images:", self.auto_generate_node_images_check)
        
        self.auto_generate_videos_check = QCheckBox()
        media_layout.addRow("Auto-generate videos:", self.auto_generate_videos_check)
        
        self.image_style_edit = QLineEdit()
        self.image_style_edit.setPlaceholderText("e.g., fantasy art, realistic, cartoon")
        media_layout.addRow("Default image style:", self.image_style_edit)
        
        layout.addWidget(media_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "🎨 Generation")
    
    def _create_interface_tab(self):
        """Create interface settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Chat Assistant
        chat_group = QGroupBox("💬 Chat Assistant")
        chat_layout = QFormLayout(chat_group)
        
        self.chat_sidebar_enabled_check = QCheckBox()
        chat_layout.addRow("Enable sidebar:", self.chat_sidebar_enabled_check)
        
        self.chat_floating_enabled_check = QCheckBox()
        chat_layout.addRow("Enable floating window:", self.chat_floating_enabled_check)
        
        self.quick_actions_visible_check = QCheckBox()
        chat_layout.addRow("Show quick actions:", self.quick_actions_visible_check)
        
        layout.addWidget(chat_group)
        
        # Graph Viewer
        graph_group = QGroupBox("📊 Graph Viewer")
        graph_layout = QFormLayout(graph_group)
        
        self.auto_layout_check = QCheckBox()
        graph_layout.addRow("Auto-layout on load:", self.auto_layout_check)
        
        self.show_node_details_check = QCheckBox()
        graph_layout.addRow("Show node details:", self.show_node_details_check)
        
        layout.addWidget(graph_group)
        
        # Tabs
        tabs_group = QGroupBox("📑 Tabs")
        tabs_layout = QFormLayout(tabs_group)
        
        self.default_tab_combo = QComboBox()
        self.default_tab_combo.addItems([
            "Story Editor", "Graph Viewer", "Characters", "Items", 
            "X Manager", "Analytics", "Voice Studio"
        ])
        tabs_layout.addRow("Default tab:", self.default_tab_combo)
        
        layout.addWidget(tabs_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "🖥️ Interface")
    
    def _create_performance_tab(self):
        """Create performance settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Processing
        processing_group = QGroupBox("⚡ Processing")
        processing_layout = QFormLayout(processing_group)
        
        self.max_concurrent_requests_spin = QSpinBox()
        self.max_concurrent_requests_spin.setRange(1, 10)
        self.max_concurrent_requests_spin.setValue(3)
        processing_layout.addRow("Max concurrent AI requests:", self.max_concurrent_requests_spin)
        
        self.request_timeout_spin = QSpinBox()
        self.request_timeout_spin.setRange(10, 300)
        self.request_timeout_spin.setValue(60)
        self.request_timeout_spin.setSuffix(" seconds")
        processing_layout.addRow("Request timeout:", self.request_timeout_spin)
        
        layout.addWidget(processing_group)
        
        # Memory
        memory_group = QGroupBox("💾 Memory")
        memory_layout = QFormLayout(memory_group)
        
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setValue(100)
        self.cache_size_spin.setSuffix(" MB")
        memory_layout.addRow("Cache size:", self.cache_size_spin)
        
        self.auto_save_interval_spin = QSpinBox()
        self.auto_save_interval_spin.setRange(0, 60)
        self.auto_save_interval_spin.setValue(5)
        self.auto_save_interval_spin.setSuffix(" minutes (0 = disabled)")
        memory_layout.addRow("Auto-save interval:", self.auto_save_interval_spin)
        
        layout.addWidget(memory_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "⚡ Performance")
    
    def _create_logging_tab(self):
        """Create logging settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Log Level
        level_group = QGroupBox("📋 Log Level")
        level_layout = QFormLayout(level_group)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["SILENT", "ERROR", "WARNING", "INFO", "DEBUG", "TRACE"])
        self.log_level_combo.setCurrentText("INFO")
        level_layout.addRow("Log Level:", self.log_level_combo)
        
        layout.addWidget(level_group)
        
        # Log Files
        files_group = QGroupBox("📁 Log Files")
        files_layout = QFormLayout(files_group)
        
        self.log_to_file_check = QCheckBox()
        self.log_to_file_check.setChecked(True)
        files_layout.addRow("Log to file:", self.log_to_file_check)
        
        self.log_to_console_check = QCheckBox()
        self.log_to_console_check.setChecked(True)
        files_layout.addRow("Log to console:", self.log_to_console_check)
        
        self.max_log_size_spin = QSpinBox()
        self.max_log_size_spin.setRange(1, 100)
        self.max_log_size_spin.setValue(10)
        self.max_log_size_spin.setSuffix(" MB")
        files_layout.addRow("Max log file size:", self.max_log_size_spin)
        
        self.log_backup_count_spin = QSpinBox()
        self.log_backup_count_spin.setRange(1, 20)
        self.log_backup_count_spin.setValue(5)
        files_layout.addRow("Log backup count:", self.log_backup_count_spin)
        
        layout.addWidget(files_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "📋 Logging")
    
    def _create_buttons(self) -> QWidget:
        """Create save/reset buttons"""
        buttons = QFrame()
        layout = QHBoxLayout(buttons)
        
        layout.addStretch()
        
        self.reset_btn = QPushButton("🔄 Reset to Defaults")
        self.reset_btn.clicked.connect(self._reset_settings)
        layout.addWidget(self.reset_btn)
        
        self.save_btn = QPushButton("💾 Save Settings")
        self.save_btn.clicked.connect(self._save_settings)
        layout.addWidget(self.save_btn)
        
        return buttons
    
    def _discover_models(self):
        """Discover available models from services"""
        self.refresh_models_btn.setEnabled(False)
        self.refresh_models_btn.setText("🔄 Discovering...")
        
        self.model_worker = ModelDiscoveryWorker(self.lmstudio_client, self.comfyui_client)
        self.model_worker.models_discovered.connect(self._on_models_discovered)
        self.model_worker.start()
    
    def _on_models_discovered(self, models: Dict[str, List[str]]):
        """Handle discovered models"""
        self.available_models = models
        
        # Update LM Studio model combos
        lm_models = models.get('lmstudio', [])
        for combo in [self.chat_model_combo, self.story_generation_model_combo, 
                     self.character_generation_model_combo, self.quiz_generation_model_combo]:
            combo.clear()
            combo.addItems(lm_models)
        
        # Update ComfyUI model combos
        comfy_models = models.get('comfyui', [])
        for combo in [self.image_generation_model_combo, self.video_generation_model_combo]:
            combo.clear()
            combo.addItems(comfy_models)
        
        self.refresh_models_btn.setEnabled(True)
        self.refresh_models_btn.setText("🔄 Refresh Models")
        
        logger.info(f"Discovered {len(lm_models)} LM Studio models, {len(comfy_models)} ComfyUI models")
    
    def _load_settings(self):
        """Load current settings into the form"""
        try:
            # AI Models
            ai_config = self.config.get('ai', {})
            self.chat_model_combo.setCurrentText(ai_config.get('chat_model', ''))
            self.story_generation_model_combo.setCurrentText(ai_config.get('story_generation_model', ''))
            self.character_generation_model_combo.setCurrentText(ai_config.get('character_generation_model', ''))
            self.quiz_generation_model_combo.setCurrentText(ai_config.get('quiz_generation_model', ''))
            
            # Model Parameters
            params = ai_config.get('parameters', {})
            self.temperature_spin.setValue(params.get('temperature', 0.7))
            self.max_tokens_spin.setValue(params.get('max_tokens', 2048))
            self.top_p_spin.setValue(params.get('top_p', 0.9))
            
            # Generation Settings
            gen_config = self.config.get('generation', {})
            self.auto_generate_descriptions_check.setChecked(gen_config.get('auto_descriptions', False))
            self.default_node_length_spin.setValue(gen_config.get('default_node_length', 200))
            self.choice_count_spin.setValue(gen_config.get('default_choice_count', 3))
            
            # Interface Settings
            ui_config = self.config.get('ui', {})
            self.chat_sidebar_enabled_check.setChecked(ui_config.get('chat_sidebar_enabled', True))
            self.chat_floating_enabled_check.setChecked(ui_config.get('chat_floating_enabled', True))
            self.quick_actions_visible_check.setChecked(ui_config.get('quick_actions_visible', True))
            
            # Logging Settings
            log_config = self.config.get('logging', {})
            self.log_level_combo.setCurrentText(log_config.get('verbosity_name', 'INFO'))
            self.log_to_file_check.setChecked(log_config.get('log_to_file', True))
            self.log_to_console_check.setChecked(log_config.get('log_to_console', True))
            
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
    
    def _save_settings(self):
        """Save current settings"""
        try:
            # AI Models
            self.config.setdefault('ai', {})
            self.config['ai']['chat_model'] = self.chat_model_combo.currentText()
            self.config['ai']['story_generation_model'] = self.story_generation_model_combo.currentText()
            self.config['ai']['character_generation_model'] = self.character_generation_model_combo.currentText()
            self.config['ai']['quiz_generation_model'] = self.quiz_generation_model_combo.currentText()
            
            # Model Parameters
            self.config['ai'].setdefault('parameters', {})
            self.config['ai']['parameters']['temperature'] = self.temperature_spin.value()
            self.config['ai']['parameters']['max_tokens'] = self.max_tokens_spin.value()
            self.config['ai']['parameters']['top_p'] = self.top_p_spin.value()
            
            # Generation Settings
            self.config.setdefault('generation', {})
            self.config['generation']['auto_descriptions'] = self.auto_generate_descriptions_check.isChecked()
            self.config['generation']['default_node_length'] = self.default_node_length_spin.value()
            self.config['generation']['default_choice_count'] = self.choice_count_spin.value()
            
            # Interface Settings
            self.config.setdefault('ui', {})
            self.config['ui']['chat_sidebar_enabled'] = self.chat_sidebar_enabled_check.isChecked()
            self.config['ui']['chat_floating_enabled'] = self.chat_floating_enabled_check.isChecked()
            self.config['ui']['quick_actions_visible'] = self.quick_actions_visible_check.isChecked()
            
            # Logging Settings
            self.config.setdefault('logging', {})
            self.config['logging']['verbosity_name'] = self.log_level_combo.currentText()
            self.config['logging']['log_to_file'] = self.log_to_file_check.isChecked()
            self.config['logging']['log_to_console'] = self.log_to_console_check.isChecked()
            
            # Save to file
            config_file = Path("config.json")
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully!")
            logger.info("Settings saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save settings: {e}")
    
    def _reset_settings(self):
        """Reset settings to defaults"""
        reply = QMessageBox.question(
            self, "Reset Settings", 
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Reset to default values
            self._load_default_settings()
            logger.info("Settings reset to defaults")
    
    def _load_default_settings(self):
        """Load default settings"""
        # Set default values for all controls
        self.temperature_spin.setValue(0.7)
        self.max_tokens_spin.setValue(2048)
        self.top_p_spin.setValue(0.9)
        self.auto_generate_descriptions_check.setChecked(False)
        self.default_node_length_spin.setValue(200)
        self.choice_count_spin.setValue(3)
        self.chat_sidebar_enabled_check.setChecked(True)
        self.chat_floating_enabled_check.setChecked(True)
        self.quick_actions_visible_check.setChecked(True)
        self.log_level_combo.setCurrentText("INFO")
        self.log_to_file_check.setChecked(True)
        self.log_to_console_check.setChecked(True)
