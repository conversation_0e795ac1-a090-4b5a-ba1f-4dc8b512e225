"""
Google Drive Configuration Dialog
Floating window for configuring Google Drive API credentials and backup settings
"""

import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QLabel, QPushButton, QTextEdit, QCheckBox,
    QDialogButtonBox, QMessageBox, QTabWidget, QWidget,
    QSpinBox, QComboBox, QFileDialog
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)

class DriveConfigDialog(QDialog):
    """Configuration dialog for Google Drive backup settings"""
    
    def __init__(self, config: Dict[str, Any], backup_manager, parent=None):
        super().__init__(parent)
        self.config = config
        self.backup_manager = backup_manager
        
        self.setWindowTitle("☁️ Configure Google Drive")
        self.setModal(True)
        self.resize(600, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        
        self._setup_ui()
        self._load_current_settings()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("☁️ Google Drive Configuration")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("padding: 15px; color: #4285F4; background-color: #f8f9fa; border-radius: 8px; margin-bottom: 10px;")
        layout.addWidget(header)
        
        # Tab widget for different configuration sections
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # API Credentials Tab
        self._create_credentials_tab()
        
        # Backup Settings Tab
        self._create_backup_settings_tab()
        
        # Storage Settings Tab
        self._create_storage_settings_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Test connection button
        self.test_btn = QPushButton("🔗 Test Connection")
        self.test_btn.clicked.connect(self._test_connection)
        self.test_btn.setStyleSheet("background-color: #4285F4; color: white; padding: 8px 16px; border-radius: 4px;")
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        # Standard dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self._save_and_accept)
        button_box.rejected.connect(self.reject)
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
    
    def _create_credentials_tab(self):
        """Create API credentials configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # API Credentials Group
        creds_group = QGroupBox("🔑 API Credentials")
        creds_layout = QFormLayout(creds_group)
        
        # Client ID
        self.client_id_edit = QLineEdit()
        self.client_id_edit.setPlaceholderText("Enter your Google Drive API Client ID")
        creds_layout.addRow("Client ID:", self.client_id_edit)
        
        # Client Secret
        self.client_secret_edit = QLineEdit()
        self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.client_secret_edit.setPlaceholderText("Enter your Google Drive API Client Secret")
        creds_layout.addRow("Client Secret:", self.client_secret_edit)
        
        # Show/Hide password
        self.show_secret_check = QCheckBox("Show Client Secret")
        self.show_secret_check.toggled.connect(self._toggle_secret_visibility)
        creds_layout.addRow("", self.show_secret_check)
        
        # Credentials file
        creds_file_layout = QHBoxLayout()
        self.creds_file_edit = QLineEdit()
        self.creds_file_edit.setPlaceholderText("Path to credentials.json file")
        self.creds_file_edit.setReadOnly(True)
        creds_file_layout.addWidget(self.creds_file_edit)
        
        browse_btn = QPushButton("📁 Browse")
        browse_btn.clicked.connect(self._browse_credentials_file)
        creds_file_layout.addWidget(browse_btn)
        
        creds_layout.addRow("Credentials File:", creds_file_layout)
        
        layout.addWidget(creds_group)
        
        # OAuth Settings Group
        oauth_group = QGroupBox("🔐 OAuth Settings")
        oauth_layout = QFormLayout(oauth_group)
        
        # Redirect URI
        self.redirect_uri_edit = QLineEdit()
        self.redirect_uri_edit.setText("http://localhost:8081/callback")
        self.redirect_uri_edit.setReadOnly(True)
        oauth_layout.addRow("Redirect URI:", self.redirect_uri_edit)
        
        # Scopes
        self.scopes_edit = QTextEdit()
        self.scopes_edit.setMaximumHeight(60)
        self.scopes_edit.setPlainText("https://www.googleapis.com/auth/drive.file\nhttps://www.googleapis.com/auth/drive.appdata")
        self.scopes_edit.setReadOnly(True)
        oauth_layout.addRow("Scopes:", self.scopes_edit)
        
        layout.addWidget(oauth_group)
        
        # Instructions
        instructions = QTextEdit()
        instructions.setMaximumHeight(120)
        instructions.setReadOnly(True)
        instructions.setHtml("""
        <h4>📋 Setup Instructions:</h4>
        <ol>
        <li>Go to <a href="https://console.cloud.google.com">console.cloud.google.com</a></li>
        <li>Create a new project or select an existing one</li>
        <li>Enable the Google Drive API</li>
        <li>Create OAuth 2.0 credentials for a desktop application</li>
        <li>Download the credentials.json file and select it above</li>
        <li>Set the redirect URI to: <code>http://localhost:8081/callback</code></li>
        </ol>
        """)
        layout.addWidget(instructions)
        
        self.tab_widget.addTab(tab, "🔑 Credentials")
    
    def _create_backup_settings_tab(self):
        """Create backup settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Default Backup Options Group
        defaults_group = QGroupBox("📦 Default Backup Options")
        defaults_layout = QFormLayout(defaults_group)
        
        # Default selections
        self.backup_stories_check = QCheckBox("Story data (nodes, connections, metadata)")
        self.backup_stories_check.setChecked(True)
        defaults_layout.addRow("Stories:", self.backup_stories_check)
        
        self.backup_characters_check = QCheckBox("Character profiles and personalities")
        self.backup_characters_check.setChecked(True)
        defaults_layout.addRow("Characters:", self.backup_characters_check)
        
        self.backup_items_check = QCheckBox("Item definitions and inventory")
        self.backup_items_check.setChecked(True)
        defaults_layout.addRow("Items:", self.backup_items_check)
        
        self.backup_media_check = QCheckBox("Media files (images, audio, videos)")
        self.backup_media_check.setChecked(False)
        defaults_layout.addRow("Media:", self.backup_media_check)
        
        self.backup_ai_check = QCheckBox("AI settings and voice profiles")
        self.backup_ai_check.setChecked(True)
        defaults_layout.addRow("AI Settings:", self.backup_ai_check)
        
        layout.addWidget(defaults_group)
        
        # Backup Behavior Group
        behavior_group = QGroupBox("⚙️ Backup Behavior")
        behavior_layout = QFormLayout(behavior_group)
        
        # Auto-backup
        self.auto_backup_check = QCheckBox("Enable automatic backups")
        behavior_layout.addRow("Auto-backup:", self.auto_backup_check)
        
        # Backup frequency
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["Never", "Daily", "Weekly", "Monthly"])
        self.backup_frequency_combo.setCurrentText("Weekly")
        behavior_layout.addRow("Frequency:", self.backup_frequency_combo)
        
        # Compression
        self.compression_check = QCheckBox("Enable compression")
        self.compression_check.setChecked(True)
        behavior_layout.addRow("Compression:", self.compression_check)
        
        # Max versions
        self.max_versions_spin = QSpinBox()
        self.max_versions_spin.setRange(1, 50)
        self.max_versions_spin.setValue(5)
        behavior_layout.addRow("Max Versions:", self.max_versions_spin)
        
        layout.addWidget(behavior_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "📦 Backup")
    
    def _create_storage_settings_tab(self):
        """Create storage settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Folder Structure Group
        folder_group = QGroupBox("📁 Folder Structure")
        folder_layout = QFormLayout(folder_group)
        
        # Root folder name
        self.root_folder_edit = QLineEdit()
        self.root_folder_edit.setText("CYOA Automation Backups")
        folder_layout.addRow("Root Folder:", self.root_folder_edit)
        
        # Organization style
        self.organization_combo = QComboBox()
        self.organization_combo.addItems(["By Date", "By Project", "Flat Structure"])
        self.organization_combo.setCurrentText("By Date")
        folder_layout.addRow("Organization:", self.organization_combo)
        
        # Include timestamps
        self.timestamps_check = QCheckBox("Include timestamps in backup names")
        self.timestamps_check.setChecked(True)
        folder_layout.addRow("Timestamps:", self.timestamps_check)
        
        layout.addWidget(folder_group)
        
        # Storage Limits Group
        limits_group = QGroupBox("💾 Storage Limits")
        limits_layout = QFormLayout(limits_group)
        
        # Max backup size
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 10000)
        self.max_size_spin.setValue(100)
        self.max_size_spin.setSuffix(" MB")
        limits_layout.addRow("Max Backup Size:", self.max_size_spin)
        
        # Total storage limit
        self.total_limit_spin = QSpinBox()
        self.total_limit_spin.setRange(100, 100000)
        self.total_limit_spin.setValue(1000)
        self.total_limit_spin.setSuffix(" MB")
        limits_layout.addRow("Total Storage Limit:", self.total_limit_spin)
        
        # Cleanup old backups
        self.cleanup_check = QCheckBox("Automatically cleanup old backups")
        self.cleanup_check.setChecked(True)
        limits_layout.addRow("Auto-cleanup:", self.cleanup_check)
        
        layout.addWidget(limits_group)
        
        # Sync Settings Group
        sync_group = QGroupBox("🔄 Sync Settings")
        sync_layout = QFormLayout(sync_group)
        
        # Sync on startup
        self.sync_startup_check = QCheckBox("Check for backups on startup")
        self.sync_startup_check.setChecked(True)
        sync_layout.addRow("Startup Sync:", self.sync_startup_check)
        
        # Conflict resolution
        self.conflict_combo = QComboBox()
        self.conflict_combo.addItems(["Ask User", "Keep Local", "Keep Remote", "Keep Both"])
        self.conflict_combo.setCurrentText("Ask User")
        sync_layout.addRow("Conflict Resolution:", self.conflict_combo)
        
        layout.addWidget(sync_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "💾 Storage")
    
    def _toggle_secret_visibility(self, checked: bool):
        """Toggle client secret visibility"""
        if checked:
            self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
    
    def _browse_credentials_file(self):
        """Browse for credentials.json file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Google Drive Credentials File",
            "",
            "JSON files (*.json);;All files (*)"
        )
        
        if file_path:
            self.creds_file_edit.setText(file_path)
    
    def _load_current_settings(self):
        """Load current settings into the form"""
        try:
            drive_config = self.config.get('google_drive', {})
            
            # Load credentials
            self.client_id_edit.setText(drive_config.get('client_id', ''))
            self.client_secret_edit.setText(drive_config.get('client_secret', ''))
            self.creds_file_edit.setText(drive_config.get('credentials_file', ''))
            
            # Load backup settings
            backup_defaults = drive_config.get('backup_defaults', {})
            self.backup_stories_check.setChecked(backup_defaults.get('stories', True))
            self.backup_characters_check.setChecked(backup_defaults.get('characters', True))
            self.backup_items_check.setChecked(backup_defaults.get('items', True))
            self.backup_media_check.setChecked(backup_defaults.get('media', False))
            self.backup_ai_check.setChecked(backup_defaults.get('ai_settings', True))
            
            # Load behavior settings
            self.auto_backup_check.setChecked(drive_config.get('auto_backup', False))
            self.compression_check.setChecked(drive_config.get('compression', True))
            self.max_versions_spin.setValue(drive_config.get('max_versions', 5))
            
            # Load storage settings
            self.root_folder_edit.setText(drive_config.get('root_folder', 'CYOA Automation Backups'))
            self.timestamps_check.setChecked(drive_config.get('include_timestamps', True))
            self.max_size_spin.setValue(drive_config.get('max_backup_size_mb', 100))
            self.total_limit_spin.setValue(drive_config.get('total_storage_limit_mb', 1000))
            self.cleanup_check.setChecked(drive_config.get('auto_cleanup', True))
            self.sync_startup_check.setChecked(drive_config.get('sync_on_startup', True))
                
        except Exception as e:
            logger.error(f"Error loading Google Drive settings: {e}")
    
    def _test_connection(self):
        """Test the Google Drive API connection"""
        try:
            # Mock test for now
            QMessageBox.information(
                self, 
                "Connection Test", 
                "✅ Google Drive API configuration appears to be valid!\n\nNote: Full validation occurs during authentication."
            )
            
        except Exception as e:
            logger.error(f"Error testing Google Drive connection: {e}")
            QMessageBox.critical(self, "Connection Test Failed", f"Failed to test connection: {e}")
    
    def _save_and_accept(self):
        """Save settings and accept dialog"""
        try:
            # Save configuration
            drive_config = {
                'client_id': self.client_id_edit.text().strip(),
                'client_secret': self.client_secret_edit.text().strip(),
                'credentials_file': self.creds_file_edit.text().strip(),
                'redirect_uri': self.redirect_uri_edit.text().strip(),
                'backup_defaults': {
                    'stories': self.backup_stories_check.isChecked(),
                    'characters': self.backup_characters_check.isChecked(),
                    'items': self.backup_items_check.isChecked(),
                    'media': self.backup_media_check.isChecked(),
                    'ai_settings': self.backup_ai_check.isChecked()
                },
                'auto_backup': self.auto_backup_check.isChecked(),
                'backup_frequency': self.backup_frequency_combo.currentText().lower(),
                'compression': self.compression_check.isChecked(),
                'max_versions': self.max_versions_spin.value(),
                'root_folder': self.root_folder_edit.text().strip(),
                'organization': self.organization_combo.currentText().lower().replace(' ', '_'),
                'include_timestamps': self.timestamps_check.isChecked(),
                'max_backup_size_mb': self.max_size_spin.value(),
                'total_storage_limit_mb': self.total_limit_spin.value(),
                'auto_cleanup': self.cleanup_check.isChecked(),
                'sync_on_startup': self.sync_startup_check.isChecked(),
                'conflict_resolution': self.conflict_combo.currentText().lower().replace(' ', '_')
            }
            
            # Update config
            self.config['google_drive'] = drive_config
            
            # Save to file
            config_file = Path("config.json")
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            logger.info("Google Drive configuration saved successfully")
            self.accept()
            
        except Exception as e:
            logger.error(f"Error saving Google Drive configuration: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save configuration: {e}")
