"""
Chat Assistant - AI Assistant with App Awareness
Provides contextual help and app control through chat interface
Can be used as sidebar, floating window, or embedded widget
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLineEdit, 
    QPushButton, QSplitter, QGroupBox, QLabel, QScrollArea,
    QFrame, QCheckBox, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QTextCursor

from ai.chat_agent import ChatAgent
from ai.rag_system import RAGSystem
from audio.speech_to_text import STTManager, STTButton
from audio.text_to_speech import TTSManager, TTSWidget
from story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class ChatWorker(QThread):
    """Worker thread for chat responses"""
    
    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, chat_agent: ChatAgent, message: str):
        super().__init__()
        self.chat_agent = chat_agent
        self.message = message
    
    def run(self):
        """Get chat response in background"""
        try:
            response = self.chat_agent.get_response(self.message)
            self.response_ready.emit(response)
        except Exception as e:
            self.error_occurred.emit(str(e))


class ChatTab(QWidget):
    """Chat interface with AI assistant"""
    
    # Signals for app control
    node_creation_requested = pyqtSignal(dict)
    tab_navigation_requested = pyqtSignal(str)
    setting_change_requested = pyqtSignal(str, object)
    
    def __init__(self, config: Dict[str, Any], lmstudio_client, parent=None):
        super().__init__(parent)
        
        self.config = config
        self.lmstudio_client = lmstudio_client
        self.current_story: Optional[StoryWeb] = None
        
        # Initialize AI components
        self.rag_system = RAGSystem(config)
        self.chat_agent = ChatAgent(config, lmstudio_client, self.rag_system)
        
        # Initialize audio components
        self.stt_manager = STTManager(config)
        self.tts_manager = TTSManager(config)
        
        # Chat state
        self.chat_worker: Optional[ChatWorker] = None
        self.auto_scroll = True
        self.tts_enabled = config.get('chat', {}).get('tts_enabled', False)
        
        self._setup_ui()
        self._setup_app_actions()
        self._setup_connections()
        
        # Welcome message
        self._add_assistant_message(
            "👋 Hello! I'm your CYOA assistant. I can help you navigate the app, "
            "create stories, manage characters, and answer questions about the system. "
            "Try asking me something like 'How do I create a new story node?' or 'Show me the analytics tab'. "
            "You can also use the microphone button for voice input!"
        )
        
        logger.info("Chat tab initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create splitter for chat and controls
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # Chat display area
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Consolas", 10))
        self.chat_display.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        splitter.addWidget(self.chat_display)
        
        # Input area
        input_widget = self._create_input_area()
        splitter.addWidget(input_widget)
        
        # Set splitter proportions
        splitter.setSizes([400, 150])
        
        # Controls area
        controls_widget = self._create_controls_area()
        layout.addWidget(controls_widget)
    
    def _create_input_area(self) -> QWidget:
        """Create the input area with STT and TTS"""
        widget = QGroupBox("💬 Chat Input")
        layout = QVBoxLayout(widget)
        
        # Input field with STT button
        input_layout = QHBoxLayout()
        
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("Ask me anything about the app or your story...")
        self.input_field.returnPressed.connect(self._send_message)
        input_layout.addWidget(self.input_field)
        
        # STT button
        if self.stt_manager.is_available:
            self.stt_button = self.stt_manager.create_stt_button()
            self.stt_button.text_recognized.connect(self._on_speech_recognized)
            input_layout.addWidget(self.stt_button)
        
        # Send button
        self.send_button = QPushButton("Send")
        self.send_button.clicked.connect(self._send_message)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)
        
        # TTS controls
        tts_layout = QHBoxLayout()
        
        self.tts_enabled_check = QCheckBox("Enable voice responses")
        self.tts_enabled_check.setChecked(self.tts_enabled)
        self.tts_enabled_check.toggled.connect(self._toggle_tts)
        tts_layout.addWidget(self.tts_enabled_check)
        
        # TTS widget
        self.tts_widget = self.tts_manager.create_tts_widget()
        tts_layout.addWidget(self.tts_widget)
        
        tts_layout.addStretch()
        layout.addLayout(tts_layout)
        
        return widget
    
    def _create_controls_area(self) -> QWidget:
        """Create the controls area"""
        widget = QGroupBox("🎛️ Chat Controls")
        layout = QHBoxLayout(widget)
        
        # Clear chat button
        clear_button = QPushButton("Clear Chat")
        clear_button.clicked.connect(self._clear_chat)
        layout.addWidget(clear_button)
        
        # Export chat button
        export_button = QPushButton("Export Chat")
        export_button.clicked.connect(self._export_chat)
        layout.addWidget(export_button)
        
        # Context toggle
        self.include_story_context = QCheckBox("Include story context")
        self.include_story_context.setChecked(True)
        self.include_story_context.setToolTip("Include current story information in chat context")
        layout.addWidget(self.include_story_context)
        
        # Auto-scroll toggle
        self.auto_scroll_check = QCheckBox("Auto-scroll")
        self.auto_scroll_check.setChecked(True)
        self.auto_scroll_check.toggled.connect(lambda checked: setattr(self, 'auto_scroll', checked))
        layout.addWidget(self.auto_scroll_check)
        
        layout.addStretch()
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #888; font-size: 10px;")
        layout.addWidget(self.status_label)
        
        return widget
    
    def _setup_app_actions(self):
        """Setup app action callbacks for the chat agent"""
        actions = {
            "create_node": self._create_node_action,
            "edit_node": self._edit_node_action,
            "get_story_stats": self._get_story_stats_action,
            "navigate_to_tab": self._navigate_to_tab_action,
            "change_setting": self._change_setting_action,
            "export_story": self._export_story_action,
            "import_story": self._import_story_action
        }
        
        self.chat_agent.register_app_actions(actions)
    
    def _setup_connections(self):
        """Setup signal connections"""
        pass
    
    def _send_message(self):
        """Send user message to chat agent"""
        message = self.input_field.text().strip()
        if not message:
            return
        
        # Clear input
        self.input_field.clear()
        
        # Add user message to display
        self._add_user_message(message)
        
        # Update status
        self.status_label.setText("Thinking...")
        self.send_button.setEnabled(False)
        
        # Get response in background
        self.chat_worker = ChatWorker(self.chat_agent, message)
        self.chat_worker.response_ready.connect(self._on_response_ready)
        self.chat_worker.error_occurred.connect(self._on_response_error)
        self.chat_worker.start()
    
    def _on_speech_recognized(self, text: str):
        """Handle speech recognition result"""
        self.input_field.setText(text)
        self.input_field.setFocus()
    
    def _toggle_tts(self, enabled: bool):
        """Toggle text-to-speech"""
        self.tts_enabled = enabled
    
    def _on_response_ready(self, response: str):
        """Handle chat response"""
        self._add_assistant_message(response)
        
        # Speak response if TTS enabled
        if self.tts_enabled:
            self.tts_widget.speak_text(response)
        
        self.status_label.setText("Ready")
        self.send_button.setEnabled(True)
        self.input_field.setFocus()
    
    def _on_response_error(self, error: str):
        """Handle chat response error"""
        self._add_system_message(f"Error: {error}")
        self.status_label.setText("Error occurred")
        self.send_button.setEnabled(True)
        self.input_field.setFocus()
    
    def _add_user_message(self, message: str):
        """Add user message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"<div style='color: #4CAF50; margin: 5px 0;'>"
                                f"<b>[{timestamp}] You:</b> {message}</div>")
        self._scroll_to_bottom()
    
    def _add_assistant_message(self, message: str):
        """Add assistant message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"<div style='color: #2196F3; margin: 5px 0;'>"
                                f"<b>[{timestamp}] Assistant:</b> {message}</div>")
        self._scroll_to_bottom()
    
    def _add_system_message(self, message: str):
        """Add system message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"<div style='color: #FF9800; margin: 5px 0;'>"
                                f"<b>[{timestamp}] System:</b> {message}</div>")
        self._scroll_to_bottom()
    
    def _scroll_to_bottom(self):
        """Scroll chat display to bottom"""
        if self.auto_scroll:
            cursor = self.chat_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.chat_display.setTextCursor(cursor)
    
    def _clear_chat(self):
        """Clear chat history"""
        self.chat_display.clear()
        self.chat_agent.clear_history()
        self._add_assistant_message("Chat cleared. How can I help you?")
    
    def _export_chat(self):
        """Export chat conversation"""
        # Implementation for exporting chat
        self._add_system_message("Chat export feature coming soon!")
    
    # App action implementations
    def _create_node_action(self, params: Dict[str, Any]) -> str:
        """Create a new story node"""
        self.node_creation_requested.emit(params)
        return "Node creation requested"
    
    def _edit_node_action(self, params: Dict[str, Any]) -> str:
        """Edit an existing story node"""
        return "Node editing not yet implemented"
    
    def _get_story_stats_action(self, params: Dict[str, Any]) -> str:
        """Get story statistics"""
        if not self.current_story:
            return "No story currently loaded"
        
        stats = {
            "nodes": len(self.current_story.nodes),
            "entry_points": len([n for n in self.current_story.nodes.values() if n.is_entry_point]),
            "endings": len([n for n in self.current_story.nodes.values() if n.is_ending])
        }
        
        return f"Story stats: {stats['nodes']} nodes, {stats['entry_points']} entry points, {stats['endings']} endings"
    
    def _navigate_to_tab_action(self, params: Dict[str, Any]) -> str:
        """Navigate to a specific tab"""
        tab_name = params.get("tab_name")
        if tab_name:
            self.tab_navigation_requested.emit(tab_name)
            return f"Navigating to {tab_name}"
        return "No tab specified"
    
    def _change_setting_action(self, params: Dict[str, Any]) -> str:
        """Change an app setting"""
        return "Setting changes not yet implemented"
    
    def _export_story_action(self, params: Dict[str, Any]) -> str:
        """Export the current story"""
        return "Story export not yet implemented"
    
    def _import_story_action(self, params: Dict[str, Any]) -> str:
        """Import a story"""
        return "Story import not yet implemented"
    
    def set_story(self, story: StoryWeb):
        """Set the current story for context"""
        self.current_story = story
        
        # Update RAG system with new story
        if story:
            self.rag_system.index_story(story)
            self._add_system_message(f"Story loaded: {len(story.nodes)} nodes indexed for context")
        else:
            self._add_system_message("Story unloaded")
    
    def get_suggestions(self) -> list:
        """Get contextual suggestions"""
        return self.chat_agent.get_suggestions("")
