"""
Backup Options Dialog
Allows users to select what to backup for the current story
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QCheckBox, 
    QLabel, QPushButton, QProgressBar, QTextEdit, QFormLayout,
    QSpinBox, QComboBox, QDialogButtonBox, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)

class BackupWorker(QThread):
    """Worker thread for backup operations"""
    
    progress_updated = pyqtSignal(int, str)
    backup_completed = pyqtSignal(str)  # backup_id
    backup_failed = pyqtSignal(str)     # error message
    
    def __init__(self, story_data, backup_manager, options):
        super().__init__()
        self.story_data = story_data
        self.backup_manager = backup_manager
        self.options = options
    
    def run(self):
        """Run backup process"""
        try:
            self.progress_updated.emit(10, "Preparing backup data...")
            
            # Create backup
            self.progress_updated.emit(50, "Uploading to Google Drive...")
            backup_id = self.backup_manager.create_backup(self.story_data, self.options)
            
            if backup_id:
                self.progress_updated.emit(100, "Backup completed successfully")
                self.backup_completed.emit(backup_id)
            else:
                self.backup_failed.emit("Failed to create backup")
                
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            self.backup_failed.emit(str(e))


class BackupOptionsDialog(QDialog):
    """Dialog for selecting backup options"""
    
    def __init__(self, story, backup_manager, parent=None):
        super().__init__(parent)
        self.story = story
        self.backup_manager = backup_manager
        self.backup_worker = None
        
        self.setWindowTitle("📤 Backup to Google Drive")
        self.setModal(True)
        self.resize(600, 700)
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("📤 Backup Story to Google Drive")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("padding: 10px; color: #2c3e50;")
        layout.addWidget(header)
        
        # Story info
        story_group = QGroupBox("📖 Story Information")
        story_layout = QFormLayout(story_group)
        
        story_title = self.story.metadata.get('title', 'Untitled Story')
        story_layout.addRow("Title:", QLabel(story_title))
        
        node_count = len(self.story.nodes)
        story_layout.addRow("Nodes:", QLabel(str(node_count)))
        
        char_count = len(self.story.get_character_manager().get_all_characters())
        story_layout.addRow("Characters:", QLabel(str(char_count)))
        
        layout.addWidget(story_group)
        
        # Backup options
        options_group = QGroupBox("⚙️ Backup Options")
        options_layout = QVBoxLayout(options_group)
        
        # What to backup
        self.story_data_check = QCheckBox("📝 Story Data (nodes, connections, metadata)")
        self.story_data_check.setChecked(True)
        self.story_data_check.setToolTip("Include all story nodes, connections, and metadata")
        options_layout.addWidget(self.story_data_check)
        
        self.characters_check = QCheckBox("👥 Characters (profiles, personalities, voices)")
        self.characters_check.setChecked(True)
        self.characters_check.setToolTip("Include character definitions, personalities, and voice samples")
        options_layout.addWidget(self.characters_check)
        
        self.items_check = QCheckBox("🎒 Items (inventory, effects, descriptions)")
        self.items_check.setChecked(True)
        self.items_check.setToolTip("Include item definitions and inventory system data")
        options_layout.addWidget(self.items_check)
        
        self.media_check = QCheckBox("🎬 Media Files (images, audio, videos)")
        self.media_check.setChecked(False)  # Large files, off by default
        self.media_check.setToolTip("Include generated images, audio, and video files (may be large)")
        options_layout.addWidget(self.media_check)
        
        self.ai_settings_check = QCheckBox("🤖 AI Settings (prompts, voice profiles)")
        self.ai_settings_check.setChecked(True)
        self.ai_settings_check.setToolTip("Include AI prompts and voice profile configurations")
        options_layout.addWidget(self.ai_settings_check)
        
        self.app_settings_check = QCheckBox("⚙️ App Settings (preferences, configurations)")
        self.app_settings_check.setChecked(False)
        self.app_settings_check.setToolTip("Include application settings and preferences")
        options_layout.addWidget(self.app_settings_check)
        
        # Advanced options
        advanced_layout = QFormLayout()
        
        self.compression_check = QCheckBox("Enable compression")
        self.compression_check.setChecked(True)
        self.compression_check.setToolTip("Compress backup to reduce file size")
        advanced_layout.addRow("Compression:", self.compression_check)
        
        self.max_versions_spin = QSpinBox()
        self.max_versions_spin.setRange(1, 20)
        self.max_versions_spin.setValue(5)
        self.max_versions_spin.setToolTip("Maximum number of backup versions to keep")
        advanced_layout.addRow("Max Versions:", self.max_versions_spin)
        
        options_layout.addLayout(advanced_layout)
        
        layout.addWidget(options_group)
        
        # Backup name
        name_group = QGroupBox("📝 Backup Name")
        name_layout = QFormLayout(name_group)
        
        from datetime import datetime
        default_name = f"{story_title}_backup_{datetime.now().strftime('%Y%m%d_%H%M')}"
        
        self.backup_name_combo = QComboBox()
        self.backup_name_combo.setEditable(True)
        self.backup_name_combo.addItems([
            default_name,
            f"{story_title}_manual_backup",
            f"{story_title}_complete_backup",
            f"{story_title}_checkpoint"
        ])
        name_layout.addRow("Name:", self.backup_name_combo)
        
        layout.addWidget(name_group)
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Select all/none buttons
        select_all_btn = QPushButton("✅ Select All")
        select_all_btn.clicked.connect(self._select_all)
        button_layout.addWidget(select_all_btn)
        
        select_none_btn = QPushButton("❌ Select None")
        select_none_btn.clicked.connect(self._select_none)
        button_layout.addWidget(select_none_btn)
        
        button_layout.addStretch()
        
        # Main action buttons
        self.backup_btn = QPushButton("📤 Start Backup")
        self.backup_btn.clicked.connect(self._start_backup)
        self.backup_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 8px 16px;")
        button_layout.addWidget(self.backup_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _select_all(self):
        """Select all backup options"""
        self.story_data_check.setChecked(True)
        self.characters_check.setChecked(True)
        self.items_check.setChecked(True)
        self.media_check.setChecked(True)
        self.ai_settings_check.setChecked(True)
        self.app_settings_check.setChecked(True)
    
    def _select_none(self):
        """Deselect all backup options"""
        self.story_data_check.setChecked(False)
        self.characters_check.setChecked(False)
        self.items_check.setChecked(False)
        self.media_check.setChecked(False)
        self.ai_settings_check.setChecked(False)
        self.app_settings_check.setChecked(False)
    
    def _start_backup(self):
        """Start the backup process"""
        try:
            # Validate selection
            if not any([
                self.story_data_check.isChecked(),
                self.characters_check.isChecked(),
                self.items_check.isChecked(),
                self.media_check.isChecked(),
                self.ai_settings_check.isChecked(),
                self.app_settings_check.isChecked()
            ]):
                QMessageBox.warning(self, "No Selection", "Please select at least one item to backup.")
                return
            
            # Prepare backup options
            from backup.google_drive_backup import BackupOptions
            options = BackupOptions()
            options.story_data = self.story_data_check.isChecked()
            options.characters = self.characters_check.isChecked()
            options.items = self.items_check.isChecked()
            options.media_files = self.media_check.isChecked()
            options.ai_settings = self.ai_settings_check.isChecked()
            options.app_settings = self.app_settings_check.isChecked()
            
            # Prepare story data
            story_data = {
                'title': self.story.metadata.get('title', 'Untitled Story'),
                'metadata': self.story.metadata,
                'nodes': {node_id: node.to_dict() for node_id, node in self.story.nodes.items()},
                'characters': {char.id: char.to_dict() for char in self.story.get_character_manager().get_all_characters()},
                'items': {}  # TODO: Add items data
            }
            
            # Start backup worker
            self.backup_worker = BackupWorker(story_data, self.backup_manager, options)
            self.backup_worker.progress_updated.connect(self._on_progress_updated)
            self.backup_worker.backup_completed.connect(self._on_backup_completed)
            self.backup_worker.backup_failed.connect(self._on_backup_failed)
            
            # Update UI
            self.progress_bar.setVisible(True)
            self.backup_btn.setEnabled(False)
            self.backup_btn.setText("⏳ Backing up...")
            
            # Start backup
            self.backup_worker.start()
            
        except Exception as e:
            logger.error(f"Error starting backup: {e}")
            QMessageBox.critical(self, "Backup Error", f"Failed to start backup: {e}")
    
    def _on_progress_updated(self, progress: int, message: str):
        """Handle progress updates"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def _on_backup_completed(self, backup_id: str):
        """Handle backup completion"""
        self.progress_bar.setVisible(False)
        self.backup_btn.setEnabled(True)
        self.backup_btn.setText("📤 Start Backup")
        self.status_label.setText("")
        
        QMessageBox.information(
            self, 
            "Backup Complete", 
            f"✅ Backup completed successfully!\n\nBackup ID: {backup_id}\n\nYour story has been safely backed up to Google Drive."
        )
        self.accept()
    
    def _on_backup_failed(self, error_message: str):
        """Handle backup failure"""
        self.progress_bar.setVisible(False)
        self.backup_btn.setEnabled(True)
        self.backup_btn.setText("📤 Start Backup")
        self.status_label.setText("")
        
        QMessageBox.critical(
            self, 
            "Backup Failed", 
            f"❌ Backup failed:\n\n{error_message}\n\nPlease check your Google Drive connection and try again."
        )
