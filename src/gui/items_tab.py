"""
Items Tab - Manage story items and inventory system
"""

import logging
from typing import Dict, List, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QLineEdit, QTextEdit, QComboBox, QPushButton, QListWidget,
    QListWidgetItem, QFormLayout, QSpinBox, QCheckBox, QMessageBox,
    QSplitter, QScrollArea, QFileDialog, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal

from story.item_system import ItemManager, Item, ItemType, ItemRarity, ItemEffect

logger = logging.getLogger(__name__)


class ItemListWidget(QWidget):
    """Widget for displaying and managing the item list"""
    
    item_selected = pyqtSignal(str)  # item_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.item_manager = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("Items"))
        
        self.add_item_btn = QPushButton("Add Item")
        self.add_item_btn.clicked.connect(self.add_item)
        header_layout.addWidget(self.add_item_btn)
        
        self.import_item_btn = QPushButton("Import Item")
        self.import_item_btn.clicked.connect(self.import_item)
        header_layout.addWidget(self.import_item_btn)
        
        layout.addLayout(header_layout)
        
        # Filter options
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItem("All Types", None)
        for item_type in ItemType:
            self.type_filter.addItem(item_type.value.title(), item_type)
        self.type_filter.currentTextChanged.connect(self.refresh_item_list)
        filter_layout.addWidget(self.type_filter)
        
        filter_layout.addWidget(QLabel("Rarity:"))
        self.rarity_filter = QComboBox()
        self.rarity_filter.addItem("All Rarities", None)
        for rarity in ItemRarity:
            self.rarity_filter.addItem(rarity.value.title(), rarity)
        self.rarity_filter.currentTextChanged.connect(self.refresh_item_list)
        filter_layout.addWidget(self.rarity_filter)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # Item list
        self.item_list = QListWidget()
        self.item_list.itemClicked.connect(self.on_item_selected)
        layout.addWidget(self.item_list)
        
        # Summary
        self.summary_label = QLabel("No items")
        self.summary_label.setStyleSheet("color: gray; font-style: italic;")
        layout.addWidget(self.summary_label)
    
    def set_item_manager(self, item_manager: ItemManager):
        """Set the item manager"""
        self.item_manager = item_manager
        self.refresh_item_list()
    
    def refresh_item_list(self):
        """Refresh the item list display"""
        self.item_list.clear()
        
        if not self.item_manager:
            return
        
        # Get filter values
        type_filter = self.type_filter.currentData()
        rarity_filter = self.rarity_filter.currentData()
        
        # Filter items
        filtered_items = []
        for item in self.item_manager.items.values():
            if type_filter and item.item_type != type_filter:
                continue
            if rarity_filter and item.rarity != rarity_filter:
                continue
            filtered_items.append(item)
        
        # Sort by name
        filtered_items.sort(key=lambda x: x.name)
        
        # Add to list
        for item in filtered_items:
            list_item = QListWidgetItem()
            
            # Create display text
            rarity_color = {
                ItemRarity.COMMON: "#ffffff",
                ItemRarity.UNCOMMON: "#1eff00", 
                ItemRarity.RARE: "#0070dd",
                ItemRarity.EPIC: "#a335ee",
                ItemRarity.LEGENDARY: "#ff8000",
                ItemRarity.UNIQUE: "#e6cc80"
            }.get(item.rarity, "#ffffff")
            
            display_text = f"{item.name} ({item.item_type.value})"
            if item.value > 0:
                display_text += f" - {item.value}g"
            
            list_item.setText(display_text)
            list_item.setData(Qt.ItemDataRole.UserRole, item.id)
            
            # Set color based on rarity
            list_item.setForeground(Qt.GlobalColor.white)
            list_item.setBackground(Qt.GlobalColor.darkGray)
            
            self.item_list.addItem(list_item)
        
        # Update summary
        total = len(filtered_items)
        total_all = len(self.item_manager.items)
        if total == total_all:
            self.summary_label.setText(f"{total} items")
        else:
            self.summary_label.setText(f"{total} of {total_all} items")
    
    def on_item_selected(self, item: QListWidgetItem):
        """Handle item selection"""
        item_id = item.data(Qt.ItemDataRole.UserRole)
        if item_id:
            self.item_selected.emit(item_id)
    
    def add_item(self):
        """Add a new item"""
        if not self.item_manager:
            QMessageBox.warning(self, "Warning", "No item manager available. Please load a story first.")
            return
        
        try:
            # Create basic item
            item = self.item_manager.create_item(
                name="New Item",
                item_type=ItemType.TOOL
            )
            
            self.refresh_item_list()
            self.item_selected.emit(item.id)
            logger.info(f"Created new item: {item.id}")
            
        except Exception as e:
            logger.error(f"Error creating item: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create item: {e}")
    
    def import_item(self):
        """Import an item from another story"""
        if not self.item_manager:
            QMessageBox.warning(self, "Warning", "No item manager available. Please load a story first.")
            return
        
        try:
            # Show file dialog to select story file
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Story File to Import Item From",
                "data/storylines", "JSON files (*.json)"
            )
            
            if not file_path:
                return
            
            # Load the story
            from story.story_web import StoryWeb
            source_story = StoryWeb.load_from_file(file_path)
            if not source_story:
                QMessageBox.critical(self, "Error", "Failed to load story file")
                return
            
            # Get items from source story
            source_item_manager = source_story.get_item_manager()
            if not source_item_manager.items:
                QMessageBox.information(self, "No Items", "The selected story has no items to import.")
                return
            
            # Show item selection dialog
            from .item_import_dialog import ItemImportDialog
            dialog = ItemImportDialog(source_item_manager, self)
            
            if dialog.exec() == dialog.Accepted:
                selected_items = dialog.get_selected_items()
                
                imported_count = 0
                for item_data in selected_items:
                    if self.item_manager.import_item_data(item_data):
                        imported_count += 1
                
                if imported_count > 0:
                    self.refresh_item_list()
                    QMessageBox.information(
                        self, "Import Complete",
                        f"Successfully imported {imported_count} item(s)."
                    )
                else:
                    QMessageBox.warning(self, "Import Failed", "No items were imported.")
                    
        except Exception as e:
            logger.error(f"Error importing item: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import item: {e}")


class ItemDetailsWidget(QWidget):
    """Widget for editing item details"""
    
    item_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.item_manager = None
        self.current_item = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Create tabs for different aspects
        self.tab_widget = QTabWidget()
        
        # Basic Info Tab
        self.basic_tab = self.create_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "Basic Info")
        
        # Effects Tab
        self.effects_tab = self.create_effects_tab()
        self.tab_widget.addTab(self.effects_tab, "Effects")
        
        # Story Integration Tab
        self.story_tab = self.create_story_tab()
        self.tab_widget.addTab(self.story_tab, "Story Integration")
        
        layout.addWidget(self.tab_widget)
        
        # Action buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_btn = QPushButton("Save Changes")
        self.save_btn.clicked.connect(self.save_item)
        button_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("Delete Item")
        self.delete_btn.clicked.connect(self.delete_item)
        self.delete_btn.setStyleSheet("background-color: #ffcccc;")
        button_layout.addWidget(self.delete_btn)
        
        layout.addLayout(button_layout)
    
    def create_basic_tab(self) -> QWidget:
        """Create basic info tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.name_edit = QLineEdit()
        layout.addRow("Name:", self.name_edit)
        
        self.type_combo = QComboBox()
        for item_type in ItemType:
            self.type_combo.addItem(item_type.value.title(), item_type)
        layout.addRow("Type:", self.type_combo)
        
        self.rarity_combo = QComboBox()
        for rarity in ItemRarity:
            self.rarity_combo.addItem(rarity.value.title(), rarity)
        layout.addRow("Rarity:", self.rarity_combo)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        layout.addRow("Description:", self.description_edit)
        
        self.value_spin = QSpinBox()
        self.value_spin.setRange(0, 999999)
        layout.addRow("Value:", self.value_spin)
        
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(0.0, 999.9)
        self.weight_spin.setDecimals(1)
        layout.addRow("Weight:", self.weight_spin)
        
        self.stackable_check = QCheckBox()
        layout.addRow("Stackable:", self.stackable_check)
        
        self.max_stack_spin = QSpinBox()
        self.max_stack_spin.setRange(1, 9999)
        layout.addRow("Max Stack:", self.max_stack_spin)
        
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("Comma-separated tags")
        layout.addRow("Tags:", self.tags_edit)
        
        return widget
    
    def create_effects_tab(self) -> QWidget:
        """Create effects tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        layout.addWidget(QLabel("Item Effects (Advanced - JSON format):"))
        
        self.effects_edit = QTextEdit()
        self.effects_edit.setPlaceholderText("""
Example effects:
[
    {
        "effect_type": "stat_boost",
        "effect_value": {"strength": 5},
        "description": "Increases strength by 5",
        "duration": "permanent"
    },
    {
        "effect_type": "unlock_choice",
        "effect_value": "special_attack",
        "description": "Unlocks special attack option"
    }
]
        """.strip())
        layout.addWidget(self.effects_edit)
        
        return widget
    
    def create_story_tab(self) -> QWidget:
        """Create story integration tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.unlocks_choices_edit = QTextEdit()
        self.unlocks_choices_edit.setMaximumHeight(80)
        self.unlocks_choices_edit.setPlaceholderText("One choice ID per line")
        layout.addRow("Unlocks Choices:", self.unlocks_choices_edit)
        
        self.blocks_choices_edit = QTextEdit()
        self.blocks_choices_edit.setMaximumHeight(80)
        self.blocks_choices_edit.setPlaceholderText("One choice ID per line")
        layout.addRow("Blocks Choices:", self.blocks_choices_edit)
        
        self.story_flags_edit = QTextEdit()
        self.story_flags_edit.setMaximumHeight(80)
        self.story_flags_edit.setPlaceholderText("One flag per line")
        layout.addRow("Story Flags:", self.story_flags_edit)
        
        self.requirements_edit = QTextEdit()
        self.requirements_edit.setMaximumHeight(80)
        self.requirements_edit.setPlaceholderText('JSON format: {"strength": 10, "level": 5}')
        layout.addRow("Requirements:", self.requirements_edit)
        
        return widget
    
    def set_item_manager(self, item_manager: ItemManager):
        """Set the item manager"""
        self.item_manager = item_manager
    
    def load_item(self, item_id: str):
        """Load item data into the form"""
        if not self.item_manager:
            return
        
        item = self.item_manager.get_item(item_id)
        if not item:
            return
        
        self.current_item = item
        
        # Basic info
        self.name_edit.setText(item.name)
        type_index = self.type_combo.findData(item.item_type)
        if type_index >= 0:
            self.type_combo.setCurrentIndex(type_index)
        rarity_index = self.rarity_combo.findData(item.rarity)
        if rarity_index >= 0:
            self.rarity_combo.setCurrentIndex(rarity_index)
        self.description_edit.setPlainText(item.description)
        self.value_spin.setValue(item.value)
        self.weight_spin.setValue(item.weight)
        self.stackable_check.setChecked(item.stackable)
        self.max_stack_spin.setValue(item.max_stack)
        self.tags_edit.setText(", ".join(item.tags))
        
        # Effects
        import json
        try:
            effects_data = [
                {
                    "effect_type": effect.effect_type,
                    "effect_value": effect.effect_value,
                    "description": effect.description,
                    "duration": effect.duration,
                    "conditions": effect.conditions
                }
                for effect in item.effects
            ]
            self.effects_edit.setPlainText(json.dumps(effects_data, indent=2))
        except Exception:
            self.effects_edit.setPlainText("[]")
        
        # Story integration
        self.unlocks_choices_edit.setPlainText("\n".join(item.unlocks_choices))
        self.blocks_choices_edit.setPlainText("\n".join(item.blocks_choices))
        self.story_flags_edit.setPlainText("\n".join(item.story_flags))
        
        try:
            self.requirements_edit.setPlainText(json.dumps(item.requirements, indent=2))
        except Exception:
            self.requirements_edit.setPlainText("{}")
    
    def save_item(self):
        """Save item changes"""
        if not self.current_item:
            return
        
        try:
            # Update basic info
            self.current_item.name = self.name_edit.text()
            self.current_item.item_type = self.type_combo.currentData()
            self.current_item.rarity = self.rarity_combo.currentData()
            self.current_item.description = self.description_edit.toPlainText()
            self.current_item.value = self.value_spin.value()
            self.current_item.weight = self.weight_spin.value()
            self.current_item.stackable = self.stackable_check.isChecked()
            self.current_item.max_stack = self.max_stack_spin.value()
            
            tags_text = self.tags_edit.text().strip()
            self.current_item.tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
            
            # Update effects
            import json
            try:
                effects_data = json.loads(self.effects_edit.toPlainText())
                effects = []
                for effect_data in effects_data:
                    effects.append(ItemEffect(**effect_data))
                self.current_item.effects = effects
            except Exception as e:
                QMessageBox.warning(self, "Effects Error", f"Invalid effects JSON: {e}")
                return
            
            # Update story integration
            unlocks_text = self.unlocks_choices_edit.toPlainText().strip()
            self.current_item.unlocks_choices = [c.strip() for c in unlocks_text.split("\n") if c.strip()]
            
            blocks_text = self.blocks_choices_edit.toPlainText().strip()
            self.current_item.blocks_choices = [c.strip() for c in blocks_text.split("\n") if c.strip()]
            
            flags_text = self.story_flags_edit.toPlainText().strip()
            self.current_item.story_flags = [f.strip() for f in flags_text.split("\n") if f.strip()]
            
            try:
                requirements_text = self.requirements_edit.toPlainText().strip()
                self.current_item.requirements = json.loads(requirements_text) if requirements_text else {}
            except Exception as e:
                QMessageBox.warning(self, "Requirements Error", f"Invalid requirements JSON: {e}")
                return
            
            self.item_changed.emit()
            QMessageBox.information(self, "Success", "Item saved successfully!")
            
        except Exception as e:
            logger.error(f"Error saving item: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save item: {e}")
    
    def delete_item(self):
        """Delete the current item"""
        if not self.current_item:
            return
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete '{self.current_item.name}'?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.item_manager:
                del self.item_manager.items[self.current_item.id]
                self.current_item = None
                self.item_changed.emit()
                QMessageBox.information(self, "Success", "Item deleted successfully!")


class ItemsTab(QWidget):
    """Main items tab"""
    
    def __init__(self, config: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.config = config
        self.item_manager = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QHBoxLayout(self)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - item list
        self.item_list_widget = ItemListWidget()
        self.item_list_widget.item_selected.connect(self.on_item_selected)
        splitter.addWidget(self.item_list_widget)
        
        # Right side - item details
        self.item_details_widget = ItemDetailsWidget()
        self.item_details_widget.item_changed.connect(self.on_item_changed)
        splitter.addWidget(self.item_details_widget)
        
        # Set splitter proportions
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
    
    def set_story(self, story_web):
        """Set the current story"""
        if story_web:
            self.item_manager = story_web.get_item_manager()
            self.item_list_widget.set_item_manager(self.item_manager)
            self.item_details_widget.set_item_manager(self.item_manager)
    
    def on_item_selected(self, item_id: str):
        """Handle item selection"""
        self.item_details_widget.load_item(item_id)
    
    def on_item_changed(self):
        """Handle item changes"""
        self.item_list_widget.refresh_item_list()
