"""
Character Editor - GUI for managing story characters
Handles character creation, editing, and consistency tracking
"""

import logging
from typing import Dict, List, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QLineEdit, QTextEdit, QComboBox, QPushButton, QListWidget,
    QListWidgetItem, QFormLayout, QSpinBox, QCheckBox, QMessageBox,
    QSplitter, QScrollArea, QFileDialog, QDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont

from story.character_system import (
    CharacterManager, Character, CharacterRole, CharacterAppearance,
    CharacterVoice, CharacterPersonality, CharacterStatus
)
from story.character_randomizer import CharacterRandomizer

logger = logging.getLogger(__name__)


class CharacterListWidget(QWidget):
    """Widget for displaying and selecting characters"""
    
    character_selected = pyqtSignal(str)  # character_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.character_manager = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("Characters"))
        
        self.add_character_btn = QPushButton("Add Character")
        self.add_character_btn.clicked.connect(self.add_character)
        header_layout.addWidget(self.add_character_btn)

        self.import_character_btn = QPushButton("Import Character")
        self.import_character_btn.clicked.connect(self.import_character)
        header_layout.addWidget(self.import_character_btn)
        
        layout.addLayout(header_layout)
        
        # Character list
        self.character_list = QListWidget()
        self.character_list.itemClicked.connect(self.on_character_selected)
        layout.addWidget(self.character_list)
        
        # Summary
        self.summary_label = QLabel("No characters")
        self.summary_label.setStyleSheet("color: gray; font-style: italic;")
        layout.addWidget(self.summary_label)
    
    def set_character_manager(self, char_manager: CharacterManager):
        """Set the character manager"""
        self.character_manager = char_manager
        self.refresh_character_list()
    
    def refresh_character_list(self):
        """Refresh the character list display"""
        self.character_list.clear()
        
        if not self.character_manager:
            return
        
        for character in self.character_manager.characters.values():
            item = QListWidgetItem()
            item.setText(f"{character.name} ({character.role.value})")
            item.setData(Qt.ItemDataRole.UserRole, character.id)
            
            # Color code by importance
            if character.importance_level >= 4:
                item.setBackground(Qt.GlobalColor.lightGray)
            
            self.character_list.addItem(item)
        
        # Update summary
        total = len(self.character_manager.characters)
        self.summary_label.setText(f"{total} characters")
    
    def on_character_selected(self, item: QListWidgetItem):
        """Handle character selection"""
        character_id = item.data(Qt.ItemDataRole.UserRole)
        if character_id:
            self.character_selected.emit(character_id)
    
    def add_character(self):
        """Add a new character"""
        if not self.character_manager:
            QMessageBox.warning(self, "Warning", "No character manager available. Please load a story first.")
            return

        try:
            # Create basic character
            character = self.character_manager.create_character(
                name="New Character",
                role=CharacterRole.NEUTRAL
            )

            self.refresh_character_list()
            self.character_selected.emit(character.id)
            logger.info(f"Created new character: {character.id}")

        except Exception as e:
            logger.error(f"Error creating character: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create character: {e}")

    def import_character(self):
        """Import a character from another story"""
        if not self.character_manager:
            QMessageBox.warning(self, "Warning", "No character manager available. Please load a story first.")
            return

        try:
            # Show file dialog to select story file
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Story File to Import Character From",
                "data/storylines", "JSON files (*.json)"
            )

            if not file_path:
                return

            # Load the story
            from story.story_web import StoryWeb
            source_story = StoryWeb.load_from_file(file_path)
            if not source_story:
                QMessageBox.critical(self, "Error", "Failed to load story file")
                return

            # Get characters from source story
            source_char_manager = source_story.get_character_manager()
            if not source_char_manager.characters:
                QMessageBox.information(self, "No Characters", "The selected story has no characters to import.")
                return

            # Show character selection dialog
            from .character_import_dialog import CharacterImportDialog
            dialog = CharacterImportDialog(source_char_manager, self)

            if dialog.exec() == dialog.Accepted:
                selected_characters = dialog.get_selected_characters()

                imported_count = 0
                for char_data in selected_characters:
                    if self.character_manager.import_character_data(char_data):
                        imported_count += 1

                if imported_count > 0:
                    self.refresh_character_list()
                    QMessageBox.information(
                        self, "Import Complete",
                        f"Successfully imported {imported_count} character(s)."
                    )
                else:
                    QMessageBox.warning(self, "Import Failed", "No characters were imported.")

        except Exception as e:
            logger.error(f"Error importing character: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import character: {e}")


class CharacterDetailsWidget(QWidget):
    """Widget for editing character details"""
    
    character_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.character_manager = None
        self.current_character = None
        self.character_randomizer = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Create tabs for different aspects
        self.tab_widget = QTabWidget()
        
        # Basic Info Tab
        self.basic_tab = self.create_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "Basic Info")
        
        # Appearance Tab
        self.appearance_tab = self.create_appearance_tab()
        self.tab_widget.addTab(self.appearance_tab, "Appearance")
        
        # Voice Tab
        self.voice_tab = self.create_voice_tab()
        self.tab_widget.addTab(self.voice_tab, "Voice")
        
        # Personality Tab
        self.personality_tab = self.create_personality_tab()
        self.tab_widget.addTab(self.personality_tab, "Personality")
        
        layout.addWidget(self.tab_widget)
        
        # Action buttons
        button_layout = QHBoxLayout()

        # Randomize buttons
        self.randomize_all_btn = QPushButton("🎲 Randomize All")
        self.randomize_all_btn.clicked.connect(self.randomize_all_character)
        self.randomize_all_btn.setStyleSheet("background-color: #e3f2fd; color: #1976d2; font-weight: bold;")
        self.randomize_all_btn.setToolTip("Randomize all character aspects using AI and presets")
        button_layout.addWidget(self.randomize_all_btn)

        self.randomize_current_btn = QPushButton("🎯 Randomize Current Tab")
        self.randomize_current_btn.clicked.connect(self.randomize_current_tab)
        self.randomize_current_btn.setStyleSheet("background-color: #f3e5f5; color: #7b1fa2;")
        self.randomize_current_btn.setToolTip("Randomize only the current tab's fields")
        button_layout.addWidget(self.randomize_current_btn)

        button_layout.addStretch()

        self.save_btn = QPushButton("Save Changes")
        self.save_btn.clicked.connect(self.save_character)
        button_layout.addWidget(self.save_btn)

        self.delete_btn = QPushButton("Delete Character")
        self.delete_btn.clicked.connect(self.delete_character)
        self.delete_btn.setStyleSheet("background-color: #ffcccc;")
        button_layout.addWidget(self.delete_btn)

        layout.addLayout(button_layout)
    
    def create_basic_tab(self) -> QWidget:
        """Create basic info tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.name_edit = QLineEdit()
        layout.addRow("Name:", self.name_edit)
        
        self.role_combo = QComboBox()
        for role in CharacterRole:
            self.role_combo.addItem(role.value.title(), role)
        layout.addRow("Role:", self.role_combo)
        
        self.importance_spin = QSpinBox()
        self.importance_spin.setRange(1, 5)
        self.importance_spin.setValue(3)
        layout.addRow("Importance (1-5):", self.importance_spin)
        
        self.is_player_check = QCheckBox()
        layout.addRow("Player Character:", self.is_player_check)
        
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("Comma-separated tags")
        layout.addRow("Tags:", self.tags_edit)
        
        return widget
    
    def create_appearance_tab(self) -> QWidget:
        """Create appearance tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.physical_desc_edit = QTextEdit()
        self.physical_desc_edit.setMaximumHeight(100)
        layout.addRow("Physical Description:", self.physical_desc_edit)
        
        self.age_range_edit = QLineEdit()
        layout.addRow("Age Range:", self.age_range_edit)
        
        self.hair_color_edit = QLineEdit()
        layout.addRow("Hair Color:", self.hair_color_edit)
        
        self.eye_color_edit = QLineEdit()
        layout.addRow("Eye Color:", self.eye_color_edit)
        
        self.clothing_edit = QLineEdit()
        layout.addRow("Clothing:", self.clothing_edit)
        
        self.distinctive_edit = QTextEdit()
        self.distinctive_edit.setMaximumHeight(60)
        layout.addRow("Distinctive Features:", self.distinctive_edit)
        
        return widget
    
    def create_voice_tab(self) -> QWidget:
        """Create voice tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.voice_desc_edit = QTextEdit()
        self.voice_desc_edit.setMaximumHeight(80)
        layout.addRow("Voice Description:", self.voice_desc_edit)
        
        self.accent_edit = QLineEdit()
        layout.addRow("Accent:", self.accent_edit)
        
        self.vocab_style_edit = QLineEdit()
        self.vocab_style_edit.setPlaceholderText("formal, casual, archaic, etc.")
        layout.addRow("Vocabulary Style:", self.vocab_style_edit)
        
        self.speech_patterns_edit = QTextEdit()
        self.speech_patterns_edit.setMaximumHeight(80)
        self.speech_patterns_edit.setPlaceholderText("One pattern per line")
        layout.addRow("Speech Patterns:", self.speech_patterns_edit)
        
        return widget
    
    def create_personality_tab(self) -> QWidget:
        """Create personality tab"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.traits_edit = QTextEdit()
        self.traits_edit.setMaximumHeight(80)
        self.traits_edit.setPlaceholderText("One trait per line")
        layout.addRow("Personality Traits:", self.traits_edit)
        
        self.motivations_edit = QTextEdit()
        self.motivations_edit.setMaximumHeight(80)
        self.motivations_edit.setPlaceholderText("One motivation per line")
        layout.addRow("Motivations:", self.motivations_edit)
        
        self.fears_edit = QTextEdit()
        self.fears_edit.setMaximumHeight(80)
        self.fears_edit.setPlaceholderText("One fear per line")
        layout.addRow("Fears:", self.fears_edit)
        
        self.goals_edit = QTextEdit()
        self.goals_edit.setMaximumHeight(80)
        self.goals_edit.setPlaceholderText("One goal per line")
        layout.addRow("Goals:", self.goals_edit)
        
        self.moral_alignment_edit = QLineEdit()
        self.moral_alignment_edit.setPlaceholderText("good, evil, neutral, etc.")
        layout.addRow("Moral Alignment:", self.moral_alignment_edit)
        
        self.background_edit = QTextEdit()
        self.background_edit.setMaximumHeight(100)
        layout.addRow("Background Story:", self.background_edit)
        
        return widget
    
    def set_character_manager(self, char_manager: CharacterManager, config: dict = None, lmstudio_client=None):
        """Set the character manager and initialize randomizer"""
        self.character_manager = char_manager

        # Initialize character randomizer
        if config and lmstudio_client:
            self.character_randomizer = CharacterRandomizer(config, lmstudio_client)
        elif config:
            self.character_randomizer = CharacterRandomizer(config)
        else:
            self.character_randomizer = CharacterRandomizer({})
    
    def load_character(self, character_id: str):
        """Load character data into the form"""
        if not self.character_manager:
            return
        
        character = self.character_manager.get_character(character_id)
        if not character:
            return
        
        self.current_character = character
        
        # Basic info
        self.name_edit.setText(character.name)
        role_index = self.role_combo.findData(character.role)
        if role_index >= 0:
            self.role_combo.setCurrentIndex(role_index)
        self.importance_spin.setValue(character.importance_level)
        self.is_player_check.setChecked(character.is_player_character)
        self.tags_edit.setText(", ".join(character.tags))
        
        # Appearance
        self.physical_desc_edit.setPlainText(character.appearance.physical_description)
        self.age_range_edit.setText(character.appearance.age_range)
        self.hair_color_edit.setText(character.appearance.hair_color)
        self.eye_color_edit.setText(character.appearance.eye_color)
        self.clothing_edit.setText(character.appearance.clothing)
        self.distinctive_edit.setPlainText(character.appearance.distinctive_features)
        
        # Voice
        self.voice_desc_edit.setPlainText(character.voice.voice_description)
        self.accent_edit.setText(character.voice.accent)
        self.vocab_style_edit.setText(character.voice.vocabulary_style)
        self.speech_patterns_edit.setPlainText("\n".join(character.voice.speech_patterns))
        
        # Personality
        self.traits_edit.setPlainText("\n".join(character.personality.traits))
        self.motivations_edit.setPlainText("\n".join(character.personality.motivations))
        self.fears_edit.setPlainText("\n".join(character.personality.fears))
        self.goals_edit.setPlainText("\n".join(character.personality.goals))
        self.moral_alignment_edit.setText(character.personality.moral_alignment)
        self.background_edit.setPlainText(character.personality.background_story)
    
    def save_character(self):
        """Save character changes"""
        if not self.current_character:
            return
        
        try:
            # Update basic info
            self.current_character.name = self.name_edit.text()
            self.current_character.role = self.role_combo.currentData()
            self.current_character.importance_level = self.importance_spin.value()
            self.current_character.is_player_character = self.is_player_check.isChecked()
            
            tags_text = self.tags_edit.text().strip()
            self.current_character.tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
            
            # Update appearance
            self.current_character.appearance.physical_description = self.physical_desc_edit.toPlainText()
            self.current_character.appearance.age_range = self.age_range_edit.text()
            self.current_character.appearance.hair_color = self.hair_color_edit.text()
            self.current_character.appearance.eye_color = self.eye_color_edit.text()
            self.current_character.appearance.clothing = self.clothing_edit.text()
            self.current_character.appearance.distinctive_features = self.distinctive_edit.toPlainText()
            
            # Update voice
            self.current_character.voice.voice_description = self.voice_desc_edit.toPlainText()
            self.current_character.voice.accent = self.accent_edit.text()
            self.current_character.voice.vocabulary_style = self.vocab_style_edit.text()
            
            patterns_text = self.speech_patterns_edit.toPlainText().strip()
            self.current_character.voice.speech_patterns = [p.strip() for p in patterns_text.split("\n") if p.strip()]
            
            # Update personality
            traits_text = self.traits_edit.toPlainText().strip()
            self.current_character.personality.traits = [t.strip() for t in traits_text.split("\n") if t.strip()]
            
            motivations_text = self.motivations_edit.toPlainText().strip()
            self.current_character.personality.motivations = [m.strip() for m in motivations_text.split("\n") if m.strip()]
            
            fears_text = self.fears_edit.toPlainText().strip()
            self.current_character.personality.fears = [f.strip() for f in fears_text.split("\n") if f.strip()]
            
            goals_text = self.goals_edit.toPlainText().strip()
            self.current_character.personality.goals = [g.strip() for g in goals_text.split("\n") if g.strip()]
            
            self.current_character.personality.moral_alignment = self.moral_alignment_edit.text()
            self.current_character.personality.background_story = self.background_edit.toPlainText()
            
            self.character_changed.emit()
            QMessageBox.information(self, "Success", "Character saved successfully!")
            
        except Exception as e:
            logger.error(f"Error saving character: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save character: {e}")
    
    def delete_character(self):
        """Delete the current character"""
        if not self.current_character:
            return
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete '{self.current_character.name}'?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.character_manager:
                del self.character_manager.characters[self.current_character.id]
                self.current_character = None
                self.character_changed.emit()
                QMessageBox.information(self, "Success", "Character deleted successfully!")

    def randomize_all_character(self):
        """Randomize all character aspects"""
        if not self.character_randomizer or not self.current_character:
            QMessageBox.warning(self, "Warning", "No character selected or randomizer not available")
            return

        try:
            # Get randomized data
            random_data = self.character_randomizer.randomize_all()

            # Apply basic info
            basic = random_data["basic"]
            self.name_edit.setText(basic["name"])
            self.importance_spin.setValue(basic["importance_level"])
            self.is_player_check.setChecked(basic["is_player_character"])
            self.tags_edit.setText(", ".join(basic["tags"]))

            # Apply appearance
            appearance = random_data["appearance"]
            self.physical_desc_edit.setPlainText(appearance["physical_description"])
            self.age_range_edit.setText(appearance["age_range"])
            self.hair_color_edit.setText(appearance["hair_color"])
            self.eye_color_edit.setText(appearance["eye_color"])
            self.clothing_edit.setText(appearance["clothing"])
            self.distinctive_edit.setPlainText(appearance["distinctive_features"])

            # Apply voice
            voice = random_data["voice"]
            self.voice_desc_edit.setPlainText(voice["voice_description"])
            self.accent_edit.setText(voice["accent"])
            self.vocab_style_edit.setText(voice["vocabulary_style"])
            self.speech_patterns_edit.setPlainText("\n".join(voice["speech_patterns"]))

            # Apply personality
            personality = random_data["personality"]
            self.traits_edit.setPlainText("\n".join(personality["traits"]))
            self.motivations_edit.setPlainText("\n".join(personality["motivations"]))
            self.fears_edit.setPlainText("\n".join(personality["fears"]))
            self.goals_edit.setPlainText("\n".join(personality["goals"]))
            self.moral_alignment_edit.setText(personality["moral_alignment"])
            self.background_edit.setPlainText(personality["background_story"])

            QMessageBox.information(self, "Success", "Character randomized successfully!")

        except Exception as e:
            logger.error(f"Error randomizing character: {e}")
            QMessageBox.critical(self, "Error", f"Failed to randomize character: {e}")

    def randomize_current_tab(self):
        """Randomize only the current tab's fields"""
        if not self.character_randomizer or not self.current_character:
            QMessageBox.warning(self, "Warning", "No character selected or randomizer not available")
            return

        try:
            current_tab_index = self.tab_widget.currentIndex()
            tab_name = self.tab_widget.tabText(current_tab_index)

            if tab_name == "Basic Info":
                basic = self.character_randomizer.randomize_basic_info()
                self.name_edit.setText(basic["name"])
                self.importance_spin.setValue(basic["importance_level"])
                self.is_player_check.setChecked(basic["is_player_character"])
                self.tags_edit.setText(", ".join(basic["tags"]))

            elif tab_name == "Appearance":
                appearance = self.character_randomizer.randomize_appearance()
                self.physical_desc_edit.setPlainText(appearance["physical_description"])
                self.age_range_edit.setText(appearance["age_range"])
                self.hair_color_edit.setText(appearance["hair_color"])
                self.eye_color_edit.setText(appearance["eye_color"])
                self.clothing_edit.setText(appearance["clothing"])
                self.distinctive_edit.setPlainText(appearance["distinctive_features"])

            elif tab_name == "Voice":
                voice = self.character_randomizer.randomize_voice()
                self.voice_desc_edit.setPlainText(voice["voice_description"])
                self.accent_edit.setText(voice["accent"])
                self.vocab_style_edit.setText(voice["vocabulary_style"])
                self.speech_patterns_edit.setPlainText("\n".join(voice["speech_patterns"]))

            elif tab_name == "Personality":
                personality = self.character_randomizer.randomize_personality()
                self.traits_edit.setPlainText("\n".join(personality["traits"]))
                self.motivations_edit.setPlainText("\n".join(personality["motivations"]))
                self.fears_edit.setPlainText("\n".join(personality["fears"]))
                self.goals_edit.setPlainText("\n".join(personality["goals"]))
                self.moral_alignment_edit.setText(personality["moral_alignment"])
                self.background_edit.setPlainText(personality["background_story"])

            QMessageBox.information(self, "Success", f"{tab_name} randomized successfully!")

        except Exception as e:
            logger.error(f"Error randomizing current tab: {e}")
            QMessageBox.critical(self, "Error", f"Failed to randomize current tab: {e}")


class CharacterEditorTab(QWidget):
    """Main character editor tab"""

    def __init__(self, config: Dict[str, Any], lmstudio_client=None, parent=None):
        super().__init__(parent)
        self.config = config
        self.lmstudio_client = lmstudio_client
        self.character_manager = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QHBoxLayout(self)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - character list
        self.character_list_widget = CharacterListWidget()
        self.character_list_widget.character_selected.connect(self.on_character_selected)
        splitter.addWidget(self.character_list_widget)
        
        # Right side - character details
        self.character_details_widget = CharacterDetailsWidget()
        self.character_details_widget.character_changed.connect(self.on_character_changed)
        splitter.addWidget(self.character_details_widget)
        
        # Set splitter proportions
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
    
    def set_story(self, story_web):
        """Set the current story"""
        if story_web:
            self.character_manager = story_web.get_character_manager()
            self.character_list_widget.set_character_manager(self.character_manager)
            self.character_details_widget.set_character_manager(
                self.character_manager,
                self.config,
                self.lmstudio_client
            )
    
    def on_character_selected(self, character_id: str):
        """Handle character selection"""
        self.character_details_widget.load_character(character_id)
    
    def on_character_changed(self):
        """Handle character changes"""
        self.character_list_widget.refresh_character_list()
