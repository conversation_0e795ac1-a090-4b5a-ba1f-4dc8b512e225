"""
Maze Creation Tab - Generate mazes and place story nodes at intersections
"""

import logging
import random
from typing import Dict, <PERSON>, <PERSON>, <PERSON><PERSON>, Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QComboBox, QSpinBox, QSlider, QPushButton, QLabel, QTextEdit,
    QSplitter, QScrollArea, QFrame, QMessageBox, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont

logger = logging.getLogger(__name__)


class MazeGenerator:
    """Generate different types of mazes"""
    
    def __init__(self, width: int, height: int):
        self.width = width
        self.height = height
        self.maze = [[1 for _ in range(width)] for _ in range(height)]  # 1 = wall, 0 = path
        self.intersections = []
    
    def generate_recursive_backtrack(self) -> List[List[int]]:
        """Generate maze using recursive backtracking algorithm"""
        # Start with all walls
        self.maze = [[1 for _ in range(self.width)] for _ in range(self.height)]
        
        # Make sure dimensions are odd for proper maze generation
        if self.width % 2 == 0:
            self.width -= 1
        if self.height % 2 == 0:
            self.height -= 1
        
        # Start from (1,1)
        start_x, start_y = 1, 1
        self.maze[start_y][start_x] = 0
        
        stack = [(start_x, start_y)]
        
        while stack:
            current_x, current_y = stack[-1]
            neighbors = self._get_unvisited_neighbors(current_x, current_y)
            
            if neighbors:
                next_x, next_y = random.choice(neighbors)
                # Remove wall between current and next
                wall_x = current_x + (next_x - current_x) // 2
                wall_y = current_y + (next_y - current_y) // 2
                self.maze[wall_y][wall_x] = 0
                self.maze[next_y][next_x] = 0
                stack.append((next_x, next_y))
            else:
                stack.pop()
        
        self._find_intersections()
        return self.maze
    
    def generate_binary_tree(self) -> List[List[int]]:
        """Generate maze using binary tree algorithm"""
        self.maze = [[1 for _ in range(self.width)] for _ in range(self.height)]
        
        for y in range(1, self.height, 2):
            for x in range(1, self.width, 2):
                self.maze[y][x] = 0
                
                # Randomly choose to carve north or east
                directions = []
                if y > 1:  # Can go north
                    directions.append('north')
                if x < self.width - 2:  # Can go east
                    directions.append('east')
                
                if directions:
                    direction = random.choice(directions)
                    if direction == 'north':
                        self.maze[y-1][x] = 0
                    else:  # east
                        self.maze[y][x+1] = 0
        
        self._find_intersections()
        return self.maze
    
    def generate_cellular_automata(self) -> List[List[int]]:
        """Generate maze using cellular automata"""
        # Start with random noise
        for y in range(self.height):
            for x in range(self.width):
                self.maze[y][x] = 1 if random.random() < 0.45 else 0
        
        # Apply cellular automata rules
        for _ in range(5):
            new_maze = [[0 for _ in range(self.width)] for _ in range(self.height)]
            for y in range(self.height):
                for x in range(self.width):
                    wall_count = self._count_walls(x, y)
                    if wall_count >= 4:
                        new_maze[y][x] = 1
                    else:
                        new_maze[y][x] = 0
            self.maze = new_maze
        
        self._find_intersections()
        return self.maze
    
    def _get_unvisited_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """Get unvisited neighbors for recursive backtracking"""
        neighbors = []
        directions = [(0, -2), (2, 0), (0, 2), (-2, 0)]  # North, East, South, West
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if (0 < nx < self.width - 1 and 0 < ny < self.height - 1 and 
                self.maze[ny][nx] == 1):
                neighbors.append((nx, ny))
        
        return neighbors
    
    def _count_walls(self, x: int, y: int) -> int:
        """Count walls around a cell for cellular automata"""
        count = 0
        for dy in range(-1, 2):
            for dx in range(-1, 2):
                nx, ny = x + dx, y + dy
                if nx < 0 or ny < 0 or nx >= self.width or ny >= self.height:
                    count += 1
                elif self.maze[ny][nx] == 1:
                    count += 1
        return count
    
    def _find_intersections(self):
        """Find intersections (cells with 3+ path neighbors)"""
        self.intersections = []
        for y in range(1, self.height - 1):
            for x in range(1, self.width - 1):
                if self.maze[y][x] == 0:  # Is a path
                    path_neighbors = 0
                    for dx, dy in [(0, -1), (1, 0), (0, 1), (-1, 0)]:
                        nx, ny = x + dx, y + dy
                        if (0 <= nx < self.width and 0 <= ny < self.height and 
                            self.maze[ny][nx] == 0):
                            path_neighbors += 1
                    
                    if path_neighbors >= 3:
                        self.intersections.append((x, y))


class MazeWidget(QWidget):
    """Widget to display and interact with maze"""
    
    intersection_clicked = pyqtSignal(int, int)  # x, y coordinates
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.maze = None
        self.intersections = []
        self.node_positions = {}  # {(x, y): node_data}
        self.cell_size = 20
        self.setMinimumSize(400, 400)
    
    def set_maze(self, maze: List[List[int]], intersections: List[Tuple[int, int]]):
        """Set the maze to display"""
        self.maze = maze
        self.intersections = intersections
        self.node_positions = {}
        self.update()
    
    def paintEvent(self, event):
        """Paint the maze"""
        if not self.maze:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw maze
        for y, row in enumerate(self.maze):
            for x, cell in enumerate(row):
                rect_x = x * self.cell_size
                rect_y = y * self.cell_size
                
                if cell == 1:  # Wall
                    painter.fillRect(rect_x, rect_y, self.cell_size, self.cell_size, QColor(50, 50, 50))
                else:  # Path
                    painter.fillRect(rect_x, rect_y, self.cell_size, self.cell_size, QColor(240, 240, 240))
        
        # Draw intersections
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.setBrush(QBrush(QColor(255, 100, 100, 150)))
        for x, y in self.intersections:
            rect_x = x * self.cell_size + 2
            rect_y = y * self.cell_size + 2
            painter.drawEllipse(rect_x, rect_y, self.cell_size - 4, self.cell_size - 4)
        
        # Draw story nodes
        painter.setPen(QPen(QColor(0, 100, 255), 2))
        painter.setBrush(QBrush(QColor(100, 150, 255)))
        painter.setFont(QFont("Arial", 8))
        for (x, y), node_data in self.node_positions.items():
            rect_x = x * self.cell_size
            rect_y = y * self.cell_size
            painter.drawRect(rect_x, rect_y, self.cell_size, self.cell_size)
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(rect_x + 2, rect_y + 12, str(node_data.get('id', '?')))
            painter.setPen(QPen(QColor(0, 100, 255), 2))
    
    def mousePressEvent(self, event):
        """Handle mouse clicks on intersections"""
        if not self.maze or event.button() != Qt.MouseButton.LeftButton:
            return
        
        x = event.position().x() // self.cell_size
        y = event.position().y() // self.cell_size
        
        if (int(x), int(y)) in self.intersections:
            self.intersection_clicked.emit(int(x), int(y))
    
    def add_node_at_intersection(self, x: int, y: int, node_data: Dict[str, Any]):
        """Add a story node at an intersection"""
        if (x, y) in self.intersections:
            self.node_positions[(x, y)] = node_data
            self.update()
    
    def remove_node_at_intersection(self, x: int, y: int):
        """Remove a story node from an intersection"""
        if (x, y) in self.node_positions:
            del self.node_positions[(x, y)]
            self.update()


class MazeCreationTab(QWidget):
    """Tab for creating maze-based stories"""
    
    maze_created = pyqtSignal(dict)  # Emit maze data
    
    def __init__(self, config: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.config = config
        self.current_maze = None
        self.current_intersections = []
        self.node_counter = 1
        
        self._setup_ui()
        logger.info("Maze creation tab initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QHBoxLayout(self)
        
        # Left panel - controls
        controls_panel = self._create_controls_panel()
        layout.addWidget(controls_panel)
        
        # Right panel - maze display
        maze_panel = self._create_maze_panel()
        layout.addWidget(maze_panel)
        
        # Set proportions
        layout.setStretch(0, 1)  # Controls take 1/3
        layout.setStretch(1, 2)  # Maze takes 2/3
    
    def _create_controls_panel(self) -> QWidget:
        """Create the controls panel"""
        panel = QWidget()
        panel.setMaximumWidth(350)
        layout = QVBoxLayout(panel)
        
        # Maze generation settings
        gen_group = QGroupBox("🌀 Maze Generation")
        gen_layout = QFormLayout(gen_group)
        
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.addItems([
            "Recursive Backtracking",
            "Binary Tree", 
            "Cellular Automata"
        ])
        gen_layout.addRow("Algorithm:", self.algorithm_combo)
        
        self.width_spin = QSpinBox()
        self.width_spin.setRange(11, 101)
        self.width_spin.setValue(31)
        self.width_spin.setSingleStep(2)  # Keep odd numbers
        gen_layout.addRow("Width:", self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(11, 101)
        self.height_spin.setValue(31)
        self.height_spin.setSingleStep(2)  # Keep odd numbers
        gen_layout.addRow("Height:", self.height_spin)
        
        self.complexity_slider = QSlider(Qt.Orientation.Horizontal)
        self.complexity_slider.setRange(1, 10)
        self.complexity_slider.setValue(5)
        gen_layout.addRow("Complexity:", self.complexity_slider)
        
        self.generate_btn = QPushButton("🌀 Generate Maze")
        self.generate_btn.clicked.connect(self._generate_maze)
        gen_layout.addRow(self.generate_btn)
        
        layout.addWidget(gen_group)
        
        # Node placement
        node_group = QGroupBox("📍 Story Nodes")
        node_layout = QVBoxLayout(node_group)
        
        self.intersection_info = QLabel("Click red circles to place story nodes")
        self.intersection_info.setWordWrap(True)
        self.intersection_info.setStyleSheet("color: #666; font-style: italic;")
        node_layout.addWidget(self.intersection_info)
        
        self.auto_place_btn = QPushButton("🎯 Auto-place Nodes")
        self.auto_place_btn.clicked.connect(self._auto_place_nodes)
        self.auto_place_btn.setEnabled(False)
        node_layout.addWidget(self.auto_place_btn)
        
        self.clear_nodes_btn = QPushButton("🗑️ Clear All Nodes")
        self.clear_nodes_btn.clicked.connect(self._clear_nodes)
        self.clear_nodes_btn.setEnabled(False)
        node_layout.addWidget(self.clear_nodes_btn)
        
        layout.addWidget(node_group)
        
        # Export options
        export_group = QGroupBox("📤 Export")
        export_layout = QVBoxLayout(export_group)
        
        self.export_story_btn = QPushButton("📖 Export as Story")
        self.export_story_btn.clicked.connect(self._export_as_story)
        self.export_story_btn.setEnabled(False)
        export_layout.addWidget(self.export_story_btn)
        
        self.export_image_btn = QPushButton("🖼️ Export as Image")
        self.export_image_btn.clicked.connect(self._export_as_image)
        self.export_image_btn.setEnabled(False)
        export_layout.addWidget(self.export_image_btn)
        
        layout.addWidget(export_group)
        
        layout.addStretch()
        return panel
    
    def _create_maze_panel(self) -> QWidget:
        """Create the maze display panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Maze display
        scroll_area = QScrollArea()
        self.maze_widget = MazeWidget()
        self.maze_widget.intersection_clicked.connect(self._on_intersection_clicked)
        scroll_area.setWidget(self.maze_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return panel

    def _generate_maze(self):
        """Generate a new maze"""
        try:
            width = self.width_spin.value()
            height = self.height_spin.value()
            algorithm = self.algorithm_combo.currentText()

            # Ensure odd dimensions for proper maze generation
            if width % 2 == 0:
                width += 1
                self.width_spin.setValue(width)
            if height % 2 == 0:
                height += 1
                self.height_spin.setValue(height)

            generator = MazeGenerator(width, height)

            if algorithm == "Recursive Backtracking":
                maze = generator.generate_recursive_backtrack()
            elif algorithm == "Binary Tree":
                maze = generator.generate_binary_tree()
            else:  # Cellular Automata
                maze = generator.generate_cellular_automata()

            self.current_maze = maze
            self.current_intersections = generator.intersections

            # Update maze display
            self.maze_widget.set_maze(maze, generator.intersections)

            # Update UI
            self.intersection_info.setText(f"Found {len(generator.intersections)} intersections. Click to place nodes.")
            self.auto_place_btn.setEnabled(True)
            self.clear_nodes_btn.setEnabled(True)
            self.export_story_btn.setEnabled(True)
            self.export_image_btn.setEnabled(True)

            logger.info(f"Generated {algorithm} maze: {width}x{height} with {len(generator.intersections)} intersections")

        except Exception as e:
            logger.error(f"Error generating maze: {e}")
            QMessageBox.critical(self, "Error", f"Failed to generate maze: {e}")

    def _on_intersection_clicked(self, x: int, y: int):
        """Handle intersection click"""
        if (x, y) in self.maze_widget.node_positions:
            # Remove existing node
            self.maze_widget.remove_node_at_intersection(x, y)
        else:
            # Add new node
            node_data = {
                'id': self.node_counter,
                'x': x,
                'y': y,
                'text': f"You are at intersection {self.node_counter}. Which direction will you go?",
                'choices': self._get_available_directions(x, y)
            }
            self.maze_widget.add_node_at_intersection(x, y, node_data)
            self.node_counter += 1

    def _get_available_directions(self, x: int, y: int) -> List[str]:
        """Get available movement directions from a position"""
        if not self.current_maze:
            return []

        directions = []
        direction_map = {
            (0, -1): "North",
            (1, 0): "East",
            (0, 1): "South",
            (-1, 0): "West"
        }

        for (dx, dy), direction in direction_map.items():
            nx, ny = x + dx, y + dy
            if (0 <= nx < len(self.current_maze[0]) and
                0 <= ny < len(self.current_maze) and
                self.current_maze[ny][nx] == 0):  # Is a path
                directions.append(direction)

        return directions

    def _auto_place_nodes(self):
        """Automatically place nodes at all intersections"""
        if not self.current_intersections:
            return

        for x, y in self.current_intersections:
            if (x, y) not in self.maze_widget.node_positions:
                node_data = {
                    'id': self.node_counter,
                    'x': x,
                    'y': y,
                    'text': f"You are at intersection {self.node_counter}. Which direction will you go?",
                    'choices': self._get_available_directions(x, y)
                }
                self.maze_widget.add_node_at_intersection(x, y, node_data)
                self.node_counter += 1

    def _clear_nodes(self):
        """Clear all placed nodes"""
        self.maze_widget.node_positions = {}
        self.maze_widget.update()
        self.node_counter = 1

    def _export_as_story(self):
        """Export maze as a story structure"""
        if not self.maze_widget.node_positions:
            QMessageBox.warning(self, "No Nodes", "Please place some story nodes first.")
            return

        try:
            # Create story data structure
            story_data = {
                'title': f"Maze Adventure {len(self.maze_widget.node_positions)} Nodes",
                'type': 'maze',
                'maze_data': {
                    'width': len(self.current_maze[0]),
                    'height': len(self.current_maze),
                    'algorithm': self.algorithm_combo.currentText(),
                    'maze': self.current_maze
                },
                'nodes': {}
            }

            # Convert maze nodes to story nodes
            for (x, y), node_data in self.maze_widget.node_positions.items():
                node_id = f"maze_node_{node_data['id']}"

                # Create choices based on available directions
                choices = []
                for direction in node_data['choices']:
                    # Find the next intersection in that direction
                    next_node = self._find_next_intersection(x, y, direction)
                    if next_node:
                        choices.append({
                            'text': f"Go {direction}",
                            'target': f"maze_node_{next_node}"
                        })

                story_data['nodes'][node_id] = {
                    'text': node_data['text'],
                    'choices': choices,
                    'position': {'x': x, 'y': y},
                    'is_entry_point': len(story_data['nodes']) == 0  # First node is entry
                }

            # Emit the story data
            self.maze_created.emit(story_data)

            QMessageBox.information(self, "Export Complete",
                                  f"Maze exported as story with {len(story_data['nodes'])} nodes.")

        except Exception as e:
            logger.error(f"Error exporting maze as story: {e}")
            QMessageBox.critical(self, "Export Error", f"Failed to export maze: {e}")

    def _find_next_intersection(self, start_x: int, start_y: int, direction: str) -> Optional[int]:
        """Find the next intersection in a given direction"""
        direction_map = {
            "North": (0, -1),
            "East": (1, 0),
            "South": (0, 1),
            "West": (-1, 0)
        }

        if direction not in direction_map:
            return None

        dx, dy = direction_map[direction]
        x, y = start_x + dx, start_y + dy

        # Follow the path until we hit another intersection or a dead end
        while (0 <= x < len(self.current_maze[0]) and
               0 <= y < len(self.current_maze) and
               self.current_maze[y][x] == 0):

            # Check if this is an intersection with a node
            if (x, y) in self.maze_widget.node_positions:
                return self.maze_widget.node_positions[(x, y)]['id']

            # Continue in the same direction if it's a straight path
            next_x, next_y = x + dx, y + dy
            if (0 <= next_x < len(self.current_maze[0]) and
                0 <= next_y < len(self.current_maze) and
                self.current_maze[next_y][next_x] == 0):
                x, y = next_x, next_y
            else:
                break

        return None

    def _export_as_image(self):
        """Export maze as an image"""
        try:
            from PyQt6.QtGui import QPixmap

            # Create a pixmap of the maze
            pixmap = QPixmap(self.maze_widget.size())
            self.maze_widget.render(pixmap)

            # Save to file
            output_dir = Path("data/mazes")
            output_dir.mkdir(parents=True, exist_ok=True)

            filename = f"maze_{len(self.maze_widget.node_positions)}_nodes.png"
            output_path = output_dir / filename

            if pixmap.save(str(output_path)):
                QMessageBox.information(self, "Export Complete",
                                      f"Maze image saved to: {output_path}")
            else:
                QMessageBox.warning(self, "Export Failed", "Failed to save maze image.")

        except Exception as e:
            logger.error(f"Error exporting maze image: {e}")
            QMessageBox.critical(self, "Export Error", f"Failed to export image: {e}")
