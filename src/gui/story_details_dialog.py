"""
Story Details Dialog
Dialog for viewing and editing story/storyline metadata and details
"""

import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QLabel, QPushButton, QTextEdit, QCheckBox,
    QDialogButtonBox, QMessageBox, QTabWidget, QWidget,
    QSpinBox, QComboBox, QFileDialog, QScrollArea
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap

logger = logging.getLogger(__name__)

class StoryDetailsDialog(QDialog):
    """Dialog for viewing and editing story details"""
    
    def __init__(self, story_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.story_data = story_data
        self.image_path = story_data.get('image_path', '')
        
        self.setWindowTitle("📖 Story Details")
        self.setModal(True)
        self.resize(700, 600)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        
        self._setup_ui()
        self._load_story_data()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("📖 Story Details & Metadata")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("padding: 15px; color: #2E7D32; background-color: #f8f9fa; border-radius: 8px; margin-bottom: 10px;")
        layout.addWidget(header)
        
        # Tab widget for different sections
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_basic_info_tab()
        self._create_metadata_tab()
        self._create_statistics_tab()
        self._create_settings_tab()
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self._save_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def _create_basic_info_tab(self):
        """Create basic information tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Story Image Group
        image_group = QGroupBox("🖼️ Story Image")
        image_layout = QVBoxLayout(image_group)
        
        # Image display
        self.image_label = QLabel()
        self.image_label.setMinimumSize(200, 150)
        self.image_label.setMaximumSize(400, 300)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        self.image_label.setText("No image selected\nClick 'Browse' to add an image")
        image_layout.addWidget(self.image_label)
        
        # Image controls
        image_controls = QHBoxLayout()
        
        self.browse_image_btn = QPushButton("📁 Browse Image")
        self.browse_image_btn.clicked.connect(self._browse_image)
        image_controls.addWidget(self.browse_image_btn)
        
        self.remove_image_btn = QPushButton("🗑️ Remove Image")
        self.remove_image_btn.clicked.connect(self._remove_image)
        self.remove_image_btn.setEnabled(False)
        image_controls.addWidget(self.remove_image_btn)
        
        image_controls.addStretch()
        image_layout.addLayout(image_controls)
        
        layout.addWidget(image_group)
        
        # Basic Info Group
        info_group = QGroupBox("📝 Basic Information")
        info_layout = QFormLayout(info_group)
        
        # Title
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Enter story title...")
        info_layout.addRow("Title:", self.title_edit)
        
        # Author
        self.author_edit = QLineEdit()
        self.author_edit.setPlaceholderText("Enter author name...")
        info_layout.addRow("Author:", self.author_edit)
        
        # Genre
        self.genre_combo = QComboBox()
        self.genre_combo.setEditable(True)
        self.genre_combo.addItems([
            "Fantasy", "Science Fiction", "Mystery", "Romance", "Adventure",
            "Horror", "Thriller", "Drama", "Comedy", "Historical Fiction",
            "Urban Fantasy", "Cyberpunk", "Steampunk", "Post-Apocalyptic"
        ])
        info_layout.addRow("Genre:", self.genre_combo)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("Enter story description...")
        info_layout.addRow("Description:", self.description_edit)
        
        layout.addWidget(info_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "📝 Basic Info")
    
    def _create_metadata_tab(self):
        """Create metadata tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Story Properties Group
        props_group = QGroupBox("⚙️ Story Properties")
        props_layout = QFormLayout(props_group)
        
        # Story Type
        self.story_type_combo = QComboBox()
        self.story_type_combo.addItems(["Story Web", "Linear Story", "Quiz", "Interactive Fiction"])
        props_layout.addRow("Story Type:", self.story_type_combo)
        
        # Difficulty Level
        self.difficulty_combo = QComboBox()
        self.difficulty_combo.addItems(["Beginner", "Intermediate", "Advanced", "Expert"])
        props_layout.addRow("Difficulty:", self.difficulty_combo)
        
        # Target Age
        self.age_combo = QComboBox()
        self.age_combo.addItems(["All Ages", "Teen (13+)", "Young Adult (16+)", "Adult (18+)", "Mature (21+)"])
        props_layout.addRow("Target Age:", self.age_combo)
        
        # Content Rating
        self.rating_combo = QComboBox()
        self.rating_combo.addItems(["G - General", "PG - Parental Guidance", "PG-13 - Parents Cautioned", "R - Restricted", "NC-17 - Adults Only"])
        props_layout.addRow("Content Rating:", self.rating_combo)
        
        layout.addWidget(props_group)
        
        # Tags Group
        tags_group = QGroupBox("🏷️ Tags & Keywords")
        tags_layout = QVBoxLayout(tags_group)
        
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("Enter tags separated by commas (e.g., magic, dragons, adventure)")
        tags_layout.addWidget(self.tags_edit)
        
        layout.addWidget(tags_group)
        
        # Creation Info Group
        creation_group = QGroupBox("📅 Creation Information")
        creation_layout = QFormLayout(creation_group)
        
        # Created Date (read-only)
        self.created_label = QLabel("Not set")
        creation_layout.addRow("Created:", self.created_label)
        
        # Last Modified (read-only)
        self.modified_label = QLabel("Not set")
        creation_layout.addRow("Last Modified:", self.modified_label)
        
        # Version
        self.version_edit = QLineEdit()
        self.version_edit.setPlaceholderText("1.0")
        creation_layout.addRow("Version:", self.version_edit)
        
        layout.addWidget(creation_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "📋 Metadata")
    
    def _create_statistics_tab(self):
        """Create statistics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Node Statistics Group
        nodes_group = QGroupBox("📊 Node Statistics")
        nodes_layout = QFormLayout(nodes_group)
        
        # Node counts (read-only)
        self.total_nodes_label = QLabel("0")
        nodes_layout.addRow("Total Nodes:", self.total_nodes_label)
        
        self.start_nodes_label = QLabel("0")
        nodes_layout.addRow("Start Nodes:", self.start_nodes_label)
        
        self.end_nodes_label = QLabel("0")
        nodes_layout.addRow("End Nodes:", self.end_nodes_label)
        
        self.choice_nodes_label = QLabel("0")
        nodes_layout.addRow("Choice Nodes:", self.choice_nodes_label)
        
        layout.addWidget(nodes_group)
        
        # Content Statistics Group
        content_group = QGroupBox("📝 Content Statistics")
        content_layout = QFormLayout(content_group)
        
        # Content stats (read-only)
        self.total_words_label = QLabel("0")
        content_layout.addRow("Total Words:", self.total_words_label)
        
        self.avg_words_label = QLabel("0")
        content_layout.addRow("Avg Words/Node:", self.avg_words_label)
        
        self.total_characters_label = QLabel("0")
        content_layout.addRow("Total Characters:", self.total_characters_label)
        
        layout.addWidget(content_group)
        
        # Complexity Group
        complexity_group = QGroupBox("🔀 Complexity Metrics")
        complexity_layout = QFormLayout(complexity_group)
        
        # Complexity metrics (read-only)
        self.branching_factor_label = QLabel("0")
        complexity_layout.addRow("Avg Branching Factor:", self.branching_factor_label)
        
        self.max_depth_label = QLabel("0")
        complexity_layout.addRow("Maximum Depth:", self.max_depth_label)
        
        self.cycles_label = QLabel("0")
        complexity_layout.addRow("Cycles Detected:", self.cycles_label)
        
        layout.addWidget(complexity_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "📊 Statistics")
    
    def _create_settings_tab(self):
        """Create settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Publishing Settings Group
        pub_group = QGroupBox("🌐 Publishing Settings")
        pub_layout = QFormLayout(pub_group)
        
        # Visibility
        self.visibility_combo = QComboBox()
        self.visibility_combo.addItems(["Private", "Public", "Unlisted", "Friends Only"])
        pub_layout.addRow("Visibility:", self.visibility_combo)
        
        # Allow Comments
        self.comments_check = QCheckBox("Allow reader comments")
        pub_layout.addRow("Comments:", self.comments_check)
        
        # Allow Sharing
        self.sharing_check = QCheckBox("Allow social media sharing")
        pub_layout.addRow("Sharing:", self.sharing_check)
        
        layout.addWidget(pub_group)
        
        # Generation Settings Group
        gen_group = QGroupBox("🤖 AI Generation Settings")
        gen_layout = QFormLayout(gen_group)
        
        # Auto-generate descriptions
        self.auto_desc_check = QCheckBox("Auto-generate node descriptions")
        gen_layout.addRow("Descriptions:", self.auto_desc_check)
        
        # Auto-generate images
        self.auto_images_check = QCheckBox("Auto-generate node images")
        gen_layout.addRow("Images:", self.auto_images_check)
        
        # Auto-generate audio
        self.auto_audio_check = QCheckBox("Auto-generate voice narration")
        gen_layout.addRow("Audio:", self.auto_audio_check)
        
        layout.addWidget(gen_group)
        
        # Export Settings Group
        export_group = QGroupBox("📤 Export Settings")
        export_layout = QFormLayout(export_group)
        
        # Include media in exports
        self.export_media_check = QCheckBox("Include media files in exports")
        export_layout.addRow("Media:", self.export_media_check)
        
        # Compress exports
        self.compress_check = QCheckBox("Compress exported files")
        export_layout.addRow("Compression:", self.compress_check)
        
        layout.addWidget(export_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "⚙️ Settings")
    
    def _browse_image(self):
        """Browse for story image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Story Image",
            "",
            "Image files (*.png *.jpg *.jpeg *.gif *.bmp);;All files (*)"
        )
        
        if file_path:
            self.image_path = file_path
            self._update_image_display()
            self.remove_image_btn.setEnabled(True)
    
    def _remove_image(self):
        """Remove story image"""
        self.image_path = ""
        self._update_image_display()
        self.remove_image_btn.setEnabled(False)
    
    def _update_image_display(self):
        """Update image display"""
        if self.image_path and Path(self.image_path).exists():
            pixmap = QPixmap(self.image_path)
            if not pixmap.isNull():
                # Scale image to fit label
                scaled_pixmap = pixmap.scaled(
                    self.image_label.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.image_label.setPixmap(scaled_pixmap)
            else:
                self.image_label.setText("Invalid image file")
        else:
            self.image_label.clear()
            self.image_label.setText("No image selected\nClick 'Browse' to add an image")
    
    def _load_story_data(self):
        """Load story data into the form"""
        try:
            # Basic info
            self.title_edit.setText(self.story_data.get('title', ''))
            self.author_edit.setText(self.story_data.get('author', ''))
            self.genre_combo.setCurrentText(self.story_data.get('genre', ''))
            self.description_edit.setPlainText(self.story_data.get('description', ''))
            
            # Metadata
            self.story_type_combo.setCurrentText(self.story_data.get('story_type', 'Story Web'))
            self.difficulty_combo.setCurrentText(self.story_data.get('difficulty', 'Intermediate'))
            self.age_combo.setCurrentText(self.story_data.get('target_age', 'All Ages'))
            self.rating_combo.setCurrentText(self.story_data.get('content_rating', 'G - General'))
            self.tags_edit.setText(self.story_data.get('tags', ''))
            self.version_edit.setText(self.story_data.get('version', '1.0'))
            
            # Creation info
            created = self.story_data.get('created_date', 'Not set')
            modified = self.story_data.get('modified_date', 'Not set')
            self.created_label.setText(created)
            self.modified_label.setText(modified)
            
            # Statistics (calculated from story data)
            self._calculate_statistics()
            
            # Settings
            self.visibility_combo.setCurrentText(self.story_data.get('visibility', 'Private'))
            self.comments_check.setChecked(self.story_data.get('allow_comments', True))
            self.sharing_check.setChecked(self.story_data.get('allow_sharing', True))
            self.auto_desc_check.setChecked(self.story_data.get('auto_descriptions', False))
            self.auto_images_check.setChecked(self.story_data.get('auto_images', False))
            self.auto_audio_check.setChecked(self.story_data.get('auto_audio', False))
            self.export_media_check.setChecked(self.story_data.get('export_media', True))
            self.compress_check.setChecked(self.story_data.get('compress_exports', True))
            
            # Image
            if self.image_path:
                self._update_image_display()
                self.remove_image_btn.setEnabled(True)
                
        except Exception as e:
            logger.error(f"Error loading story data: {e}")
    
    def _calculate_statistics(self):
        """Calculate and display story statistics"""
        try:
            nodes = self.story_data.get('nodes', {})
            
            # Node counts
            total_nodes = len(nodes)
            start_nodes = sum(1 for node in nodes.values() if node.get('is_start', False))
            end_nodes = sum(1 for node in nodes.values() if not node.get('choices', []))
            choice_nodes = total_nodes - end_nodes
            
            self.total_nodes_label.setText(str(total_nodes))
            self.start_nodes_label.setText(str(start_nodes))
            self.end_nodes_label.setText(str(end_nodes))
            self.choice_nodes_label.setText(str(choice_nodes))
            
            # Content statistics
            total_words = 0
            total_chars = 0
            
            for node in nodes.values():
                content = node.get('content', '')
                words = len(content.split()) if content else 0
                total_words += words
                total_chars += len(content)
            
            avg_words = total_words // total_nodes if total_nodes > 0 else 0
            
            self.total_words_label.setText(str(total_words))
            self.avg_words_label.setText(str(avg_words))
            self.total_characters_label.setText(str(total_chars))
            
            # Complexity metrics
            total_choices = sum(len(node.get('choices', [])) for node in nodes.values())
            avg_branching = total_choices / choice_nodes if choice_nodes > 0 else 0
            
            self.branching_factor_label.setText(f"{avg_branching:.1f}")
            self.max_depth_label.setText("N/A")  # Would need graph analysis
            self.cycles_label.setText("N/A")     # Would need graph analysis
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
    
    def _save_and_accept(self):
        """Save story data and accept dialog"""
        try:
            # Update story data with form values
            self.story_data.update({
                'title': self.title_edit.text().strip(),
                'author': self.author_edit.text().strip(),
                'genre': self.genre_combo.currentText(),
                'description': self.description_edit.toPlainText().strip(),
                'story_type': self.story_type_combo.currentText(),
                'difficulty': self.difficulty_combo.currentText(),
                'target_age': self.age_combo.currentText(),
                'content_rating': self.rating_combo.currentText(),
                'tags': self.tags_edit.text().strip(),
                'version': self.version_edit.text().strip(),
                'visibility': self.visibility_combo.currentText(),
                'allow_comments': self.comments_check.isChecked(),
                'allow_sharing': self.sharing_check.isChecked(),
                'auto_descriptions': self.auto_desc_check.isChecked(),
                'auto_images': self.auto_images_check.isChecked(),
                'auto_audio': self.auto_audio_check.isChecked(),
                'export_media': self.export_media_check.isChecked(),
                'compress_exports': self.compress_check.isChecked(),
                'image_path': self.image_path,
                'modified_date': str(datetime.now())
            })
            
            logger.info("Story details updated successfully")
            self.accept()
            
        except Exception as e:
            logger.error(f"Error saving story details: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save story details: {e}")
