"""
Authentication Worker Threads
Background threads for handling OAuth authentication without blocking the GUI
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtGui import QDesktopServices
from PyQt6.QtCore import QUrl

logger = logging.getLogger(__name__)

class XAuthWorker(QThread):
    """Background worker for X authentication"""
    
    auth_completed = pyqtSignal(dict)  # user_info
    auth_failed = pyqtSignal(str)      # error_message
    auth_progress = pyqtSignal(str)    # status_message
    
    def __init__(self, x_authenticator):
        super().__init__()
        self.x_authenticator = x_authenticator
        self.cancelled = False
        self.callback_server = None
    
    def cancel(self):
        """Cancel the authentication process"""
        self.cancelled = True
        if self.callback_server:
            self.callback_server.stop()
    
    def run(self):
        """Run the X authentication process"""
        try:
            if self.cancelled:
                return
            
            self.auth_progress.emit("🔧 Starting callback server...")
            
            # Start callback server
            from gui.auth_tab import OAuthCallbackServer
            self.callback_server = OAuthCallbackServer()
            callback_url = self.callback_server.start()
            
            if self.cancelled:
                self.callback_server.stop()
                return
            
            # Update authenticator with dynamic callback URL
            self.x_authenticator.redirect_uri = callback_url
            
            self.auth_progress.emit("🔗 Generating authentication URL...")
            
            # Get auth URL
            auth_url, state = self.x_authenticator.get_auth_url()
            
            if not auth_url:
                self.auth_failed.emit("Failed to generate authentication URL")
                return
            
            if self.cancelled:
                self.callback_server.stop()
                return
            
            self.auth_progress.emit("🌐 Opening browser for authentication...")
            
            # Open browser
            QDesktopServices.openUrl(QUrl(auth_url))
            
            self.auth_progress.emit("⏳ Waiting for authorization... Please complete authentication in your browser.")

            # Wait for callback with periodic progress updates
            import time
            timeout = 300  # 5 minutes
            start_time = time.time()
            check_interval = 2  # Check every 2 seconds

            while time.time() - start_time < timeout:
                if self.cancelled:
                    return

                # Check if we have a result
                if (self.callback_server.server and
                    hasattr(self.callback_server.server, 'auth_result') and
                    self.callback_server.server.auth_result):
                    result = self.callback_server.server.auth_result
                    self.callback_server.stop()
                    break

                # Update progress every 30 seconds
                elapsed = time.time() - start_time
                if int(elapsed) % 30 == 0 and int(elapsed) > 0:
                    remaining = int(timeout - elapsed)
                    self.auth_progress.emit(f"⏳ Still waiting for authorization... ({remaining}s remaining)")

                time.sleep(check_interval)
            else:
                # Timeout occurred
                result = {'error': 'Timeout waiting for authentication'}
                self.callback_server.stop()
            
            if self.cancelled:
                return
            
            if 'error' in result:
                error_msg = result['error']
                if error_msg == 'Timeout waiting for authentication':
                    self.auth_failed.emit("Authentication timed out. Please try again.")
                else:
                    self.auth_failed.emit(f"Authentication error: {error_msg}")
                return
            
            elif 'code' in result:
                self.auth_progress.emit("✅ Authorization received, completing authentication...")
                
                # Complete authentication
                success = self.x_authenticator.authenticate_with_code(result['code'], result.get('state', ''))
                
                if success:
                    user_info = self.x_authenticator.get_user_info()
                    self.auth_completed.emit(user_info or {})
                else:
                    self.auth_failed.emit("Failed to complete authentication with received code")
            else:
                self.auth_failed.emit("No valid response received from authentication")
                
        except Exception as e:
            logger.error(f"X authentication worker failed: {e}")
            self.auth_failed.emit(f"Authentication failed: {str(e)}")
        finally:
            if self.callback_server:
                self.callback_server.stop()


class DriveAuthWorker(QThread):
    """Background worker for Google Drive authentication"""
    
    auth_completed = pyqtSignal(dict)  # user_info
    auth_failed = pyqtSignal(str)      # error_message
    auth_progress = pyqtSignal(str)    # status_message
    
    def __init__(self, backup_manager):
        super().__init__()
        self.backup_manager = backup_manager
        self.cancelled = False
        self.callback_server = None
    
    def cancel(self):
        """Cancel the authentication process"""
        self.cancelled = True
        if self.callback_server:
            self.callback_server.stop()
    
    def run(self):
        """Run the Google Drive authentication process"""
        try:
            if self.cancelled:
                return
            
            self.auth_progress.emit("🔧 Starting Google Drive callback server...")
            
            # Start callback server
            from backup.google_drive_auth import GoogleDriveCallbackServer
            self.callback_server = GoogleDriveCallbackServer()
            callback_url = self.callback_server.start()
            
            if self.cancelled:
                self.callback_server.stop()
                return
            
            # Update authenticator with dynamic callback URL
            if hasattr(self.backup_manager, 'authenticator'):
                self.backup_manager.authenticator.redirect_uri = callback_url
                
                self.auth_progress.emit("🔗 Generating Google Drive authentication URL...")
                
                # Get auth URL
                auth_url, state = self.backup_manager.authenticator.get_auth_url()
                
                if not auth_url:
                    self.auth_failed.emit("Failed to generate Google Drive authentication URL")
                    return
                
                if self.cancelled:
                    self.callback_server.stop()
                    return
                
                self.auth_progress.emit("🌐 Opening browser for Google Drive authentication...")
                
                # Open browser
                QDesktopServices.openUrl(QUrl(auth_url))
                
                self.auth_progress.emit("⏳ Waiting for Google Drive authorization... Please complete authentication in your browser.")
                
                # Wait for callback
                result = self.callback_server.wait_for_callback(timeout=300)
                
                if self.cancelled:
                    return
                
                if 'error' in result:
                    error_msg = result['error']
                    if error_msg == 'Timeout waiting for authentication':
                        self.auth_failed.emit("Google Drive authentication timed out. Please try again.")
                    else:
                        self.auth_failed.emit(f"Google Drive authentication error: {error_msg}")
                    return
                
                elif 'code' in result:
                    self.auth_progress.emit("✅ Google Drive authorization received, completing authentication...")
                    
                    # Complete authentication
                    success = self.backup_manager.authenticator.authenticate_with_code(result['code'], result.get('state', ''))
                    
                    if success:
                        user_info = self.backup_manager.authenticator.get_user_info()
                        self.auth_completed.emit(user_info or {})
                    else:
                        self.auth_failed.emit("Failed to complete Google Drive authentication with received code")
                else:
                    self.auth_failed.emit("No valid response received from Google Drive authentication")
            else:
                # Fallback for simple authentication
                self.auth_progress.emit("🌐 Opening browser for Google Drive authentication...")
                
                # Mock success for now
                self.auth_progress.emit("✅ Google Drive authentication completed (mock)")
                self.auth_completed.emit({'name': 'Mock User', 'email': '<EMAIL>'})
                
        except Exception as e:
            logger.error(f"Google Drive authentication worker failed: {e}")
            self.auth_failed.emit(f"Google Drive authentication failed: {str(e)}")
        finally:
            if self.callback_server:
                self.callback_server.stop()


class AuthProgressDialog:
    """Helper class for showing authentication progress"""
    
    def __init__(self, parent, title: str, service: str):
        from PyQt6.QtWidgets import QProgressDialog
        from PyQt6.QtCore import Qt
        
        self.dialog = QProgressDialog(parent)
        self.dialog.setWindowTitle(title)
        self.dialog.setLabelText(f"🚀 Starting {service} authentication...")
        self.dialog.setRange(0, 0)  # Indeterminate progress
        self.dialog.setModal(True)
        self.dialog.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowTitleHint)
        self.dialog.setCancelButtonText("Cancel")
        
    def update_progress(self, message: str):
        """Update progress message"""
        self.dialog.setLabelText(message)
    
    def show(self):
        """Show the dialog"""
        self.dialog.show()
    
    def close(self):
        """Close the dialog"""
        self.dialog.close()
    
    def was_cancelled(self) -> bool:
        """Check if dialog was cancelled"""
        return self.dialog.wasCanceled()
    
    def connect_cancel(self, callback):
        """Connect cancel button to callback"""
        self.dialog.canceled.connect(callback)
