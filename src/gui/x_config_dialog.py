"""
X (Twitter) Configuration Dialog
Floating window for configuring X API credentials and settings
"""

import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QLabel, QPushButton, QTextEdit, QCheckBox,
    QDialogButtonBox, QMessageBox, QTabWidget, QWidget,
    QSpinBox, QComboBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap, QIcon

logger = logging.getLogger(__name__)

class XConfigDialog(QDialog):
    """Configuration dialog for X (Twitter) API settings"""
    
    def __init__(self, config: Dict[str, Any], x_authenticator, parent=None):
        super().__init__(parent)
        self.config = config
        self.x_authenticator = x_authenticator
        
        self.setWindowTitle("🐦 Configure X (Twitter)")
        self.setModal(True)
        self.resize(600, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        
        self._setup_ui()
        self._load_current_settings()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("🐦 X (Twitter) Configuration")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("padding: 15px; color: #1DA1F2; background-color: #f8f9fa; border-radius: 8px; margin-bottom: 10px;")
        layout.addWidget(header)
        
        # Tab widget for different configuration sections
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # API Credentials Tab
        self._create_credentials_tab()
        
        # App Settings Tab
        self._create_app_settings_tab()
        
        # Rate Limits Tab
        self._create_rate_limits_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Test connection button
        self.test_btn = QPushButton("🔗 Test Connection")
        self.test_btn.clicked.connect(self._test_connection)
        self.test_btn.setStyleSheet("background-color: #1DA1F2; color: white; padding: 8px 16px; border-radius: 4px;")
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        # Standard dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self._save_and_accept)
        button_box.rejected.connect(self.reject)
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
    
    def _create_credentials_tab(self):
        """Create API credentials configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # API Credentials Group
        creds_group = QGroupBox("🔑 API Credentials")
        creds_layout = QFormLayout(creds_group)
        
        # Client ID
        self.client_id_edit = QLineEdit()
        self.client_id_edit.setPlaceholderText("Enter your X API Client ID")
        creds_layout.addRow("Client ID:", self.client_id_edit)
        
        # Client Secret
        self.client_secret_edit = QLineEdit()
        self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.client_secret_edit.setPlaceholderText("Enter your X API Client Secret")
        creds_layout.addRow("Client Secret:", self.client_secret_edit)
        
        # Show/Hide password
        self.show_secret_check = QCheckBox("Show Client Secret")
        self.show_secret_check.toggled.connect(self._toggle_secret_visibility)
        creds_layout.addRow("", self.show_secret_check)
        
        layout.addWidget(creds_group)
        
        # OAuth Settings Group
        oauth_group = QGroupBox("🔐 OAuth Settings")
        oauth_layout = QFormLayout(oauth_group)
        
        # Redirect URI
        self.redirect_uri_edit = QLineEdit()
        self.redirect_uri_edit.setText("http://localhost:8080/callback")
        self.redirect_uri_edit.setReadOnly(True)
        oauth_layout.addRow("Redirect URI:", self.redirect_uri_edit)
        
        # Scopes
        self.scopes_edit = QLineEdit()
        self.scopes_edit.setText("tweet.read tweet.write users.read offline.access")
        self.scopes_edit.setReadOnly(True)
        oauth_layout.addRow("Scopes:", self.scopes_edit)
        
        layout.addWidget(oauth_group)
        
        # Instructions
        instructions = QTextEdit()
        instructions.setMaximumHeight(120)
        instructions.setReadOnly(True)
        instructions.setHtml("""
        <h4>📋 Setup Instructions:</h4>
        <ol>
        <li>Go to <a href="https://developer.twitter.com">developer.twitter.com</a></li>
        <li>Create a new app or use an existing one</li>
        <li>Copy the <b>Client ID</b> and <b>Client Secret</b> from your app settings</li>
        <li>Set the callback URL to: <code>http://localhost:8080/callback</code></li>
        <li>Enable the required scopes: tweet.read, tweet.write, users.read, offline.access</li>
        </ol>
        """)
        layout.addWidget(instructions)
        
        self.tab_widget.addTab(tab, "🔑 Credentials")
    
    def _create_app_settings_tab(self):
        """Create app settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # App Info Group
        app_group = QGroupBox("📱 Application Settings")
        app_layout = QFormLayout(app_group)
        
        # App Name
        self.app_name_edit = QLineEdit()
        self.app_name_edit.setText("CYOA Automation System")
        app_layout.addRow("App Name:", self.app_name_edit)
        
        # User Agent
        self.user_agent_edit = QLineEdit()
        self.user_agent_edit.setText("CYOA-Bot/1.0")
        app_layout.addRow("User Agent:", self.user_agent_edit)
        
        layout.addWidget(app_group)
        
        # Posting Settings Group
        posting_group = QGroupBox("📝 Posting Settings")
        posting_layout = QFormLayout(posting_group)
        
        # Auto-post enabled
        self.auto_post_check = QCheckBox("Enable automatic posting")
        posting_layout.addRow("Auto-posting:", self.auto_post_check)
        
        # Default hashtags
        self.hashtags_edit = QLineEdit()
        self.hashtags_edit.setPlaceholderText("#CYOA #InteractiveStory #Gaming")
        posting_layout.addRow("Default Hashtags:", self.hashtags_edit)
        
        # Post template
        self.template_edit = QTextEdit()
        self.template_edit.setMaximumHeight(80)
        self.template_edit.setPlaceholderText("🎮 New story node available! What will you choose? {hashtags}")
        posting_layout.addRow("Post Template:", self.template_edit)
        
        layout.addWidget(posting_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "⚙️ Settings")
    
    def _create_rate_limits_tab(self):
        """Create rate limits configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Rate Limits Group
        limits_group = QGroupBox("⏱️ Rate Limits")
        limits_layout = QFormLayout(limits_group)
        
        # Posts per hour
        self.posts_per_hour_spin = QSpinBox()
        self.posts_per_hour_spin.setRange(1, 100)
        self.posts_per_hour_spin.setValue(10)
        limits_layout.addRow("Posts per Hour:", self.posts_per_hour_spin)
        
        # Posts per day
        self.posts_per_day_spin = QSpinBox()
        self.posts_per_day_spin.setRange(1, 1000)
        self.posts_per_day_spin.setValue(50)
        limits_layout.addRow("Posts per Day:", self.posts_per_day_spin)
        
        # API calls per minute
        self.api_calls_spin = QSpinBox()
        self.api_calls_spin.setRange(1, 300)
        self.api_calls_spin.setValue(75)
        limits_layout.addRow("API Calls per Minute:", self.api_calls_spin)
        
        layout.addWidget(limits_group)
        
        # Subscription Tier Group
        tier_group = QGroupBox("💎 Subscription Tier")
        tier_layout = QFormLayout(tier_group)
        
        # Tier selection
        self.tier_combo = QComboBox()
        self.tier_combo.addItems(["Free", "Basic", "Premium", "Enterprise"])
        self.tier_combo.currentTextChanged.connect(self._update_rate_limits)
        tier_layout.addRow("Current Tier:", self.tier_combo)
        
        # Tier info
        self.tier_info_label = QLabel("Select your X subscription tier to set appropriate rate limits")
        self.tier_info_label.setWordWrap(True)
        self.tier_info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        tier_layout.addRow("", self.tier_info_label)
        
        layout.addWidget(tier_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "⏱️ Limits")
    
    def _toggle_secret_visibility(self, checked: bool):
        """Toggle client secret visibility"""
        if checked:
            self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.client_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
    
    def _update_rate_limits(self, tier: str):
        """Update rate limits based on subscription tier"""
        tier_limits = {
            "Free": {"hour": 5, "day": 25, "api": 50},
            "Basic": {"hour": 10, "day": 50, "api": 75},
            "Premium": {"hour": 25, "day": 100, "api": 150},
            "Enterprise": {"hour": 100, "day": 500, "api": 300}
        }
        
        if tier in tier_limits:
            limits = tier_limits[tier]
            self.posts_per_hour_spin.setValue(limits["hour"])
            self.posts_per_day_spin.setValue(limits["day"])
            self.api_calls_spin.setValue(limits["api"])
            
            self.tier_info_label.setText(f"✅ {tier} tier limits applied automatically")
    
    def _load_current_settings(self):
        """Load current settings into the form"""
        try:
            x_config = self.config.get('x', {})
            
            # Load credentials
            self.client_id_edit.setText(x_config.get('client_id', ''))
            self.client_secret_edit.setText(x_config.get('client_secret', ''))
            
            # Load app settings
            self.app_name_edit.setText(x_config.get('app_name', 'CYOA Automation System'))
            self.user_agent_edit.setText(x_config.get('user_agent', 'CYOA-Bot/1.0'))
            self.auto_post_check.setChecked(x_config.get('auto_post', False))
            self.hashtags_edit.setText(x_config.get('default_hashtags', ''))
            self.template_edit.setPlainText(x_config.get('post_template', ''))
            
            # Load rate limits
            rate_limits = x_config.get('rate_limits', {})
            self.posts_per_hour_spin.setValue(rate_limits.get('posts_per_hour', 10))
            self.posts_per_day_spin.setValue(rate_limits.get('posts_per_day', 50))
            self.api_calls_spin.setValue(rate_limits.get('api_calls_per_minute', 75))
            
            # Load tier
            tier = x_config.get('subscription_tier', 'Free')
            index = self.tier_combo.findText(tier.title())
            if index >= 0:
                self.tier_combo.setCurrentIndex(index)
                
        except Exception as e:
            logger.error(f"Error loading X settings: {e}")
    
    def _test_connection(self):
        """Test the X API connection"""
        try:
            client_id = self.client_id_edit.text().strip()
            client_secret = self.client_secret_edit.text().strip()
            
            if not client_id or not client_secret:
                QMessageBox.warning(self, "Missing Credentials", "Please enter both Client ID and Client Secret")
                return
            
            # Mock test for now
            QMessageBox.information(
                self, 
                "Connection Test", 
                "✅ X API credentials appear to be valid!\n\nNote: Full validation occurs during authentication."
            )
            
        except Exception as e:
            logger.error(f"Error testing X connection: {e}")
            QMessageBox.critical(self, "Connection Test Failed", f"Failed to test connection: {e}")
    
    def _save_and_accept(self):
        """Save settings and accept dialog"""
        try:
            # Validate required fields
            if not self.client_id_edit.text().strip():
                QMessageBox.warning(self, "Missing Client ID", "Please enter your X API Client ID")
                self.tab_widget.setCurrentIndex(0)
                self.client_id_edit.setFocus()
                return
            
            if not self.client_secret_edit.text().strip():
                QMessageBox.warning(self, "Missing Client Secret", "Please enter your X API Client Secret")
                self.tab_widget.setCurrentIndex(0)
                self.client_secret_edit.setFocus()
                return
            
            # Save configuration
            x_config = {
                'client_id': self.client_id_edit.text().strip(),
                'client_secret': self.client_secret_edit.text().strip(),
                'redirect_uri': self.redirect_uri_edit.text().strip(),
                'app_name': self.app_name_edit.text().strip(),
                'user_agent': self.user_agent_edit.text().strip(),
                'auto_post': self.auto_post_check.isChecked(),
                'default_hashtags': self.hashtags_edit.text().strip(),
                'post_template': self.template_edit.toPlainText().strip(),
                'subscription_tier': self.tier_combo.currentText().lower(),
                'rate_limits': {
                    'posts_per_hour': self.posts_per_hour_spin.value(),
                    'posts_per_day': self.posts_per_day_spin.value(),
                    'api_calls_per_minute': self.api_calls_spin.value()
                }
            }
            
            # Update config
            self.config['x'] = x_config
            
            # Save to file
            config_file = Path("config.json")
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            logger.info("X configuration saved successfully")
            self.accept()
            
        except Exception as e:
            logger.error(f"Error saving X configuration: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save configuration: {e}")
