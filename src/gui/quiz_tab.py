"""
Quiz Creation Tab - Beautiful GUI for creating viral personality quizzes
Template-based and AI-powered quiz generation
"""

import logging
from typing import Dict, Any, Optional, List

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QListWidget,
    QFormLayout, QComboBox, QSpinBox, QTextEdit, QLineEdit,
    QFrame, QScrollArea, QGridLayout, QListWidgetItem, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QIcon

logger = logging.getLogger(__name__)


class QuizCreationTab(QWidget):
    """Quiz creation interface with templates and AI generation"""
    
    # Signals
    quiz_created = pyqtSignal(object)  # QuizWeb
    template_selected = pyqtSignal(str)  # template_id
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.current_quiz = None
        self.quiz_templates = []
        
        self._setup_ui()
        self._load_templates()
        
    def _setup_ui(self):
        """Setup the quiz creation interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create quiz tabs
        self._create_templates_tab()
        self._create_ai_generation_tab()
        self._create_manual_creation_tab()
        self._create_preview_tab()
        
    def _create_header(self) -> QWidget:
        """Create quiz creation header"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🧠 Quiz Creator")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Quick actions
        self.new_quiz_btn = QPushButton("✨ New Quiz")
        self.new_quiz_btn.clicked.connect(self._new_quiz)
        layout.addWidget(self.new_quiz_btn)
        
        self.save_quiz_btn = QPushButton("💾 Save Quiz")
        self.save_quiz_btn.clicked.connect(self._save_quiz)
        self.save_quiz_btn.setEnabled(False)
        layout.addWidget(self.save_quiz_btn)
        
        return header
    
    def _create_templates_tab(self):
        """Create quiz templates tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Template categories
        categories_layout = QHBoxLayout()
        
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "All Templates", "Personality", "Knowledge", "Compatibility", "Pop Culture"
        ])
        self.category_combo.currentTextChanged.connect(self._filter_templates)
        categories_layout.addWidget(QLabel("Category:"))
        categories_layout.addWidget(self.category_combo)
        
        categories_layout.addStretch()
        layout.addLayout(categories_layout)
        
        # Templates grid
        templates_scroll = QScrollArea()
        templates_widget = QWidget()
        self.templates_layout = QGridLayout(templates_widget)
        templates_scroll.setWidget(templates_widget)
        templates_scroll.setWidgetResizable(True)
        layout.addWidget(templates_scroll)
        
        # Template details
        details_group = QGroupBox("📋 Template Details")
        details_layout = QVBoxLayout(details_group)
        
        self.template_details = QTextEdit()
        self.template_details.setMaximumHeight(120)
        self.template_details.setReadOnly(True)
        details_layout.addWidget(self.template_details)
        
        # Template actions
        template_actions = QHBoxLayout()
        
        self.use_template_btn = QPushButton("🚀 Use This Template")
        self.use_template_btn.clicked.connect(self._use_selected_template)
        self.use_template_btn.setEnabled(False)
        template_actions.addWidget(self.use_template_btn)
        
        self.preview_template_btn = QPushButton("👁️ Preview")
        self.preview_template_btn.clicked.connect(self._preview_template)
        self.preview_template_btn.setEnabled(False)
        template_actions.addWidget(self.preview_template_btn)
        
        template_actions.addStretch()
        details_layout.addLayout(template_actions)
        
        layout.addWidget(details_group)
        
        self.tab_widget.addTab(tab, "📚 Templates")
    
    def _create_ai_generation_tab(self):
        """Create AI quiz generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # AI generation form
        form_group = QGroupBox("🤖 AI Quiz Generation")
        form_layout = QFormLayout(form_group)
        
        # Quiz prompt
        self.quiz_prompt = QTextEdit()
        self.quiz_prompt.setMaximumHeight(100)
        self.quiz_prompt.setPlaceholderText(
            "Describe your quiz idea...\n"
            "Examples:\n"
            "• 'Which programming language matches your personality?'\n"
            "• 'What type of traveler are you?'\n"
            "• 'Which Marvel superhero are you most like?'"
        )
        form_layout.addRow("Quiz Idea:", self.quiz_prompt)
        
        # Quiz type
        self.quiz_type_combo = QComboBox()
        self.quiz_type_combo.addItems(["Personality", "Knowledge", "Compatibility", "Assessment"])
        form_layout.addRow("Quiz Type:", self.quiz_type_combo)
        
        # Number of questions
        self.num_questions_spin = QSpinBox()
        self.num_questions_spin.setRange(3, 15)
        self.num_questions_spin.setValue(6)
        form_layout.addRow("Questions:", self.num_questions_spin)
        
        # Number of outcomes
        self.num_outcomes_spin = QSpinBox()
        self.num_outcomes_spin.setRange(3, 10)
        self.num_outcomes_spin.setValue(4)
        form_layout.addRow("Outcomes:", self.num_outcomes_spin)
        
        layout.addWidget(form_group)
        
        # Generation controls
        generation_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("🎯 Generate Quiz")
        self.generate_btn.clicked.connect(self._generate_ai_quiz)
        generation_layout.addWidget(self.generate_btn)
        
        self.generate_ideas_btn = QPushButton("💡 Get Ideas")
        self.generate_ideas_btn.clicked.connect(self._generate_quiz_ideas)
        generation_layout.addWidget(self.generate_ideas_btn)
        
        generation_layout.addStretch()
        layout.addLayout(generation_layout)
        
        # Generation progress
        self.generation_progress = QProgressBar()
        self.generation_progress.setVisible(False)
        layout.addWidget(self.generation_progress)
        
        # Generated ideas
        ideas_group = QGroupBox("💡 Quiz Ideas")
        ideas_layout = QVBoxLayout(ideas_group)
        
        self.ideas_list = QListWidget()
        self.ideas_list.itemDoubleClicked.connect(self._use_idea)
        ideas_layout.addWidget(self.ideas_list)
        
        layout.addWidget(ideas_group)
        
        self.tab_widget.addTab(tab, "🤖 AI Generation")
    
    def _create_manual_creation_tab(self):
        """Create manual quiz creation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Quiz info
        info_group = QGroupBox("📝 Quiz Information")
        info_layout = QFormLayout(info_group)
        
        self.quiz_title = QLineEdit()
        self.quiz_title.setPlaceholderText("e.g., Which Harry Potter House Are You?")
        info_layout.addRow("Title:", self.quiz_title)
        
        self.quiz_description = QTextEdit()
        self.quiz_description.setMaximumHeight(60)
        self.quiz_description.setPlaceholderText("Brief description of your quiz...")
        info_layout.addRow("Description:", self.quiz_description)
        
        layout.addWidget(info_group)
        
        # Questions and outcomes
        content_layout = QHBoxLayout()
        
        # Questions section
        questions_group = QGroupBox("❓ Questions")
        questions_layout = QVBoxLayout(questions_group)
        
        self.questions_list = QListWidget()
        questions_layout.addWidget(self.questions_list)
        
        questions_buttons = QHBoxLayout()
        self.add_question_btn = QPushButton("➕ Add Question")
        self.add_question_btn.clicked.connect(self._add_question)
        questions_buttons.addWidget(self.add_question_btn)
        
        self.edit_question_btn = QPushButton("✏️ Edit")
        self.edit_question_btn.clicked.connect(self._edit_question)
        questions_buttons.addWidget(self.edit_question_btn)
        
        self.remove_question_btn = QPushButton("🗑️ Remove")
        self.remove_question_btn.clicked.connect(self._remove_question)
        questions_buttons.addWidget(self.remove_question_btn)
        
        questions_layout.addLayout(questions_buttons)
        content_layout.addWidget(questions_group)
        
        # Outcomes section
        outcomes_group = QGroupBox("🎯 Outcomes")
        outcomes_layout = QVBoxLayout(outcomes_group)
        
        self.outcomes_list = QListWidget()
        outcomes_layout.addWidget(self.outcomes_list)
        
        outcomes_buttons = QHBoxLayout()
        self.add_outcome_btn = QPushButton("➕ Add Outcome")
        self.add_outcome_btn.clicked.connect(self._add_outcome)
        outcomes_buttons.addWidget(self.add_outcome_btn)
        
        self.edit_outcome_btn = QPushButton("✏️ Edit")
        self.edit_outcome_btn.clicked.connect(self._edit_outcome)
        outcomes_buttons.addWidget(self.edit_outcome_btn)
        
        self.remove_outcome_btn = QPushButton("🗑️ Remove")
        self.remove_outcome_btn.clicked.connect(self._remove_outcome)
        outcomes_buttons.addWidget(self.remove_outcome_btn)
        
        outcomes_layout.addLayout(outcomes_buttons)
        content_layout.addWidget(outcomes_group)
        
        layout.addLayout(content_layout)
        
        self.tab_widget.addTab(tab, "✏️ Manual Creation")
    
    def _create_preview_tab(self):
        """Create quiz preview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Preview controls
        controls_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ Generate Preview")
        self.preview_btn.clicked.connect(self._generate_preview)
        controls_layout.addWidget(self.preview_btn)
        
        self.test_quiz_btn = QPushButton("🧪 Test Quiz")
        self.test_quiz_btn.clicked.connect(self._test_quiz)
        controls_layout.addWidget(self.test_quiz_btn)
        
        controls_layout.addStretch()
        
        self.publish_btn = QPushButton("🚀 Publish Quiz")
        self.publish_btn.clicked.connect(self._publish_quiz)
        self.publish_btn.setEnabled(False)
        controls_layout.addWidget(self.publish_btn)
        
        layout.addLayout(controls_layout)
        
        # Preview area
        preview_group = QGroupBox("👁️ Quiz Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        preview_layout.addWidget(self.preview_area)
        
        layout.addWidget(preview_group)
        
        # Quiz stats
        stats_group = QGroupBox("📊 Quiz Statistics")
        stats_layout = QFormLayout(stats_group)
        
        self.total_questions_label = QLabel("0")
        stats_layout.addRow("Total Questions:", self.total_questions_label)
        
        self.total_outcomes_label = QLabel("0")
        stats_layout.addRow("Total Outcomes:", self.total_outcomes_label)
        
        self.estimated_posts_label = QLabel("0")
        stats_layout.addRow("Estimated Posts:", self.estimated_posts_label)
        
        self.viral_potential_label = QLabel("Unknown")
        stats_layout.addRow("Viral Potential:", self.viral_potential_label)
        
        layout.addWidget(stats_group)
        
        self.tab_widget.addTab(tab, "👁️ Preview")
    
    def _load_templates(self):
        """Load quiz templates"""
        try:
            # TODO: Load actual templates from file
            sample_templates = [
                {
                    'id': 'harry_potter_house',
                    'title': 'Which Hogwarts House?',
                    'description': 'Discover your true Hogwarts house!',
                    'category': 'Pop Culture',
                    'viral_potential': 'Very High',
                    'questions': 5,
                    'outcomes': 4
                },
                {
                    'id': 'marvel_character',
                    'title': 'Which Marvel Hero?',
                    'description': 'Find your Marvel superhero match!',
                    'category': 'Pop Culture',
                    'viral_potential': 'High',
                    'questions': 6,
                    'outcomes': 6
                }
            ]

            self._display_templates(sample_templates)
            logger.info(f"Loaded {len(sample_templates)} quiz templates")

        except Exception as e:
            logger.error(f"Error loading quiz templates: {e}")
            # Show empty state
            self._display_templates([])
    
    def _display_templates(self, templates: List[Dict]):
        """Display templates in grid"""
        # Clear existing templates
        for i in reversed(range(self.templates_layout.count())):
            self.templates_layout.itemAt(i).widget().setParent(None)
        
        # Add template cards
        row, col = 0, 0
        for template in templates:
            card = self._create_template_card(template)
            self.templates_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 3 columns
                col = 0
                row += 1
    
    def _create_template_card(self, template: Dict) -> QWidget:
        """Create a template card widget"""
        card = QFrame()
        card.setFrameStyle(QFrame.Shape.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame:hover {
                border-color: #4CAF50;
            }
        """)
        card.setMinimumSize(200, 150)
        card.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(card)
        
        # Title
        title = QLabel(template['title'])
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Description
        desc = QLabel(template['description'])
        desc.setWordWrap(True)
        desc.setStyleSheet("color: #666;")
        layout.addWidget(desc)
        
        # Stats
        stats = QLabel(f"📊 {template['questions']} questions, {template['outcomes']} outcomes")
        stats.setStyleSheet("color: #888; font-size: 10px;")
        layout.addWidget(stats)
        
        # Viral potential
        viral = QLabel(f"🔥 {template['viral_potential']} viral potential")
        viral.setStyleSheet("color: #ff6b35; font-weight: bold; font-size: 10px;")
        layout.addWidget(viral)
        
        layout.addStretch()
        
        # Store template data
        card.template_data = template
        
        # Click handler
        def on_click():
            self._select_template(template)
        
        card.mousePressEvent = lambda e: on_click()
        
        return card
    
    def _select_template(self, template: Dict):
        """Select a template"""
        self.selected_template = template
        
        # Update details
        details_text = f"""
        <h3>{template['title']}</h3>
        <p><b>Category:</b> {template['category']}</p>
        <p><b>Description:</b> {template['description']}</p>
        <p><b>Questions:</b> {template['questions']}</p>
        <p><b>Outcomes:</b> {template['outcomes']}</p>
        <p><b>Viral Potential:</b> {template['viral_potential']}</p>
        """
        self.template_details.setHtml(details_text)
        
        # Enable buttons
        self.use_template_btn.setEnabled(True)
        self.preview_template_btn.setEnabled(True)
    
    def _filter_templates(self, category: str):
        """Filter templates by category"""
        # TODO: Implement template filtering
        pass
    
    def _use_selected_template(self):
        """Use the selected template"""
        if hasattr(self, 'selected_template'):
            self.template_selected.emit(self.selected_template['id'])
    
    def _preview_template(self):
        """Preview the selected template"""
        # TODO: Implement template preview
        pass
    
    def _new_quiz(self):
        """Start creating a new quiz"""
        self.current_quiz = None
        self.save_quiz_btn.setEnabled(False)
        # Clear all forms
    
    def _save_quiz(self):
        """Save the current quiz"""
        # TODO: Implement quiz saving
        pass
    
    def _generate_ai_quiz(self):
        """Generate quiz using AI"""
        prompt = self.quiz_prompt.toPlainText().strip()
        if not prompt:
            return
        
        self.generation_progress.setVisible(True)
        self.generate_btn.setEnabled(False)
        
        # TODO: Implement AI quiz generation
        
    def _generate_quiz_ideas(self):
        """Generate quiz ideas using AI"""
        # TODO: Implement idea generation
        sample_ideas = [
            "Which programming language matches your personality?",
            "What type of coffee are you?",
            "Which season represents your mood?",
            "What's your ideal vacation destination?",
            "Which social media platform are you?"
        ]
        
        self.ideas_list.clear()
        for idea in sample_ideas:
            self.ideas_list.addItem(idea)
    
    def _use_idea(self, item: QListWidgetItem):
        """Use a generated idea"""
        self.quiz_prompt.setPlainText(item.text())
    
    def _add_question(self):
        """Add a new question"""
        # TODO: Implement question addition
        pass
    
    def _edit_question(self):
        """Edit selected question"""
        # TODO: Implement question editing
        pass
    
    def _remove_question(self):
        """Remove selected question"""
        # TODO: Implement question removal
        pass
    
    def _add_outcome(self):
        """Add a new outcome"""
        # TODO: Implement outcome addition
        pass
    
    def _edit_outcome(self):
        """Edit selected outcome"""
        # TODO: Implement outcome editing
        pass
    
    def _remove_outcome(self):
        """Remove selected outcome"""
        # TODO: Implement outcome removal
        pass
    
    def _generate_preview(self):
        """Generate quiz preview"""
        # TODO: Implement preview generation
        pass
    
    def _test_quiz(self):
        """Test the quiz interactively"""
        # TODO: Implement quiz testing
        pass
    
    def _publish_quiz(self):
        """Publish the quiz"""
        # TODO: Implement quiz publishing
        pass
