"""
Chat Assistant - Floating/Sidebar AI Assistant
Provides contextual help and app control through chat interface
Can be displayed as sidebar or floating window like Co<PERSON><PERSON>/Augment
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QPushButton, QLabel, QFrame, QDialog, QToolButton, QMenu
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QPropertyAnimation, QRect, QEasingCurve, QEvent
from PyQt6.QtGui import QFont, QTextCursor, QAction

from ai.chat_agent import ChatAgent
from ai.rag_system import RAGSystem
from audio.speech_to_text import STTManager
from audio.text_to_speech import TTSManager
from story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class ChatWorker(QThread):
    """Worker thread for chat responses"""
    
    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, chat_agent: ChatA<PERSON>, message: str):
        super().__init__()
        self.chat_agent = chat_agent
        self.message = message
    
    def run(self):
        """Get chat response in background"""
        try:
            response = self.chat_agent.get_response(self.message)
            self.response_ready.emit(response)
        except Exception as e:
            self.error_occurred.emit(str(e))


class CompactChatWidget(QWidget):
    """Compact chat widget for sidebar/floating use"""

    # Signals for app control
    node_creation_requested = pyqtSignal(dict)
    tab_navigation_requested = pyqtSignal(str)
    setting_change_requested = pyqtSignal(str, object)

    def __init__(self, config: Dict[str, Any], lmstudio_client, parent=None):
        super().__init__(parent)

        self.config = config
        self.lmstudio_client = lmstudio_client
        self.current_story: Optional[StoryWeb] = None

        # Initialize AI components
        self.rag_system = RAGSystem(config)
        self.chat_agent = ChatAgent(config, lmstudio_client, self.rag_system)

        # Initialize audio components
        self.stt_manager = STTManager(config)
        self.tts_manager = TTSManager(config)

        # Chat state
        self.chat_worker: Optional[ChatWorker] = None
        self.auto_scroll = True
        self.tts_enabled = config.get('chat', {}).get('tts_enabled', False)
        self.is_collapsed = False

        self._setup_ui()
        self._setup_app_actions()

        # Welcome message
        self._add_assistant_message(
            "👋 Hi! I'm your AI assistant. Ask me anything about the app or your story!"
        )

        logger.info("Compact chat widget initialized")

    def _setup_ui(self):
        """Setup the compact UI"""
        self.setFixedWidth(400)  # Made wider
        self.setMinimumHeight(500)  # Made taller
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # Header with title and controls
        header = self._create_header()
        layout.addWidget(header)
        
        # Chat display (collapsible)
        self.chat_container = QWidget()
        chat_layout = QVBoxLayout(self.chat_container)
        chat_layout.setContentsMargins(0, 0, 0, 0)
        
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", 10))
        self.chat_display.setMaximumHeight(250)
        self.chat_display.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                color: #374151;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        chat_layout.addWidget(self.chat_display)
        
        layout.addWidget(self.chat_container)
        
        # Input area
        input_widget = self._create_input_area()
        layout.addWidget(input_widget)
        
        # Quick actions
        actions_widget = self._create_quick_actions()
        layout.addWidget(actions_widget)
        
        # Status
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #888; font-size: 9px;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
    
    def _create_header(self) -> QWidget:
        """Create header with title and controls"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background-color: #3d3d3d;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(8, 4, 8, 4)
        
        # Title
        title = QLabel("🤖 AI Assistant")
        title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        title.setStyleSheet("color: #ffffff;")
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Collapse button
        self.collapse_btn = QToolButton()
        self.collapse_btn.setText("−")
        self.collapse_btn.setFixedSize(20, 20)
        self.collapse_btn.clicked.connect(self._toggle_collapse)
        self.collapse_btn.setStyleSheet("""
            QToolButton {
                background-color: #555555;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QToolButton:hover {
                background-color: #666666;
            }
        """)
        layout.addWidget(self.collapse_btn)
        
        # Settings menu
        settings_btn = QToolButton()
        settings_btn.setText("⚙")
        settings_btn.setFixedSize(20, 20)
        settings_btn.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        
        settings_menu = QMenu()
        
        tts_action = QAction("🔊 Enable TTS", self)
        tts_action.setCheckable(True)
        tts_action.setChecked(self.tts_enabled)
        tts_action.toggled.connect(self._toggle_tts)
        settings_menu.addAction(tts_action)
        
        clear_action = QAction("🗑 Clear Chat", self)
        clear_action.triggered.connect(self._clear_chat)
        settings_menu.addAction(clear_action)
        
        settings_btn.setMenu(settings_menu)
        settings_btn.setStyleSheet("""
            QToolButton {
                background-color: #555555;
                color: white;
                border: none;
                border-radius: 10px;
            }
            QToolButton:hover {
                background-color: #666666;
            }
        """)
        layout.addWidget(settings_btn)
        
        return header
    
    def _create_input_area(self) -> QWidget:
        """Create modern chat input area"""
        widget = QFrame()
        widget.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                padding: 8px;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # Multi-line input field
        self.input_field = QTextEdit()
        self.input_field.setPlaceholderText("Type your message here...")
        self.input_field.setMaximumHeight(80)
        self.input_field.setMinimumHeight(40)
        self.input_field.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.input_field.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.input_field.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 8px;
                font-size: 11pt;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f9fafb;
            }
            QTextEdit:focus {
                border-color: #3b82f6;
                background-color: #ffffff;
            }
        """)

        # Handle Enter key for sending
        self.input_field.installEventFilter(self)
        layout.addWidget(self.input_field)

        # Button row
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # STT button
        if self.stt_manager.is_available:
            self.stt_button = self.stt_manager.create_stt_button()
            self.stt_button.setFixedSize(32, 32)
            self.stt_button.text_recognized.connect(self._on_speech_recognized)
            button_layout.addWidget(self.stt_button)

        button_layout.addStretch()

        # Send button
        self.send_button = QPushButton("Send")
        self.send_button.setFixedSize(60, 32)
        self.send_button.clicked.connect(self._send_message)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: 600;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)
        button_layout.addWidget(self.send_button)

        layout.addLayout(button_layout)

        return widget
    
    def _create_quick_actions(self) -> QWidget:
        """Create quick action buttons"""
        widget = QFrame()
        widget.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)

        # Header with hide button
        header_layout = QHBoxLayout()
        header_label = QLabel("Quick Actions")
        header_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 10pt;")
        header_layout.addWidget(header_label)

        header_layout.addStretch()

        self.hide_actions_btn = QPushButton("−")
        self.hide_actions_btn.setFixedSize(20, 20)
        self.hide_actions_btn.clicked.connect(self._toggle_quick_actions)
        self.hide_actions_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        header_layout.addWidget(self.hide_actions_btn)

        layout.addLayout(header_layout)

        # Actions container
        self.actions_container = QWidget()
        actions_layout = QVBoxLayout(self.actions_container)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(6)

        # Quick action buttons
        actions = [
            ("📝 Create Node", lambda: self._quick_action("create node")),
            ("📊 Show Analytics", lambda: self._quick_action("go to analytics")),
            ("👥 Manage Characters", lambda: self._quick_action("go to characters")),
            ("❓ Help", lambda: self._quick_action("how do I use this app?"))
        ]

        for text, action in actions:
            btn = QPushButton(text)
            btn.clicked.connect(action)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffffff;
                    color: #374151;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                    padding: 6px 12px;
                    text-align: left;
                    font-size: 10pt;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #f3f4f6;
                    border-color: #9ca3af;
                }
                QPushButton:pressed {
                    background-color: #e5e7eb;
                }
            """)
            actions_layout.addWidget(btn)

        layout.addWidget(self.actions_container)

        return widget

    def _toggle_quick_actions(self):
        """Toggle quick actions visibility"""
        if self.actions_container.isVisible():
            self.actions_container.hide()
            self.hide_actions_btn.setText("+")
        else:
            self.actions_container.show()
            self.hide_actions_btn.setText("−")
    
    def _setup_app_actions(self):
        """Setup app action callbacks for the chat agent"""
        actions = {
            "create_node": self._create_node_action,
            "edit_node": self._edit_node_action,
            "get_story_stats": self._get_story_stats_action,
            "navigate_to_tab": self._navigate_to_tab_action,
            "change_setting": self._change_setting_action,
            "export_story": self._export_story_action,
            "import_story": self._import_story_action
        }
        
        self.chat_agent.register_app_actions(actions)
    
    def _toggle_collapse(self):
        """Toggle chat display collapse"""
        self.is_collapsed = not self.is_collapsed
        
        if self.is_collapsed:
            self.chat_container.hide()
            self.collapse_btn.setText("+")
            self.setFixedHeight(120)
        else:
            self.chat_container.show()
            self.collapse_btn.setText("−")
            self.setMinimumHeight(400)
            self.setMaximumHeight(600)
    
    def _toggle_tts(self, enabled: bool):
        """Toggle text-to-speech"""
        self.tts_enabled = enabled
    
    def _clear_chat(self):
        """Clear chat history"""
        self.chat_display.clear()
        self.chat_agent.clear_history()
        self._add_assistant_message("Chat cleared. How can I help you?")
    
    def eventFilter(self, obj, event):
        """Handle key events for input field"""
        if obj == self.input_field and event.type() == QEvent.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
                if event.modifiers() == Qt.KeyboardModifier.ShiftModifier:
                    # Shift+Enter: new line
                    return False
                else:
                    # Enter: send message
                    self._send_message()
                    return True
        return super().eventFilter(obj, event)

    def _quick_action(self, message: str):
        """Execute a quick action"""
        self.input_field.setPlainText(message)
        self._send_message()

    def _send_message(self):
        """Send user message to chat agent"""
        message = self.input_field.toPlainText().strip()
        if not message:
            return

        # Clear input
        self.input_field.clear()

        # Add user message to display
        self._add_user_message(message)

        # Update status
        self.status_label.setText("Thinking...")
        self.send_button.setEnabled(False)

        # Get response in background
        self.chat_worker = ChatWorker(self.chat_agent, message)
        self.chat_worker.response_ready.connect(self._on_response_ready)
        self.chat_worker.error_occurred.connect(self._on_response_error)
        self.chat_worker.start()

    def _on_speech_recognized(self, text: str):
        """Handle speech recognition result"""
        self.input_field.setPlainText(text)
        self.input_field.setFocus()
    
    def _on_response_ready(self, response: str):
        """Handle chat response"""
        self._add_assistant_message(response)
        
        # Speak response if TTS enabled
        if self.tts_enabled:
            self.tts_manager.speak(response, "assistant")
        
        self.status_label.setText("Ready")
        self.send_button.setEnabled(True)
        self.input_field.setFocus()
    
    def _on_response_error(self, error: str):
        """Handle chat response error"""
        self._add_system_message(f"Error: {error}")
        self.status_label.setText("Error occurred")
        self.send_button.setEnabled(True)
        self.input_field.setFocus()
    
    def _add_user_message(self, message: str):
        """Add user message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"""
            <div style='margin: 8px 0; padding: 8px 12px; background-color: #3b82f6; color: white;
                        border-radius: 12px; margin-left: 20%; text-align: right;'>
                <div style='font-size: 10pt; font-weight: 500;'>{message}</div>
                <div style='font-size: 8pt; opacity: 0.8; margin-top: 4px;'>{timestamp}</div>
            </div>
        """)
        self._scroll_to_bottom()

    def _add_assistant_message(self, message: str):
        """Add assistant message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"""
            <div style='margin: 8px 0; padding: 8px 12px; background-color: #f3f4f6; color: #374151;
                        border-radius: 12px; margin-right: 20%;'>
                <div style='font-size: 10pt; font-weight: 500;'>🤖 Assistant</div>
                <div style='font-size: 10pt; margin-top: 4px;'>{message}</div>
                <div style='font-size: 8pt; opacity: 0.6; margin-top: 4px;'>{timestamp}</div>
            </div>
        """)
        self._scroll_to_bottom()

    def _add_system_message(self, message: str):
        """Add system message to chat display"""
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_display.append(f"""
            <div style='margin: 8px 0; padding: 6px 12px; background-color: #fef3c7; color: #92400e;
                        border-radius: 8px; text-align: center; font-size: 9pt;'>
                <b>System:</b> {message} <span style='opacity: 0.6;'>({timestamp})</span>
            </div>
        """)
        self._scroll_to_bottom()
    
    def _scroll_to_bottom(self):
        """Scroll chat display to bottom"""
        if self.auto_scroll:
            cursor = self.chat_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.chat_display.setTextCursor(cursor)
    
    # App action implementations (same as before)
    def _create_node_action(self, params: Dict[str, Any]) -> str:
        """Create a new story node"""
        self.node_creation_requested.emit(params)
        return "Node creation requested"
    
    def _edit_node_action(self, params: Dict[str, Any]) -> str:
        """Edit an existing story node"""
        return "Node editing not yet implemented"
    
    def _get_story_stats_action(self, params: Dict[str, Any]) -> str:
        """Get story statistics"""
        if not self.current_story:
            return "No story currently loaded"
        
        stats = {
            "nodes": len(self.current_story.nodes),
            "entry_points": len([n for n in self.current_story.nodes.values() if n.is_entry_point]),
            "endings": len([n for n in self.current_story.nodes.values() if n.is_ending])
        }
        
        return f"Story stats: {stats['nodes']} nodes, {stats['entry_points']} entry points, {stats['endings']} endings"
    
    def _navigate_to_tab_action(self, params: Dict[str, Any]) -> str:
        """Navigate to a specific tab"""
        tab_name = params.get("tab_name")
        if tab_name:
            self.tab_navigation_requested.emit(tab_name)
            return f"Navigating to {tab_name}"
        return "No tab specified"
    
    def _change_setting_action(self, params: Dict[str, Any]) -> str:
        """Change an app setting"""
        return "Setting changes not yet implemented"
    
    def _export_story_action(self, params: Dict[str, Any]) -> str:
        """Export the current story"""
        return "Story export not yet implemented"
    
    def _import_story_action(self, params: Dict[str, Any]) -> str:
        """Import a story"""
        return "Story import not yet implemented"
    
    def set_story(self, story: StoryWeb):
        """Set the current story for context"""
        self.current_story = story

        # Update RAG system with new story
        if story:
            self.rag_system.index_story(story)
            self._add_system_message(f"Story loaded: {len(story.nodes)} nodes indexed")
        else:
            self._add_system_message("Story unloaded")


class ChatAssistantSidebar(QWidget):
    """Sidebar implementation of chat assistant"""

    # Signals for app control
    node_creation_requested = pyqtSignal(dict)
    tab_navigation_requested = pyqtSignal(str)
    setting_change_requested = pyqtSignal(str, object)

    def __init__(self, config: Dict[str, Any], lmstudio_client, parent=None):
        super().__init__(parent)

        self.parent_window = parent
        self.is_visible = False
        self.animation = None

        # Create chat widget
        self.chat_widget = CompactChatWidget(config, lmstudio_client, self)

        # Connect signals
        self.chat_widget.node_creation_requested.connect(self.node_creation_requested.emit)
        self.chat_widget.tab_navigation_requested.connect(self.tab_navigation_requested.emit)
        self.chat_widget.setting_change_requested.connect(self.setting_change_requested.emit)

        self._setup_ui()

        # Initially hidden
        self.hide()

        logger.info("Chat assistant sidebar initialized")

    def _setup_ui(self):
        """Setup sidebar UI"""
        self.setFixedWidth(360)
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-left: 2px solid #dee2e6;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.chat_widget)

    def toggle_visibility(self):
        """Toggle sidebar visibility with animation"""
        if self.is_visible:
            self.hide_sidebar()
        else:
            self.show_sidebar()

    def show_sidebar(self):
        """Show sidebar with slide animation"""
        if self.is_visible:
            return

        self.show()
        self.is_visible = True

        # Position at right edge of parent
        if self.parent_window:
            parent_rect = self.parent_window.geometry()
            self.setGeometry(
                parent_rect.width() - self.width(),
                0,
                self.width(),
                parent_rect.height()
            )

        # Animate slide in
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(250)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        start_rect = self.geometry()
        end_rect = QRect(
            start_rect.x(),
            start_rect.y(),
            start_rect.width(),
            start_rect.height()
        )

        self.animation.setStartValue(QRect(
            start_rect.x() + start_rect.width(),
            start_rect.y(),
            start_rect.width(),
            start_rect.height()
        ))
        self.animation.setEndValue(end_rect)
        self.animation.start()

    def hide_sidebar(self):
        """Hide sidebar with slide animation"""
        if not self.is_visible:
            return

        self.is_visible = False

        # Animate slide out
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(250)
        self.animation.setEasingCurve(QEasingCurve.Type.InCubic)

        start_rect = self.geometry()
        end_rect = QRect(
            start_rect.x() + start_rect.width(),
            start_rect.y(),
            start_rect.width(),
            start_rect.height()
        )

        self.animation.setStartValue(start_rect)
        self.animation.setEndValue(end_rect)
        self.animation.finished.connect(self.hide)
        self.animation.start()

    def set_story(self, story: StoryWeb):
        """Set the current story for context"""
        self.chat_widget.set_story(story)


class ChatAssistantFloating(QDialog):
    """Floating window implementation of chat assistant"""

    # Signals for app control
    node_creation_requested = pyqtSignal(dict)
    tab_navigation_requested = pyqtSignal(str)
    setting_change_requested = pyqtSignal(str, object)

    def __init__(self, config: Dict[str, Any], lmstudio_client, parent=None):
        super().__init__(parent)

        # Create chat widget
        self.chat_widget = CompactChatWidget(config, lmstudio_client, self)

        # Connect signals
        self.chat_widget.node_creation_requested.connect(self.node_creation_requested.emit)
        self.chat_widget.tab_navigation_requested.connect(self.tab_navigation_requested.emit)
        self.chat_widget.setting_change_requested.connect(self.setting_change_requested.emit)

        self._setup_ui()

        logger.info("Chat assistant floating window initialized")

    def _setup_ui(self):
        """Setup floating window UI"""
        self.setWindowTitle("🤖 AI Assistant")
        self.setWindowFlags(
            Qt.WindowType.Tool |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.FramelessWindowHint
        )

        self.setFixedSize(360, 300)

        # Add custom title bar
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Title bar
        title_bar = self._create_title_bar()
        layout.addWidget(title_bar)

        # Chat widget
        layout.addWidget(self.chat_widget)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        # Make draggable
        self.drag_position = None

    def _create_title_bar(self) -> QWidget:
        """Create custom title bar"""
        title_bar = QFrame()
        title_bar.setFixedHeight(30)
        title_bar.setStyleSheet("""
            QFrame {
                background-color: #343a40;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
        """)

        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(8, 4, 8, 4)

        # Title
        title = QLabel("🤖 AI Assistant")
        title.setStyleSheet("color: white; font-weight: bold; font-size: 10pt;")
        layout.addWidget(title)

        layout.addStretch()

        # Minimize button
        minimize_btn = QPushButton("−")
        minimize_btn.setFixedSize(20, 20)
        minimize_btn.clicked.connect(self.showMinimized)
        minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        layout.addWidget(minimize_btn)

        # Close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.hide)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        layout.addWidget(close_btn)

        return title_bar

    def mousePressEvent(self, event):
        """Handle mouse press for dragging"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def set_story(self, story: StoryWeb):
        """Set the current story for context"""
        self.chat_widget.set_story(story)
