"""
Story Demo Dialog - Interactive text-only story playthrough
Allows users to play through the story from any node to test flow and choices
"""

import logging
from typing import Dict, List, Optional, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QScrollArea, QWidget, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from story.story_web import StoryWeb, StoryNode

logger = logging.getLogger(__name__)


class StoryDemoDialog(QDialog):
    """Dialog for playing through story interactively"""
    
    def __init__(self, story: StoryWeb, start_node_id: str, parent=None):
        super().__init__(parent)
        self.story = story
        self.current_node_id = start_node_id
        self.story_history = []  # Track visited nodes
        self.choice_history = []  # Track choices made
        
        self.setWindowTitle(f"Story Demo - Starting from '{start_node_id}'")
        self.setMinimumSize(800, 600)
        self.setup_ui()
        self.load_current_node()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📖 Interactive Story Demo")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Controls
        self.restart_btn = QPushButton("🔄 Restart from Beginning")
        self.restart_btn.clicked.connect(self.restart_story)
        header_layout.addWidget(self.restart_btn)
        
        self.back_btn = QPushButton("⬅️ Go Back")
        self.back_btn.clicked.connect(self.go_back)
        self.back_btn.setEnabled(False)
        header_layout.addWidget(self.back_btn)
        
        self.close_btn = QPushButton("❌ Close Demo")
        self.close_btn.clicked.connect(self.close)
        header_layout.addWidget(self.close_btn)
        
        layout.addLayout(header_layout)
        
        # Story content area
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        content_layout = QVBoxLayout(content_frame)
        
        # Node info
        self.node_info_label = QLabel()
        self.node_info_label.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
        content_layout.addWidget(self.node_info_label)
        
        # Story text
        self.story_text = QTextEdit()
        self.story_text.setReadOnly(True)
        self.story_text.setMinimumHeight(300)
        self.story_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                line-height: 1.6;
            }
        """)
        content_layout.addWidget(self.story_text)
        
        layout.addWidget(content_frame)
        
        # Choices area
        choices_frame = QFrame()
        choices_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        choices_layout = QVBoxLayout(choices_frame)
        
        choices_label = QLabel("🎯 Choose your path:")
        choices_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        choices_layout.addWidget(choices_label)
        
        # Scrollable choices area
        self.choices_scroll = QScrollArea()
        self.choices_widget = QWidget()
        self.choices_layout = QVBoxLayout(self.choices_widget)
        self.choices_scroll.setWidget(self.choices_widget)
        self.choices_scroll.setWidgetResizable(True)
        self.choices_scroll.setMaximumHeight(200)
        choices_layout.addWidget(self.choices_scroll)
        
        layout.addWidget(choices_frame)
        
        # Status bar
        self.status_label = QLabel()
        self.status_label.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        layout.addWidget(self.status_label)
    
    def load_current_node(self):
        """Load and display the current story node"""
        if self.current_node_id not in self.story.nodes:
            QMessageBox.critical(self, "Error", f"Node '{self.current_node_id}' not found in story!")
            self.close()
            return
        
        node = self.story.nodes[self.current_node_id]
        
        # Update node info
        node_type = "Entry Point" if node.is_entry else "Ending" if node.is_ending else "Story Node"
        self.node_info_label.setText(f"📍 Current Node: {self.current_node_id} ({node_type})")
        
        # Display story text
        story_html = f"""
        <div style="font-family: Georgia, serif; font-size: 16px; line-height: 1.8;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">{node.title or 'Untitled Scene'}</h3>
            <div style="text-align: justify;">
                {self._format_story_text(node.content)}
            </div>
        </div>
        """
        self.story_text.setHtml(story_html)
        
        # Clear previous choices
        self.clear_choices()
        
        # Add choices
        if node.choices:
            for i, choice in enumerate(node.choices):
                self.add_choice_button(i, choice)
        else:
            # No choices - this is an ending
            ending_label = QLabel("🏁 This is an ending. The story concludes here.")
            ending_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 10px; text-align: center;")
            ending_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.choices_layout.addWidget(ending_label)
        
        # Update status
        visited_count = len(self.story_history)
        total_nodes = len(self.story.nodes)
        self.status_label.setText(f"📊 Visited {visited_count} of {total_nodes} nodes | Path length: {len(self.choice_history)} choices")
        
        # Update back button
        self.back_btn.setEnabled(len(self.story_history) > 0)
    
    def _format_story_text(self, text: str) -> str:
        """Format story text for HTML display"""
        if not text:
            return "<p><em>No content available for this node.</em></p>"
        
        # Simple paragraph formatting
        paragraphs = text.split('\n\n')
        formatted_paragraphs = []
        
        for para in paragraphs:
            para = para.strip()
            if para:
                # Simple formatting
                para = para.replace('\n', '<br>')
                formatted_paragraphs.append(f"<p>{para}</p>")
        
        return ''.join(formatted_paragraphs) if formatted_paragraphs else f"<p>{text}</p>"
    
    def clear_choices(self):
        """Clear all choice buttons"""
        while self.choices_layout.count():
            child = self.choices_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def add_choice_button(self, index: int, choice):
        """Add a choice button"""
        button = QPushButton(f"{index + 1}. {choice.text}")
        button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                text-align: left;
                margin: 3px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        
        # Add choice metadata if available
        tooltip_parts = [f"Choice: {choice.text}"]
        if hasattr(choice, 'requirements') and choice.requirements:
            tooltip_parts.append(f"Requirements: {choice.requirements}")
        if hasattr(choice, 'effects') and choice.effects:
            tooltip_parts.append(f"Effects: {choice.effects}")
        
        button.setToolTip('\n'.join(tooltip_parts))
        
        # Connect to choice handler
        button.clicked.connect(lambda checked, c=choice: self.make_choice(c))
        
        self.choices_layout.addWidget(button)
    
    def make_choice(self, choice):
        """Handle choice selection"""
        try:
            # Record history
            self.story_history.append(self.current_node_id)
            self.choice_history.append({
                'from_node': self.current_node_id,
                'choice_text': choice.text,
                'to_node': choice.target_node_id
            })
            
            # Move to next node
            self.current_node_id = choice.target_node_id
            
            # Load new node
            self.load_current_node()
            
            logger.info(f"Demo: Made choice '{choice.text}' -> {choice.target_node_id}")
            
        except Exception as e:
            logger.error(f"Error making choice: {e}")
            QMessageBox.critical(self, "Error", f"Failed to process choice: {e}")
    
    def go_back(self):
        """Go back to previous node"""
        if not self.story_history:
            return
        
        # Restore previous state
        self.current_node_id = self.story_history.pop()
        if self.choice_history:
            self.choice_history.pop()
        
        # Reload node
        self.load_current_node()
        
        logger.info(f"Demo: Went back to {self.current_node_id}")
    
    def restart_story(self):
        """Restart story from the beginning"""
        # Find entry nodes
        entry_nodes = [node for node in self.story.nodes.values() if node.is_entry]
        
        if entry_nodes:
            # Use first entry node
            start_node_id = entry_nodes[0].id
        else:
            # Use original start node
            start_node_id = list(self.story.nodes.keys())[0] if self.story.nodes else None
        
        if start_node_id:
            self.current_node_id = start_node_id
            self.story_history.clear()
            self.choice_history.clear()
            self.load_current_node()
            
            logger.info(f"Demo: Restarted from {start_node_id}")
        else:
            QMessageBox.warning(self, "Warning", "No nodes available to restart from!")
    
    def closeEvent(self, event):
        """Handle dialog close"""
        logger.info(f"Story demo closed. Visited {len(self.story_history)} nodes, made {len(self.choice_history)} choices.")
        event.accept()
