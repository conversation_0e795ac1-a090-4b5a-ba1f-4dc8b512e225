"""
Logs Tab
Tab for viewing, filtering, and searching application logs
"""

import logging
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLineEdit, 
    QPushButton, QComboBox, QLabel, QCheckBox, QSplitter,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QSpinBox, QDateTimeEdit, QGroupBox, QFormLayout
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont, QColor, QTextCharFormat, QTextCursor

logger = logging.getLogger(__name__)

class LogReader(QThread):
    """Background thread for reading log files"""
    
    logs_updated = pyqtSignal(list)  # List of log entries
    
    def __init__(self, log_file_path: str, max_lines: int = 1000):
        super().__init__()
        self.log_file_path = Path(log_file_path)
        self.max_lines = max_lines
        self.running = False
        
    def run(self):
        """Read log file and emit updates"""
        self.running = True
        
        try:
            if not self.log_file_path.exists():
                return
                
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Get recent lines
            recent_lines = lines[-self.max_lines:] if len(lines) > self.max_lines else lines
            
            # Parse log entries
            log_entries = []
            for line in recent_lines:
                line = line.strip()
                if line:
                    entry = self._parse_log_line(line)
                    if entry:
                        log_entries.append(entry)
            
            self.logs_updated.emit(log_entries)
            
        except Exception as e:
            logger.error(f"Error reading log file: {e}")
    
    def _parse_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a log line into structured data"""
        try:
            # Try to parse structured log format
            # Format: [YYYY-MM-DD HH:MM:SS] LEVEL     Message
            if line.startswith('[') and ']' in line:
                parts = line.split(']', 2)
                if len(parts) >= 2:
                    timestamp_str = parts[0][1:]  # Remove [
                    rest = parts[1].strip()
                    
                    # Extract level
                    level_parts = rest.split(None, 1)
                    if level_parts:
                        level = level_parts[0]
                        message = level_parts[1] if len(level_parts) > 1 else ""
                        
                        return {
                            'timestamp': timestamp_str,
                            'level': level,
                            'message': message,
                            'raw': line
                        }
            
            # Fallback for unstructured logs
            return {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'INFO',
                'message': line,
                'raw': line
            }
            
        except Exception:
            return None
    
    def stop(self):
        """Stop the log reader"""
        self.running = False
        self.quit()
        self.wait()

class LogsTab(QWidget):
    """Tab for viewing and filtering application logs"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.log_entries: List[Dict[str, Any]] = []
        self.filtered_entries: List[Dict[str, Any]] = []
        self.log_reader: Optional[LogReader] = None
        
        self._setup_ui()
        self._setup_log_reader()
        
        logger.info("Logs tab initialized")
    
    def _setup_ui(self):
        """Setup the logs interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Filters
        filters = self._create_filters()
        layout.addWidget(filters)
        
        # Main content - splitter with table and details
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # Log table
        self.log_table = self._create_log_table()
        splitter.addWidget(self.log_table)
        
        # Log details
        self.log_details = self._create_log_details()
        splitter.addWidget(self.log_details)
        
        # Set splitter proportions
        splitter.setSizes([400, 200])
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_logs)
        self.refresh_timer.start(5000)  # Refresh every 5 seconds
    
    def _create_header(self) -> QWidget:
        """Create logs header"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("📋 Application Logs")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Controls
        self.auto_refresh_check = QCheckBox("Auto Refresh")
        self.auto_refresh_check.setChecked(True)
        self.auto_refresh_check.toggled.connect(self._toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_check)
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self._refresh_logs)
        layout.addWidget(self.refresh_btn)
        
        self.clear_btn = QPushButton("🗑️ Clear")
        self.clear_btn.clicked.connect(self._clear_logs)
        layout.addWidget(self.clear_btn)
        
        self.export_btn = QPushButton("📤 Export")
        self.export_btn.clicked.connect(self._export_logs)
        layout.addWidget(self.export_btn)
        
        return header
    
    def _create_filters(self) -> QWidget:
        """Create filter controls"""
        filters = QGroupBox("🔍 Filters")
        layout = QFormLayout(filters)
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search logs...")
        self.search_edit.textChanged.connect(self._apply_filters)
        layout.addRow("Search:", self.search_edit)
        
        # Level filter
        self.level_combo = QComboBox()
        self.level_combo.addItems(["All Levels", "ERROR", "WARNING", "INFO", "DEBUG", "TRACE"])
        self.level_combo.currentTextChanged.connect(self._apply_filters)
        layout.addRow("Level:", self.level_combo)
        
        # Time range
        time_layout = QHBoxLayout()
        
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems([
            "All Time", "Last Hour", "Last 6 Hours", "Last 24 Hours", 
            "Last 7 Days", "Custom Range"
        ])
        self.time_range_combo.currentTextChanged.connect(self._apply_filters)
        time_layout.addWidget(self.time_range_combo)
        
        self.from_datetime = QDateTimeEdit()
        self.from_datetime.setDateTime(QDateTime.currentDateTime().addDays(-1))
        self.from_datetime.setEnabled(False)
        self.from_datetime.dateTimeChanged.connect(self._apply_filters)
        time_layout.addWidget(QLabel("From:"))
        time_layout.addWidget(self.from_datetime)
        
        self.to_datetime = QDateTimeEdit()
        self.to_datetime.setDateTime(QDateTime.currentDateTime())
        self.to_datetime.setEnabled(False)
        self.to_datetime.dateTimeChanged.connect(self._apply_filters)
        time_layout.addWidget(QLabel("To:"))
        time_layout.addWidget(self.to_datetime)
        
        layout.addRow("Time Range:", time_layout)
        
        # Max entries
        self.max_entries_spin = QSpinBox()
        self.max_entries_spin.setRange(100, 10000)
        self.max_entries_spin.setValue(1000)
        self.max_entries_spin.setSuffix(" entries")
        self.max_entries_spin.valueChanged.connect(self._apply_filters)
        layout.addRow("Max Entries:", self.max_entries_spin)
        
        return filters
    
    def _create_log_table(self) -> QTableWidget:
        """Create log entries table"""
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["Timestamp", "Level", "Message", "Source"])
        
        # Configure table
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Timestamp
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Level
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)           # Message
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Source
        
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setAlternatingRowColors(True)
        table.setSortingEnabled(True)
        
        # Connect selection
        table.itemSelectionChanged.connect(self._on_log_selected)
        
        return table
    
    def _create_log_details(self) -> QWidget:
        """Create log details view"""
        details = QGroupBox("📄 Log Details")
        layout = QVBoxLayout(details)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.details_text)
        
        return details
    
    def _setup_log_reader(self):
        """Setup log file reader"""
        log_dir = Path("logs")
        log_file = log_dir / "cyoax.log"
        
        if log_file.exists():
            self.log_reader = LogReader(str(log_file), self.max_entries_spin.value())
            self.log_reader.logs_updated.connect(self._update_logs)
            self._refresh_logs()
    
    def _refresh_logs(self):
        """Refresh logs from file"""
        if self.log_reader and not self.log_reader.isRunning():
            self.log_reader.max_lines = self.max_entries_spin.value()
            self.log_reader.start()
    
    def _update_logs(self, log_entries: List[Dict[str, Any]]):
        """Update log entries"""
        self.log_entries = log_entries
        self._apply_filters()
    
    def _apply_filters(self):
        """Apply current filters to log entries"""
        filtered = self.log_entries.copy()
        
        # Search filter
        search_text = self.search_edit.text().lower()
        if search_text:
            filtered = [entry for entry in filtered 
                       if search_text in entry.get('message', '').lower()]
        
        # Level filter
        level_filter = self.level_combo.currentText()
        if level_filter != "All Levels":
            filtered = [entry for entry in filtered 
                       if entry.get('level', '') == level_filter]
        
        # Time range filter
        time_range = self.time_range_combo.currentText()
        if time_range != "All Time":
            now = datetime.now()
            if time_range == "Last Hour":
                cutoff = now - timedelta(hours=1)
            elif time_range == "Last 6 Hours":
                cutoff = now - timedelta(hours=6)
            elif time_range == "Last 24 Hours":
                cutoff = now - timedelta(days=1)
            elif time_range == "Last 7 Days":
                cutoff = now - timedelta(days=7)
            elif time_range == "Custom Range":
                # Enable custom datetime controls
                self.from_datetime.setEnabled(True)
                self.to_datetime.setEnabled(True)
                from_dt = self.from_datetime.dateTime().toPython()
                to_dt = self.to_datetime.dateTime().toPython()
                filtered = [entry for entry in filtered 
                           if self._parse_timestamp(entry.get('timestamp', '')) 
                           and from_dt <= self._parse_timestamp(entry.get('timestamp', '')) <= to_dt]
            else:
                # Disable custom datetime controls
                self.from_datetime.setEnabled(False)
                self.to_datetime.setEnabled(False)
                
                if time_range != "Custom Range":
                    filtered = [entry for entry in filtered 
                               if self._parse_timestamp(entry.get('timestamp', '')) 
                               and self._parse_timestamp(entry.get('timestamp', '')) >= cutoff]
        
        self.filtered_entries = filtered
        self._update_table()
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string to datetime"""
        try:
            # Try common formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%m/%d/%Y %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            return None
        except Exception:
            return None
    
    def _update_table(self):
        """Update the log table with filtered entries"""
        self.log_table.setRowCount(len(self.filtered_entries))
        
        for row, entry in enumerate(self.filtered_entries):
            # Timestamp
            timestamp_item = QTableWidgetItem(entry.get('timestamp', ''))
            self.log_table.setItem(row, 0, timestamp_item)
            
            # Level
            level = entry.get('level', '')
            level_item = QTableWidgetItem(level)
            
            # Color code by level
            if level == 'ERROR':
                level_item.setBackground(QColor(255, 200, 200))
            elif level == 'WARNING':
                level_item.setBackground(QColor(255, 255, 200))
            elif level == 'INFO':
                level_item.setBackground(QColor(200, 255, 200))
            elif level == 'DEBUG':
                level_item.setBackground(QColor(200, 200, 255))
            
            self.log_table.setItem(row, 1, level_item)
            
            # Message
            message = entry.get('message', '')
            message_item = QTableWidgetItem(message[:100] + '...' if len(message) > 100 else message)
            message_item.setToolTip(message)  # Full message in tooltip
            self.log_table.setItem(row, 2, message_item)
            
            # Source (extracted from message if available)
            source = self._extract_source(entry.get('message', ''))
            source_item = QTableWidgetItem(source)
            self.log_table.setItem(row, 3, source_item)
    
    def _extract_source(self, message: str) -> str:
        """Extract source/module from log message"""
        # Look for common patterns like "module.py" or "Class.method"
        import re
        
        # Pattern for file.py
        file_match = re.search(r'(\w+\.py)', message)
        if file_match:
            return file_match.group(1)
        
        # Pattern for module names
        module_match = re.search(r'(\w+\.\w+)', message)
        if module_match:
            return module_match.group(1)
        
        return "Unknown"
    
    def _on_log_selected(self):
        """Handle log entry selection"""
        current_row = self.log_table.currentRow()
        if 0 <= current_row < len(self.filtered_entries):
            entry = self.filtered_entries[current_row]
            
            # Show full details
            details = f"""Timestamp: {entry.get('timestamp', 'Unknown')}
Level: {entry.get('level', 'Unknown')}
Message: {entry.get('message', 'No message')}

Raw Log Line:
{entry.get('raw', 'No raw data')}"""
            
            self.details_text.setPlainText(details)
    
    def _toggle_auto_refresh(self, enabled: bool):
        """Toggle auto refresh"""
        if enabled:
            self.refresh_timer.start(5000)
        else:
            self.refresh_timer.stop()
    
    def _clear_logs(self):
        """Clear displayed logs"""
        self.log_entries.clear()
        self.filtered_entries.clear()
        self._update_table()
        self.details_text.clear()
    
    def _export_logs(self):
        """Export filtered logs to file"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Logs", f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text files (*.txt);;JSON files (*.json);;All files (*)"
            )
            
            if file_path:
                if file_path.endswith('.json'):
                    # Export as JSON
                    with open(file_path, 'w') as f:
                        json.dump(self.filtered_entries, f, indent=2, default=str)
                else:
                    # Export as text
                    with open(file_path, 'w') as f:
                        for entry in self.filtered_entries:
                            f.write(f"[{entry.get('timestamp', '')}] {entry.get('level', '')} - {entry.get('message', '')}\n")
                
                logger.info(f"Logs exported to {file_path}")
                
        except Exception as e:
            logger.error(f"Error exporting logs: {e}")
    
    def closeEvent(self, event):
        """Handle tab close"""
        if self.log_reader:
            self.log_reader.stop()
        event.accept()
