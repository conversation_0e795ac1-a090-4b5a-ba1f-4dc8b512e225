"""
Story Editor Tab - Interface for editing and managing CYOA stories
Provides tree view, node editing, and storyline import functionality
"""

import logging
from typing import Dict, Any, Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTextEdit, QLineEdit, QLabel, QPushButton, QGroupBox, QComboBox,
    QCheckBox, QSpinBox, QDialog, QDialogButtonBox, QFormLayout, QMessageBox,
    QFileDialog, QProgressBar, QListWidget, QListWidgetItem, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont

from story.story_web import StoryWeb, StoryNode, Choice, NodeType, EndingType

logger = logging.getLogger(__name__)


class StoryImportWorker(QThread):
    """Worker thread for importing storylines"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    import_completed = pyqtSignal(object)  # StoryWeb
    import_failed = pyqtSignal(str)  # error message
    
    def __init__(self, story_generator, source_text: str, title: str, player_class: str):
        super().__init__()
        self.story_generator = story_generator
        self.source_text = source_text
        self.title = title
        self.player_class = player_class
    
    def run(self):
        """Run the import process"""
        try:
            self.status_updated.emit("Analyzing source text...")
            self.progress_updated.emit(20)
            
            self.status_updated.emit("Generating storyline...")
            self.progress_updated.emit(40)
            
            story = self.story_generator.import_storyline_from_text(
                self.source_text, self.title, self.player_class
            )
            
            if story:
                self.progress_updated.emit(80)
                self.status_updated.emit("Validating story structure...")
                
                is_valid, errors = story.validate_structure()
                if not is_valid:
                    logger.warning(f"Imported story has validation issues: {errors}")
                
                self.progress_updated.emit(100)
                self.status_updated.emit("Import completed successfully")
                self.import_completed.emit(story)
            else:
                self.import_failed.emit("Failed to generate storyline from text")
                
        except Exception as e:
            logger.error(f"Error in import worker: {e}")
            self.import_failed.emit(str(e))


class ExtractionDialog(QDialog):
    """Dialog for extracting characters and nodes from text"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Extract Characters & Nodes from Text")
        self.setModal(True)
        self.resize(700, 600)

        self._setup_ui()

    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel("""
        <h3>📝 Extract Characters & Story Nodes from Text</h3>
        <p>This tool will analyze your text and extract:</p>
        <ul>
        <li><b>Characters</b> - Names, descriptions, roles, and personalities</li>
        <li><b>Story Nodes</b> - Key scenes, plot points, and narrative segments</li>
        <li><b>Relationships</b> - Character interactions and story connections</li>
        </ul>
        <p><b>🚀 Smart Processing:</b> Automatically handles long texts by splitting into chunks with overlap,
        then deduplicates characters and merges all extracted content.</p>
        <p><b>Tip:</b> Works with any length text - books, novels, scripts, or story outlines!</p>
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("background-color: #e7f3ff; padding: 10px; border-radius: 5px;")
        layout.addWidget(instructions)

        # Source text input
        text_group = QGroupBox("Source Text")
        text_layout = QVBoxLayout(text_group)

        self.source_text = QTextEdit()
        self.source_text.setPlaceholderText("""Paste your story text here...

Example:
"Once upon a time, there was a brave knight named Sir Galahad who lived in the kingdom of Camelot.
He was known for his pure heart and unwavering courage. One day, the evil sorcerer Morgana
threatened the kingdom with her dark magic..."

The AI will extract:
- Characters: Sir Galahad (knight, brave, pure-hearted), Morgana (sorcerer, evil)
- Nodes: Introduction, Threat appears, etc.
        """)
        self.source_text.setMinimumHeight(200)
        text_layout.addWidget(self.source_text)

        # File import button
        file_layout = QHBoxLayout()
        file_button = QPushButton("📁 Load from File...")
        file_button.clicked.connect(self.load_from_file)
        file_layout.addWidget(file_button)
        file_layout.addStretch()
        text_layout.addLayout(file_layout)

        layout.addWidget(text_group)

        # Extraction options
        options_group = QGroupBox("Extraction Options")
        options_layout = QVBoxLayout(options_group)

        self.extract_characters_check = QCheckBox("Extract Characters")
        self.extract_characters_check.setChecked(True)
        self.extract_characters_check.setToolTip("Extract character names, descriptions, and personalities")
        options_layout.addWidget(self.extract_characters_check)

        self.extract_nodes_check = QCheckBox("Extract Story Nodes")
        self.extract_nodes_check.setChecked(True)
        self.extract_nodes_check.setToolTip("Extract key scenes and plot points as story nodes")
        options_layout.addWidget(self.extract_nodes_check)

        self.extract_relationships_check = QCheckBox("Extract Relationships")
        self.extract_relationships_check.setChecked(True)
        self.extract_relationships_check.setToolTip("Extract character relationships and interactions")
        options_layout.addWidget(self.extract_relationships_check)

        layout.addWidget(options_group)

        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.extract_btn = QPushButton("🔍 Extract")
        self.extract_btn.clicked.connect(self.accept)
        self.extract_btn.setStyleSheet("background-color: #17a2b8; color: white; font-weight: bold; padding: 8px 16px;")
        button_layout.addWidget(self.extract_btn)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def load_from_file(self):
        """Load source text from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Source Text", "",
            "Text files (*.txt *.md);;Word documents (*.docx);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.source_text.setPlainText(content)
                self.status_label.setText(f"Loaded: {Path(file_path).name}")
            except Exception as e:
                QMessageBox.warning(self, "File Error", f"Could not read file: {e}")

    def get_extraction_data(self):
        """Get the extraction data from the dialog"""
        return {
            "source_text": self.source_text.toPlainText().strip(),
            "extract_characters": self.extract_characters_check.isChecked(),
            "extract_nodes": self.extract_nodes_check.isChecked(),
            "extract_relationships": self.extract_relationships_check.isChecked()
        }

    def set_progress(self, value: int):
        """Set progress bar value"""
        self.progress_bar.setValue(value)

    def set_status(self, message: str):
        """Set status message"""
        self.status_label.setText(message)

    def show_progress(self, show: bool):
        """Show or hide progress bar"""
        self.progress_bar.setVisible(show)
        self.extract_btn.setEnabled(not show)


class ImportDialog(QDialog):
    """Dialog for importing storylines from text"""
    
    def __init__(self, class_manager, parent=None):
        super().__init__(parent)
        self.class_manager = class_manager
        self.setWindowTitle("Import Storyline")
        self.setModal(True)
        self.resize(600, 500)
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Title input
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Enter storyline title...")
        form_layout.addRow("Title:", self.title_input)
        
        # Class selection
        self.class_combo = QComboBox()
        self.class_combo.addItems(self.class_manager.get_available_classes())
        form_layout.addRow("Player Class:", self.class_combo)
        
        layout.addLayout(form_layout)
        
        # Source text input
        source_group = QGroupBox("Source Text")
        source_layout = QVBoxLayout(source_group)
        
        self.source_text = QTextEdit()
        self.source_text.setPlaceholderText("Paste your story text here (e.g., Brothers Grimm fairy tale)...")
        source_layout.addWidget(self.source_text)
        
        # File import button
        file_button = QPushButton("Load from File...")
        file_button.clicked.connect(self.load_from_file)
        source_layout.addWidget(file_button)
        
        layout.addWidget(source_group)
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
        self.ok_button.setText("Import")
    
    def load_from_file(self):
        """Load source text from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Source Text", "", "Text files (*.txt);;All files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.source_text.setPlainText(content)
                
                # Auto-fill title from filename
                if not self.title_input.text():
                    title = Path(file_path).stem.replace('_', ' ').title()
                    self.title_input.setText(title)
                    
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load file: {e}")
    
    def get_import_data(self):
        """Get the import data from the dialog"""
        return {
            "title": self.title_input.text().strip(),
            "player_class": self.class_combo.currentText(),
            "source_text": self.source_text.toPlainText().strip()
        }
    
    def set_progress(self, value: int):
        """Set progress bar value"""
        self.progress_bar.setValue(value)
    
    def set_status(self, message: str):
        """Set status message"""
        self.status_label.setText(message)
    
    def show_progress(self, show: bool):
        """Show or hide progress bar"""
        self.progress_bar.setVisible(show)
        self.ok_button.setEnabled(not show)


class StoryEditorTab(QWidget):
    """Tab for editing CYOA stories"""
    
    story_changed = pyqtSignal(object)  # StoryWeb
    
    def __init__(self, config: Dict[str, Any], story_generator, inventory_manager,
                 class_manager, scoring_system, rating_system):
        super().__init__()
        
        self.config = config
        self.story_generator = story_generator
        self.inventory_manager = inventory_manager
        self.class_manager = class_manager
        self.scoring_system = scoring_system
        self.rating_system = rating_system
        
        self.current_story: Optional[StoryWeb] = None
        self.current_node: Optional[StoryNode] = None
        
        self._setup_ui()
        self._setup_connections()
        
        logger.info("Story editor tab initialized")

    def extract_from_text_dialog(self):
        """Show dialog to extract characters and nodes from text"""
        if not self.current_story:
            QMessageBox.warning(self, "No Story", "Please create or load a story first.")
            return

        dialog = ExtractionDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            extraction_data = dialog.get_extraction_data()
            self._perform_extraction(extraction_data)

    def _perform_extraction(self, extraction_data):
        """Perform the actual extraction with sliding window for long texts"""
        try:
            source_text = extraction_data["source_text"]
            if not source_text:
                QMessageBox.warning(self, "No Text", "Please provide source text to extract from.")
                return

            # Calculate text chunks for processing
            text_chunks = self._split_text_into_chunks(source_text)
            total_chunks = len(text_chunks)

            # Show progress dialog with proper progress tracking
            progress_dialog = QProgressBar(self)
            progress_dialog.setWindowTitle("Extracting...")
            progress_dialog.setRange(0, total_chunks * 2)  # Characters + Nodes
            progress_dialog.setValue(0)
            progress_dialog.show()

            # Status label
            status_label = QLabel(f"Processing {total_chunks} text chunks...", self)
            status_label.show()

            extracted_count = 0
            all_characters = []
            all_nodes = []

            # Extract characters from all chunks
            if extraction_data["extract_characters"]:
                status_label.setText("Extracting characters from text chunks...")
                for i, chunk in enumerate(text_chunks):
                    characters = self._extract_characters_from_text(chunk, chunk_index=i)
                    if characters:
                        all_characters.extend(characters)
                    progress_dialog.setValue(i + 1)
                    QApplication.processEvents()  # Keep UI responsive

                # Deduplicate characters by name
                unique_characters = self._deduplicate_characters(all_characters)

                # Add to story
                if unique_characters:
                    char_manager = self.current_story.get_character_manager()
                    for character in unique_characters:
                        char_manager.add_character(character)
                        extracted_count += 1
                    logger.info(f"Extracted {len(unique_characters)} unique characters from {total_chunks} chunks")

            # Extract story nodes from all chunks
            if extraction_data["extract_nodes"]:
                status_label.setText("Extracting story nodes from text chunks...")
                for i, chunk in enumerate(text_chunks):
                    nodes = self._extract_nodes_from_text(chunk, chunk_index=i)
                    if nodes:
                        all_nodes.extend(nodes)
                    progress_dialog.setValue(total_chunks + i + 1)
                    QApplication.processEvents()  # Keep UI responsive

                # Add all nodes to story (they should have unique IDs)
                if all_nodes:
                    for node in all_nodes:
                        self.current_story.add_node(node)
                        extracted_count += 1
                    logger.info(f"Extracted {len(all_nodes)} story nodes from {total_chunks} chunks")

            progress_dialog.close()
            status_label.close()

            # Refresh the UI
            self._refresh_tree()
            self.story_changed.emit(self.current_story)

            # Show detailed results
            result_message = f"Successfully extracted {extracted_count} items from {len(source_text):,} characters of text!\n\n"
            result_message += f"📊 Processing Details:\n"
            result_message += f"• Text chunks processed: {total_chunks}\n"
            result_message += f"• Characters extracted: {len(all_characters) if extraction_data['extract_characters'] else 0}\n"
            result_message += f"• Unique characters: {len(unique_characters) if extraction_data['extract_characters'] else 0}\n"
            result_message += f"• Story nodes extracted: {len(all_nodes) if extraction_data['extract_nodes'] else 0}\n\n"
            result_message += f"Check the Characters tab and Story Editor for the extracted content."

            QMessageBox.information(self, "Extraction Complete", result_message)

        except Exception as e:
            logger.error(f"Error during extraction: {e}")
            QMessageBox.critical(self, "Extraction Error", f"Failed to extract from text: {e}")

    def _split_text_into_chunks(self, text: str, chunk_size: int = 1500, overlap: int = 200):
        """Split text into overlapping chunks for processing"""
        chunks = []
        words = text.split()

        if len(words) <= chunk_size:
            return [text]

        start = 0
        while start < len(words):
            end = min(start + chunk_size, len(words))
            chunk_words = words[start:end]
            chunk_text = ' '.join(chunk_words)
            chunks.append(chunk_text)

            # Move start position with overlap
            start = end - overlap
            if start >= len(words):
                break

        logger.info(f"Split {len(words)} words into {len(chunks)} chunks (size: {chunk_size}, overlap: {overlap})")
        return chunks

    def _deduplicate_characters(self, characters):
        """Remove duplicate characters based on name similarity"""
        unique_characters = []
        seen_names = set()

        for character in characters:
            # Normalize name for comparison
            normalized_name = character.name.lower().strip()

            # Check for exact matches or very similar names
            is_duplicate = False
            for seen_name in seen_names:
                if (normalized_name == seen_name or
                    self._names_are_similar(normalized_name, seen_name)):
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_characters.append(character)
                seen_names.add(normalized_name)
            else:
                logger.debug(f"Skipping duplicate character: {character.name}")

        logger.info(f"Deduplicated {len(characters)} characters to {len(unique_characters)} unique characters")
        return unique_characters

    def _names_are_similar(self, name1: str, name2: str, threshold: float = 0.8):
        """Check if two names are similar enough to be considered duplicates"""
        # Simple similarity check - could be enhanced with fuzzy matching
        if len(name1) == 0 or len(name2) == 0:
            return False

        # Check if one name is contained in the other
        if name1 in name2 or name2 in name1:
            return True

        # Check character overlap
        set1 = set(name1.replace(' ', ''))
        set2 = set(name2.replace(' ', ''))
        overlap = len(set1.intersection(set2))
        union = len(set1.union(set2))

        similarity = overlap / union if union > 0 else 0
        return similarity >= threshold

    def _extract_characters_from_text(self, text: str, chunk_index: int = 0):
        """Extract characters from text using AI"""
        try:
            # Use the story generator to extract characters
            prompt = f"""
            Analyze the following text and extract all characters mentioned. For each character, provide:
            - Name
            - Role/occupation
            - Brief personality description
            - Importance level (1-5, where 5 is main character)

            Text to analyze (chunk {chunk_index + 1}):
            {text}

            Return as a simple list format:
            Character: [Name] | Role: [Role] | Personality: [Description] | Importance: [1-5]
            """

            response = self.story_generator.lmstudio_client.generate_text(prompt, max_tokens=1000)

            characters = []
            if response:
                # Parse the response and create character objects
                lines = response.split('\n')
                for line in lines:
                    if 'Character:' in line and '|' in line:
                        try:
                            parts = line.split('|')
                            name = parts[0].split('Character:')[1].strip()
                            role = parts[1].split('Role:')[1].strip() if len(parts) > 1 else "Unknown"
                            personality = parts[2].split('Personality:')[1].strip() if len(parts) > 2 else ""
                            importance = int(parts[3].split('Importance:')[1].strip()) if len(parts) > 3 else 3

                            # Create character using the character system
                            from story.character_system import Character, CharacterRole, CharacterPersonality, CharacterAppearance, CharacterVoice

                            character = Character(
                                id=name.lower().replace(' ', '_'),
                                name=name,
                                role=CharacterRole.MAIN if importance >= 4 else CharacterRole.SUPPORTING,
                                importance_level=importance,
                                personality=CharacterPersonality(
                                    background_story=personality,
                                    traits=[trait.strip() for trait in personality.split(',')[:3]]
                                ),
                                appearance=CharacterAppearance(),
                                voice=CharacterVoice()
                            )
                            characters.append(character)

                        except Exception as e:
                            logger.warning(f"Error parsing character line: {line} - {e}")
                            continue

            return characters

        except Exception as e:
            logger.error(f"Error extracting characters: {e}")
            return []

    def _extract_nodes_from_text(self, text: str, chunk_index: int = 0):
        """Extract story nodes from text using AI"""
        try:
            # Use the story generator to extract key scenes/nodes
            prompt = f"""
            Analyze the following text and identify key story scenes or plot points that could become interactive story nodes.
            For each scene, provide:
            - Scene ID (short identifier)
            - Scene description (2-3 sentences)
            - Scene type (introduction, conflict, climax, resolution, etc.)

            Text to analyze (chunk {chunk_index + 1}):
            {text}

            Return as a simple list format:
            Scene: [ID] | Description: [Description] | Type: [Type]
            """

            response = self.story_generator.lmstudio_client.generate_text(prompt, max_tokens=1000)

            nodes = []
            if response:
                # Parse the response and create story nodes
                lines = response.split('\n')
                for i, line in enumerate(lines):
                    if 'Scene:' in line and '|' in line:
                        try:
                            parts = line.split('|')
                            scene_id = parts[0].split('Scene:')[1].strip().lower().replace(' ', '_')
                            description = parts[1].split('Description:')[1].strip() if len(parts) > 1 else ""
                            scene_type = parts[2].split('Type:')[1].strip() if len(parts) > 2 else "story"

                            # Create story node with unique ID including chunk index
                            node = StoryNode(
                                id=f"extracted_chunk{chunk_index}_{scene_id}_{i}",
                                text=description,
                                node_type=NodeType.STORY,
                                is_entry=(chunk_index == 0 and i == 0),  # First node of first chunk is entry point
                                is_ending=False,
                                rating="safe"
                            )
                            nodes.append(node)

                        except Exception as e:
                            logger.warning(f"Error parsing scene line: {line} - {e}")
                            continue

            return nodes

        except Exception as e:
            logger.error(f"Error extracting nodes: {e}")
            return []
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QHBoxLayout(self)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel - Story tree
        self._create_story_tree_panel(splitter)
        
        # Right panel - Node editor
        self._create_node_editor_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
    
    def _create_story_tree_panel(self, parent):
        """Create the story tree panel"""
        tree_widget = QWidget()
        tree_layout = QVBoxLayout(tree_widget)
        
        # Header
        header_layout = QHBoxLayout()
        header_label = QLabel("Story Structure")
        header_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(header_label)
        
        # Import button
        import_button = QPushButton("Import Storyline")
        import_button.clicked.connect(self.import_storyline_dialog)
        header_layout.addWidget(import_button)

        # Extract button
        extract_button = QPushButton("Extract from Text")
        extract_button.clicked.connect(self.extract_from_text_dialog)
        extract_button.setStyleSheet("background-color: #17a2b8; color: white; font-weight: bold;")
        header_layout.addWidget(extract_button)
        
        tree_layout.addLayout(header_layout)
        
        # Tree widget
        self.story_tree = QTreeWidget()
        self.story_tree.setHeaderLabels(["Node", "Type", "Rating", "Score"])
        self.story_tree.itemClicked.connect(self.on_tree_item_clicked)
        tree_layout.addWidget(self.story_tree)
        
        # Story actions
        actions_layout = QHBoxLayout()
        
        add_node_button = QPushButton("Add Node")
        add_node_button.clicked.connect(self.add_node)
        actions_layout.addWidget(add_node_button)
        
        delete_node_button = QPushButton("Delete Node")
        delete_node_button.clicked.connect(self.delete_node)
        actions_layout.addWidget(delete_node_button)
        
        tree_layout.addLayout(actions_layout)
        
        parent.addWidget(tree_widget)
    
    def _create_node_editor_panel(self, parent):
        """Create the node editor panel"""
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        
        # Header
        header_label = QLabel("Node Editor")
        header_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        editor_layout.addWidget(header_label)
        
        # Node properties
        props_group = QGroupBox("Node Properties")
        props_layout = QFormLayout(props_group)
        
        self.node_id_input = QLineEdit()
        self.node_id_input.setReadOnly(True)
        props_layout.addRow("ID:", self.node_id_input)
        
        self.node_type_combo = QComboBox()
        self.node_type_combo.addItems([t.value for t in NodeType])
        props_layout.addRow("Type:", self.node_type_combo)
        
        self.is_entry_check = QCheckBox()
        props_layout.addRow("Entry Point:", self.is_entry_check)
        
        self.is_ending_check = QCheckBox()
        props_layout.addRow("Ending:", self.is_ending_check)
        
        self.ending_type_combo = QComboBox()
        self.ending_type_combo.addItems([t.value for t in EndingType])
        props_layout.addRow("Ending Type:", self.ending_type_combo)
        
        self.rating_combo = QComboBox()
        self.rating_combo.addItems(["safe", "spicy"])
        props_layout.addRow("Rating:", self.rating_combo)
        
        self.is_premium_check = QCheckBox()
        props_layout.addRow("Premium:", self.is_premium_check)
        
        self.score_input = QSpinBox()
        self.score_input.setRange(0, 100)
        self.score_input.setSuffix("%")
        props_layout.addRow("Score:", self.score_input)
        
        editor_layout.addWidget(props_group)
        
        # Node text
        text_group = QGroupBox("Node Text")
        text_layout = QVBoxLayout(text_group)
        
        self.node_text_edit = QTextEdit()
        self.node_text_edit.setMaximumHeight(150)
        text_layout.addWidget(self.node_text_edit)
        
        editor_layout.addWidget(text_group)
        
        # Choices
        choices_group = QGroupBox("Choices")
        choices_layout = QVBoxLayout(choices_group)

        # Choices list
        self.choices_list = QListWidget()
        self.choices_list.setMaximumHeight(100)
        choices_layout.addWidget(self.choices_list)

        # Choice editor
        choice_editor_layout = QHBoxLayout()

        self.choice_text_edit = QLineEdit()
        self.choice_text_edit.setPlaceholderText("Choice text...")
        choice_editor_layout.addWidget(self.choice_text_edit)

        self.choice_target_combo = QComboBox()
        self.choice_target_combo.setMinimumWidth(150)
        choice_editor_layout.addWidget(self.choice_target_combo)

        add_choice_button = QPushButton("Add")
        add_choice_button.clicked.connect(self.add_choice)
        choice_editor_layout.addWidget(add_choice_button)

        remove_choice_button = QPushButton("Remove")
        remove_choice_button.clicked.connect(self.remove_choice)
        choice_editor_layout.addWidget(remove_choice_button)

        choices_layout.addLayout(choice_editor_layout)
        editor_layout.addWidget(choices_group)
        
        # Save button
        save_button = QPushButton("Save Node")
        save_button.clicked.connect(self.save_current_node)
        editor_layout.addWidget(save_button)
        
        parent.addWidget(editor_widget)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.is_ending_check.toggled.connect(self.on_ending_toggled)
    
    def set_story(self, story: StoryWeb):
        """Set the current story"""
        self.current_story = story
        self.current_node = None
        self._refresh_tree()
        self._clear_editor()
    
    def _refresh_tree(self):
        """Refresh the story tree display"""
        self.story_tree.clear()
        
        if not self.current_story:
            return
        
        # Add entry points
        entry_item = QTreeWidgetItem(["Entry Points", "", "", ""])
        entry_item.setExpanded(True)
        self.story_tree.addTopLevelItem(entry_item)
        
        for entry_id in self.current_story.entry_points:
            if entry_id in self.current_story.nodes:
                node = self.current_story.nodes[entry_id]
                self._add_node_to_tree(node, entry_item)
        
        # Add other nodes
        other_item = QTreeWidgetItem(["Other Nodes", "", "", ""])
        other_item.setExpanded(True)
        self.story_tree.addTopLevelItem(other_item)
        
        for node in self.current_story.nodes.values():
            if not node.is_entry:
                self._add_node_to_tree(node, other_item)
    
    def _add_node_to_tree(self, node: StoryNode, parent_item: QTreeWidgetItem):
        """Add a node to the tree"""
        score_text = f"{node.score:.1f}%" if node.score is not None else ""
        
        item = QTreeWidgetItem([
            f"{node.id[:20]}...",
            node.node_type.value,
            node.rating,
            score_text
        ])
        
        item.setData(0, Qt.ItemDataRole.UserRole, node.id)
        parent_item.addChild(item)
    
    def _clear_editor(self):
        """Clear the node editor"""
        self.node_id_input.clear()
        self.node_text_edit.clear()
        self.node_type_combo.setCurrentIndex(0)
        self.is_entry_check.setChecked(False)
        self.is_ending_check.setChecked(False)
        self.ending_type_combo.setCurrentIndex(0)
        self.rating_combo.setCurrentIndex(0)
        self.is_premium_check.setChecked(False)
        self.score_input.setValue(0)
        self.choices_list.clear()
        self.choice_text_edit.clear()
        self.choice_target_combo.clear()
    
    def on_tree_item_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle tree item click"""
        node_id = item.data(0, Qt.ItemDataRole.UserRole)
        if node_id and self.current_story:
            self.select_node(node_id)
    
    def select_node(self, node_id: str):
        """Select and edit a specific node"""
        if not self.current_story or node_id not in self.current_story.nodes:
            return
        
        self.current_node = self.current_story.nodes[node_id]
        self._load_node_to_editor(self.current_node)
    
    def _load_node_to_editor(self, node: StoryNode):
        """Load node data into the editor"""
        self.node_id_input.setText(node.id)
        self.node_text_edit.setPlainText(node.text)

        # Set combo boxes
        type_index = self.node_type_combo.findText(node.node_type.value)
        self.node_type_combo.setCurrentIndex(type_index)

        rating_index = self.rating_combo.findText(node.rating)
        self.rating_combo.setCurrentIndex(rating_index)

        if node.ending_type:
            ending_index = self.ending_type_combo.findText(node.ending_type.value)
            self.ending_type_combo.setCurrentIndex(ending_index)

        # Set checkboxes
        self.is_entry_check.setChecked(node.is_entry)
        self.is_ending_check.setChecked(node.is_ending)
        self.is_premium_check.setChecked(node.is_premium)

        # Set score
        if node.score is not None:
            self.score_input.setValue(int(node.score))

        # Load choices
        self._load_node_choices(node)
        self._update_choice_targets()
    
    def on_ending_toggled(self, checked: bool):
        """Handle ending checkbox toggle"""
        self.ending_type_combo.setEnabled(checked)
    
    def save_current_node(self):
        """Save the current node"""
        if not self.current_node or not self.current_story:
            return
        
        try:
            # Update node properties
            self.current_node.text = self.node_text_edit.toPlainText()
            self.current_node.node_type = NodeType(self.node_type_combo.currentText())
            self.current_node.is_entry = self.is_entry_check.isChecked()
            self.current_node.is_ending = self.is_ending_check.isChecked()
            self.current_node.rating = self.rating_combo.currentText()
            self.current_node.is_premium = self.is_premium_check.isChecked()
            
            if self.is_ending_check.isChecked():
                self.current_node.ending_type = EndingType(self.ending_type_combo.currentText())
            else:
                self.current_node.ending_type = None
            
            # Update entry points list
            if self.current_node.is_entry and self.current_node.id not in self.current_story.entry_points:
                self.current_story.entry_points.append(self.current_node.id)
            elif not self.current_node.is_entry and self.current_node.id in self.current_story.entry_points:
                self.current_story.entry_points.remove(self.current_node.id)
            
            # Update endings list
            if self.current_node.is_ending and self.current_node.id not in self.current_story.endings:
                self.current_story.endings.append(self.current_node.id)
            elif not self.current_node.is_ending and self.current_node.id in self.current_story.endings:
                self.current_story.endings.remove(self.current_node.id)
            
            self._refresh_tree()
            self.story_changed.emit(self.current_story)
            
            logger.info(f"Saved node: {self.current_node.id}")
            
        except Exception as e:
            logger.error(f"Error saving node: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save node: {e}")
    
    def add_node(self):
        """Add a new node"""
        # TODO: Implement add node functionality
        QMessageBox.information(self, "Add Node", "Add node functionality coming soon...")
    
    def delete_node(self):
        """Delete the current node"""
        # TODO: Implement delete node functionality
        QMessageBox.information(self, "Delete Node", "Delete node functionality coming soon...")
    
    def import_storyline_dialog(self):
        """Show the import storyline dialog"""
        dialog = ImportDialog(self.class_manager, self)
        
        if dialog.exec() == QDialog.Accepted:
            import_data = dialog.get_import_data()
            
            if not import_data["title"] or not import_data["source_text"]:
                QMessageBox.warning(self, "Import Error", "Please provide both title and source text")
                return
            
            # Show progress and start import
            dialog.show_progress(True)
            
            self.import_worker = StoryImportWorker(
                self.story_generator,
                import_data["source_text"],
                import_data["title"],
                import_data["player_class"]
            )
            
            self.import_worker.progress_updated.connect(dialog.set_progress)
            self.import_worker.status_updated.connect(dialog.set_status)
            self.import_worker.import_completed.connect(self.on_import_completed)
            self.import_worker.import_failed.connect(self.on_import_failed)
            
            self.import_worker.start()
    
    def on_import_completed(self, story: StoryWeb):
        """Handle successful import"""
        self.current_story = story
        self.story_changed.emit(story)
        self._refresh_tree()
        
        QMessageBox.information(
            self, 
            "Import Successful", 
            f"Storyline imported successfully!\nNodes: {len(story.nodes)}"
        )
    
    def on_import_failed(self, error: str):
        """Handle failed import"""
        QMessageBox.critical(self, "Import Failed", f"Failed to import storyline:\n{error}")

    def add_choice(self):
        """Add a new choice to the current node"""
        choice_text = self.choice_text_edit.text().strip()
        target_node_id = self.choice_target_combo.currentText()

        if not choice_text:
            QMessageBox.warning(self, "Warning", "Choice text is required")
            return

        if not target_node_id:
            QMessageBox.warning(self, "Warning", "Target node is required")
            return

        # Add to choices list
        choice_data = {
            'text': choice_text,
            'target': target_node_id
        }

        item_text = f"{choice_text} → {target_node_id}"
        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, choice_data)
        self.choices_list.addItem(item)

        # Clear inputs
        self.choice_text_edit.clear()
        self.choice_target_combo.setCurrentIndex(0)

    def remove_choice(self):
        """Remove selected choice"""
        current_row = self.choices_list.currentRow()
        if current_row >= 0:
            self.choices_list.takeItem(current_row)

    def _update_choice_targets(self):
        """Update the choice target combo box"""
        self.choice_target_combo.clear()

        if self.current_story:
            node_ids = list(self.current_story.nodes.keys())
            node_ids.sort()
            self.choice_target_combo.addItems(node_ids)

    def _load_node_choices(self, node):
        """Load choices for the given node into the editor"""
        self.choices_list.clear()

        for choice in node.choices:
            choice_data = {
                'text': choice.text,
                'target': choice.target_node_id
            }

            item_text = f"{choice.text} → {choice.target_node_id}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, choice_data)
            self.choices_list.addItem(item)
