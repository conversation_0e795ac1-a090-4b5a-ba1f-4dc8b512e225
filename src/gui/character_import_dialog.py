"""
Character Import Dialog - Select and import characters from other stories
"""

import logging
from typing import Dict, List, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QCheckBox, QTextEdit, QGroupBox,
    QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt

from story.character_system import CharacterManager

logger = logging.getLogger(__name__)


class CharacterImportDialog(QDialog):
    """Dialog for selecting and importing characters from another story"""
    
    def __init__(self, source_character_manager: CharacterManager, parent=None):
        super().__init__(parent)
        self.source_character_manager = source_character_manager
        self.selected_character_data = []
        
        self.setWindowTitle("Import Characters")
        self.setMinimumSize(800, 600)
        self.setup_ui()
        self.populate_characters()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Select Characters to Import")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Main content area
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - character list
        left_widget = QGroupBox("Available Characters")
        left_layout = QVBoxLayout(left_widget)
        
        # Select all/none buttons
        select_buttons_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_characters)
        select_buttons_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_no_characters)
        select_buttons_layout.addWidget(self.select_none_btn)
        
        select_buttons_layout.addStretch()
        left_layout.addLayout(select_buttons_layout)
        
        # Character list
        self.character_list = QListWidget()
        self.character_list.itemSelectionChanged.connect(self.on_character_selected)
        left_layout.addWidget(self.character_list)
        
        splitter.addWidget(left_widget)
        
        # Right side - character preview
        right_widget = QGroupBox("Character Preview")
        right_layout = QVBoxLayout(right_widget)
        
        self.character_preview = QTextEdit()
        self.character_preview.setReadOnly(True)
        self.character_preview.setPlaceholderText("Select a character to see details...")
        right_layout.addWidget(self.character_preview)
        
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([400, 400])
        layout.addWidget(splitter)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.import_btn = QPushButton("Import Selected")
        self.import_btn.clicked.connect(self.accept)
        self.import_btn.setStyleSheet("background-color: #0078d4; color: white; font-weight: bold;")
        button_layout.addWidget(self.import_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def populate_characters(self):
        """Populate the character list"""
        self.character_list.clear()
        
        for character in self.source_character_manager.characters.values():
            item = QListWidgetItem()
            
            # Create checkbox widget
            checkbox = QCheckBox(f"{character.name} ({character.role.value})")
            checkbox.setToolTip(f"Importance: {character.importance_level}/5")
            
            # Store character data
            item.setData(Qt.ItemDataRole.UserRole, character)
            
            # Add item and set widget
            self.character_list.addItem(item)
            self.character_list.setItemWidget(item, checkbox)
            
            # Connect checkbox signal
            checkbox.stateChanged.connect(self.update_selection)
    
    def on_character_selected(self):
        """Handle character selection for preview"""
        current_item = self.character_list.currentItem()
        if not current_item:
            self.character_preview.clear()
            return
        
        character = current_item.data(Qt.ItemDataRole.UserRole)
        if character:
            self.show_character_preview(character)
    
    def show_character_preview(self, character):
        """Show character details in preview"""
        preview_text = f"""
<h3>{character.name}</h3>
<p><b>Role:</b> {character.role.value.title()}</p>
<p><b>Importance:</b> {character.importance_level}/5</p>
<p><b>Player Character:</b> {'Yes' if character.is_player_character else 'No'}</p>

<h4>Appearance:</h4>
<p><b>Physical Description:</b> {character.appearance.physical_description or 'Not specified'}</p>
<p><b>Age Range:</b> {character.appearance.age_range or 'Not specified'}</p>
<p><b>Hair Color:</b> {character.appearance.hair_color or 'Not specified'}</p>
<p><b>Eye Color:</b> {character.appearance.eye_color or 'Not specified'}</p>
<p><b>Clothing:</b> {character.appearance.clothing or 'Not specified'}</p>
<p><b>Distinctive Features:</b> {character.appearance.distinctive_features or 'Not specified'}</p>

<h4>Voice:</h4>
<p><b>Voice Description:</b> {character.voice.voice_description or 'Not specified'}</p>
<p><b>Accent:</b> {character.voice.accent or 'Not specified'}</p>
<p><b>Vocabulary Style:</b> {character.voice.vocabulary_style or 'Not specified'}</p>
<p><b>Speech Patterns:</b> {', '.join(character.voice.speech_patterns) if character.voice.speech_patterns else 'Not specified'}</p>

<h4>Personality:</h4>
<p><b>Traits:</b> {', '.join(character.personality.traits) if character.personality.traits else 'Not specified'}</p>
<p><b>Motivations:</b> {', '.join(character.personality.motivations) if character.personality.motivations else 'Not specified'}</p>
<p><b>Fears:</b> {', '.join(character.personality.fears) if character.personality.fears else 'Not specified'}</p>
<p><b>Goals:</b> {', '.join(character.personality.goals) if character.personality.goals else 'Not specified'}</p>
<p><b>Moral Alignment:</b> {character.personality.moral_alignment or 'Not specified'}</p>
<p><b>Background:</b> {character.personality.background_story or 'Not specified'}</p>

<h4>Tags:</h4>
<p>{', '.join(character.tags) if character.tags else 'No tags'}</p>

<h4>Reference Files:</h4>
<p><b>Image:</b> {character.appearance.reference_image_path or 'None'}</p>
<p><b>Voice Sample:</b> {character.voice.voice_file_path or 'None'}</p>
        """.strip()
        
        self.character_preview.setHtml(preview_text)
    
    def select_all_characters(self):
        """Select all characters"""
        for i in range(self.character_list.count()):
            item = self.character_list.item(i)
            checkbox = self.character_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(True)
    
    def select_no_characters(self):
        """Deselect all characters"""
        for i in range(self.character_list.count()):
            item = self.character_list.item(i)
            checkbox = self.character_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(False)
    
    def update_selection(self):
        """Update selection when checkbox state changes"""
        selected_count = 0
        for i in range(self.character_list.count()):
            item = self.character_list.item(i)
            checkbox = self.character_list.itemWidget(item)
            if checkbox and checkbox.isChecked():
                selected_count += 1
        
        self.import_btn.setText(f"Import Selected ({selected_count})")
        self.import_btn.setEnabled(selected_count > 0)
    
    def get_selected_characters(self) -> List[Dict[str, Any]]:
        """Get the selected character data for import"""
        selected_characters = []
        
        for i in range(self.character_list.count()):
            item = self.character_list.item(i)
            checkbox = self.character_list.itemWidget(item)
            
            if checkbox and checkbox.isChecked():
                character = item.data(Qt.ItemDataRole.UserRole)
                if character:
                    # Export character data
                    char_data = self.source_character_manager.export_character_data(character.id)
                    if char_data:
                        selected_characters.append(char_data)
        
        return selected_characters
    
    def accept(self):
        """Accept dialog and validate selection"""
        selected_characters = self.get_selected_characters()
        
        if not selected_characters:
            QMessageBox.warning(self, "No Selection", "Please select at least one character to import.")
            return
        
        self.selected_character_data = selected_characters
        super().accept()
