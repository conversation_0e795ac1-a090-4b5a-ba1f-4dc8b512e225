"""
Main Window - Primary GUI interface for CYOA Automation System
Provides tabbed interface for story management, visualization, and X posting
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout, QWidget,
    QMenuBar, QStatusBar, QMessageBox, QFileDialog, QDialog,
    QLabel, QPushButton, QTextEdit, QSplitter, QGroupBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QFont, QAction

from .story_editor import StoryEditorTab
from .graph_viewer import GraphViewerTab
from .x_manager import XManagerTab
from .character_editor import CharacterEditorTab
from .story_wizard import StoryCreationWizard
from .analytics_tab import AnalyticsTab
from .quiz_tab import QuizCreationTab
from .voice_tab import VoiceTab
from .ab_testing_tab import ABTestingTab
from .media_generation_tab import MediaGenerationTab
from .logs_tab import LogsTab
from .settings_tab import SettingsTab
# Authentication now handled through Services menu
from .chat_assistant_new import ChatAssistantSidebar, ChatAssistantFloating
from .help_system import HelpTab
from .items_tab import ItemsTab
from story.story_web import StoryWeb

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """Main application window with tabbed interface"""
    
    # Signals
    status_updated = pyqtSignal(str, str)  # message, type
    story_loaded = pyqtSignal(object)  # StoryWeb
    story_saved = pyqtSignal(str)  # filepath
    
    def __init__(self, config: Dict[str, Any], lmstudio_client, story_generator,
                 inventory_manager, class_manager, scoring_system, rating_system):
        super().__init__()

        self.config = config
        self.lmstudio_client = lmstudio_client
        self.story_generator = story_generator
        self.inventory_manager = inventory_manager
        self.class_manager = class_manager
        self.scoring_system = scoring_system
        self.rating_system = rating_system
        
        self.current_story: Optional[StoryWeb] = None
        self.current_file_path: Optional[str] = None
        
        self._setup_ui()
        self._setup_connections()
        self._setup_status_timer()
        
        logger.info("Main window initialized")

    def _init_service_managers(self):
        """Initialize service managers for authentication"""
        try:
            # Initialize X authenticator
            try:
                from social.x_auth import XAuthenticator
                self.x_authenticator = XAuthenticator(self.config)
            except Exception as e:
                logger.warning(f"X authenticator not available: {e}")
                self.x_authenticator = None

            # Initialize Google Drive backup manager
            try:
                from backup.google_drive_backup import BackupManager
                self.backup_manager = BackupManager(self.config)
            except Exception as e:
                logger.warning(f"Google Drive backup manager not available: {e}")
                self.backup_manager = None

            logger.info("Service managers initialized")
        except Exception as e:
            logger.error(f"Error initializing service managers: {e}")
            self.x_authenticator = None
            self.backup_manager = None
    
    def _setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("CYOA Automation System")
        self.setGeometry(100, 100, 1400, 800)  # Made shorter to fit screen better
        
        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            QTabBar::tab {
                background-color: #555555;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QTextEdit, QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Initialize service managers first
        self._init_service_managers()

        # Create menu bar
        self._create_menu_bar()

        # Setup keyboard shortcuts for chat assistant
        self._setup_chat_shortcuts()
        
        # Create main content area
        self._create_main_content(layout)
        
        # Create status bar
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        wizard_action = QAction('New Story Wizard...', self)
        wizard_action.setShortcut('Ctrl+N')
        wizard_action.triggered.connect(self.show_story_wizard)
        file_menu.addAction(wizard_action)

        new_action = QAction('New Empty Story', self)
        new_action.setShortcut('Ctrl+Shift+N')
        new_action.triggered.connect(self.new_story)
        file_menu.addAction(new_action)
        
        open_action = QAction('Open Story', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_story)
        file_menu.addAction(open_action)
        
        save_action = QAction('Save Story', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_story)
        file_menu.addAction(save_action)
        
        save_as_action = QAction('Save Story As...', self)
        save_as_action.setShortcut('Ctrl+Shift+S')
        save_as_action.triggered.connect(self.save_story_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        # Story details
        details_action = QAction('📋 Story Details...', self)
        details_action.setShortcut('Ctrl+I')
        details_action.triggered.connect(self.show_story_details)
        file_menu.addAction(details_action)

        file_menu.addSeparator()

        import_action = QAction('Import Storyline...', self)
        import_action.triggered.connect(self.import_storyline)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        health_action = QAction('System Health Check', self)
        health_action.triggered.connect(self.show_health_check)
        tools_menu.addAction(health_action)

        tools_menu.addSeparator()

        validate_action = QAction('Validate Story', self)
        validate_action.triggered.connect(self.validate_story)
        tools_menu.addAction(validate_action)

        calculate_scores_action = QAction('Calculate Scores', self)
        calculate_scores_action.triggered.connect(self.calculate_scores)
        tools_menu.addAction(calculate_scores_action)

        update_ratings_action = QAction('Update Ratings', self)
        update_ratings_action.triggered.connect(self.update_ratings)
        tools_menu.addAction(update_ratings_action)

        # AI Assistant menu
        ai_menu = menubar.addMenu('AI Assistant')

        # Toggle sidebar
        sidebar_action = QAction('Toggle Sidebar', self)
        sidebar_action.setShortcut('Ctrl+Shift+A')
        sidebar_action.triggered.connect(self.toggle_chat_sidebar)
        ai_menu.addAction(sidebar_action)

        # Show floating window
        floating_action = QAction('Show Floating Assistant', self)
        floating_action.setShortcut('Ctrl+Alt+A')
        floating_action.triggered.connect(self.show_chat_floating)
        ai_menu.addAction(floating_action)

        # Services menu
        services_menu = menubar.addMenu('Services')

        # X/Twitter authentication
        self.x_login_action = QAction('🔑 Login to X', self)
        self.x_login_action.triggered.connect(self.toggle_x_authentication)
        services_menu.addAction(self.x_login_action)

        # X configuration
        self.x_config_action = QAction('⚙️ Configure X', self)
        self.x_config_action.triggered.connect(self.show_x_config)
        services_menu.addAction(self.x_config_action)

        services_menu.addSeparator()

        # Google Drive authentication
        self.drive_login_action = QAction('🔑 Login to Google Drive', self)
        self.drive_login_action.triggered.connect(self.toggle_drive_authentication)
        services_menu.addAction(self.drive_login_action)

        # Google Drive configuration
        self.drive_config_action = QAction('⚙️ Configure Google Drive', self)
        self.drive_config_action.triggered.connect(self.show_drive_config)
        services_menu.addAction(self.drive_config_action)

        # Backup menu
        backup_menu = menubar.addMenu('Backup')

        # Backup to Google Drive
        backup_drive_action = QAction('📤 Backup to Google Drive...', self)
        backup_drive_action.triggered.connect(self.show_backup_options)
        backup_menu.addAction(backup_drive_action)

        # Restore from Google Drive
        restore_drive_action = QAction('📥 Restore from Google Drive...', self)
        restore_drive_action.triggered.connect(self.show_restore_options)
        backup_menu.addAction(restore_drive_action)

        backup_menu.addSeparator()

        # Export story data
        export_action = QAction('📁 Export Story Data...', self)
        export_action.triggered.connect(self.export_story_data)
        backup_menu.addAction(export_action)

        # Import story data
        import_action = QAction('📁 Import Story Data...', self)
        import_action.triggered.connect(self.import_story_data)
        backup_menu.addAction(import_action)

        # Tools menu
        tools_menu = menubar.addMenu('🔧 Tools')

        settings_action = QAction('⚙️ Settings...', self)
        settings_action.setShortcut('Ctrl+,')
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        logs_action = QAction('📋 View Logs...', self)
        logs_action.setShortcut('Ctrl+L')
        logs_action.triggered.connect(self.show_logs)
        tools_menu.addAction(logs_action)

        # Help menu
        help_menu = menubar.addMenu('❓ Help')

        help_action = QAction('📖 Help & Documentation...', self)
        help_action.setShortcut('F1')
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)

        about_action = QAction('ℹ️ About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        # Update service menu states
        self._update_service_menu_states()
    
    def _create_main_content(self, layout):
        """Create the main content area with tabs"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs in organized order
        # Core editing tabs
        self.story_editor_tab = StoryEditorTab(
            self.config, self.story_generator, self.inventory_manager,
            self.class_manager, self.scoring_system, self.rating_system
        )
        self.tab_widget.addTab(self.story_editor_tab, "📝 Story Editor")

        self.graph_viewer_tab = GraphViewerTab(self.config)
        self.tab_widget.addTab(self.graph_viewer_tab, "📊 Graph Viewer")

        self.character_editor_tab = CharacterEditorTab(self.config, self.lmstudio_client)
        self.tab_widget.addTab(self.character_editor_tab, "👥 Characters")

        self.items_tab = ItemsTab(self.config)
        self.tab_widget.addTab(self.items_tab, "🎒 Items")

        # Media and content generation
        self.media_generation_tab = MediaGenerationTab(self.config, getattr(self, 'comfyui_client', None))
        self.tab_widget.addTab(self.media_generation_tab, "🎬 Media Studio")

        self.voice_tab = VoiceTab(self.config)
        self.tab_widget.addTab(self.voice_tab, "🎤 Voice Studio")

        # Social and publishing
        self.x_manager_tab = XManagerTab(self.config, self.lmstudio_client)
        self.tab_widget.addTab(self.x_manager_tab, "📱 X Manager")

        # Analytics and testing
        self.analytics_tab = AnalyticsTab(self.config)
        self.tab_widget.addTab(self.analytics_tab, "📈 Analytics")

        self.ab_testing_tab = ABTestingTab(self.config)
        self.tab_widget.addTab(self.ab_testing_tab, "🧪 A/B Testing")

        # Quiz creator moved to the right
        self.quiz_tab = QuizCreationTab(self.config)
        self.tab_widget.addTab(self.quiz_tab, "🧠 Quiz Creator")

        # Initialize floating windows (not tabs)
        self.settings_window = None
        self.logs_window = None
        self.help_window = None

        # Initialize chat assistant (sidebar and floating)
        self.chat_sidebar = ChatAssistantSidebar(self.config, self.lmstudio_client, self)
        self.chat_floating = ChatAssistantFloating(self.config, self.lmstudio_client, self)

        # Add chat assistant to main window as overlay
        self.chat_sidebar.setParent(self)
        self.chat_sidebar.raise_()



        # Connect tab signals
        self.story_editor_tab.story_changed.connect(self.on_story_changed)
        self.graph_viewer_tab.node_selected.connect(self.on_node_selected)

        # Connect chat assistant signals
        self.chat_sidebar.node_creation_requested.connect(self.on_chat_node_creation)
        self.chat_sidebar.tab_navigation_requested.connect(self.on_chat_tab_navigation)
        self.chat_floating.node_creation_requested.connect(self.on_chat_node_creation)
        self.chat_floating.tab_navigation_requested.connect(self.on_chat_tab_navigation)

        # Connect new tab signals
        self.analytics_tab.optimization_requested.connect(self.on_optimization_requested)
        self.quiz_tab.quiz_created.connect(self.on_quiz_created)
        self.voice_tab.voice_sample_added.connect(self.on_voice_sample_added)
        self.ab_testing_tab.test_created.connect(self.on_ab_test_created)

        # Authentication now handled through Services menu
    
    def _create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status labels
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        self.lmstudio_status = QLabel("LM Studio: Checking...")
        self.status_bar.addPermanentWidget(self.lmstudio_status)
        
        self.story_status = QLabel("No story loaded")
        self.status_bar.addPermanentWidget(self.story_status)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.status_updated.connect(self.update_status_display)
        self.story_loaded.connect(self.on_story_loaded)
        self.story_saved.connect(self.on_story_saved)
    
    def _setup_status_timer(self):
        """Setup timer for status updates"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_lmstudio_status)
        self.status_timer.start(10000)  # Update every 10 seconds

        # Initial status update
        self.update_lmstudio_status()

    def update_status(self, message: str, status_type: str = "info"):
        """Update status bar message"""
        self.status_updated.emit(message, status_type)

    def update_status_display(self, message: str, status_type: str):
        """Update the status display"""
        self.status_label.setText(message)

        # Set color based on status type
        if status_type == "error":
            self.status_label.setStyleSheet("color: #ff6b6b;")
        elif status_type == "success":
            self.status_label.setStyleSheet("color: #51cf66;")
        elif status_type == "warning":
            self.status_label.setStyleSheet("color: #ffd43b;")
        else:
            self.status_label.setStyleSheet("color: #ffffff;")

    def update_lmstudio_status(self):
        """Update LM Studio connection status"""
        try:
            if self.lmstudio_client.is_available():
                current_model = self.lmstudio_client.get_current_model()
                if current_model:
                    self.lmstudio_status.setText(f"LM Studio: {current_model}")
                    self.lmstudio_status.setStyleSheet("color: #51cf66;")
                else:
                    self.lmstudio_status.setText("LM Studio: No model loaded")
                    self.lmstudio_status.setStyleSheet("color: #ffd43b;")
            else:
                self.lmstudio_status.setText("LM Studio: Disconnected")
                self.lmstudio_status.setStyleSheet("color: #ff6b6b;")
        except Exception as e:
            self.lmstudio_status.setText("LM Studio: Error")
            self.lmstudio_status.setStyleSheet("color: #ff6b6b;")
            logger.error(f"Error checking LM Studio status: {e}")
    
    def show_story_wizard(self):
        """Show the story creation wizard"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return

            wizard = StoryCreationWizard(self.config, self.lmstudio_client, self)
            wizard.story_created.connect(self.on_wizard_story_created)

            result = wizard.exec()
            if result == 1:  # QDialog.Accepted value
                self.update_status("Story wizard completed", "success")

        except Exception as e:
            logger.error(f"Error showing story wizard: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show story wizard: {e}")

    def on_wizard_story_created(self, story: StoryWeb):
        """Handle story created from wizard"""
        self.current_story = story
        self.current_file_path = None

        self.story_loaded.emit(self.current_story)
        self.update_status("Story created from wizard", "success")

    def show_health_check(self):
        """Show system health check dialog"""
        try:
            from ..utils.system_health import SystemHealthChecker

            health_checker = SystemHealthChecker(self.config)
            health_checker.run_all_checks()

            summary = health_checker.get_health_summary()
            overall_status = summary['overall_status']

            # Create health check dialog
            dialog_text = f"""
            <h3>System Health Check</h3>
            <p><b>Overall Status:</b> {overall_status.upper()}</p>

            <h4>Component Status:</h4>
            <ul>
            """

            for name, check in summary['checks'].items():
                status_icon = "✅" if check['status'] == 'healthy' else "⚠️" if check['status'] == 'warning' else "❌"
                dialog_text += f"<li>{status_icon} <b>{name}:</b> {check['message']}</li>"

            dialog_text += "</ul>"

            if summary['recommendations']:
                dialog_text += "<h4>Recommendations:</h4><ul>"
                for rec in summary['recommendations'][:5]:
                    dialog_text += f"<li>{rec}</li>"
                dialog_text += "</ul>"

            if overall_status == 'healthy':
                QMessageBox.information(self, "System Health", dialog_text)
            elif overall_status == 'warning':
                QMessageBox.warning(self, "System Health", dialog_text)
            else:
                QMessageBox.critical(self, "System Health", dialog_text)

        except Exception as e:
            logger.error(f"Error running health check: {e}")
            QMessageBox.critical(self, "Error", f"Failed to run health check: {e}")

    def new_story(self):
        """Create a new empty story with just start and end nodes"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return

            # Get story name and type from user
            from PyQt6.QtWidgets import QInputDialog

            title, ok = QInputDialog.getText(
                self, 'New Story', 'Enter story title:', text='Untitled Story'
            )
            if not ok or not title.strip():
                return

            story_type, ok = QInputDialog.getItem(
                self, 'Story Type', 'Select story type:',
                ['Story Web', 'Linear Story', 'Quiz', 'Interactive Fiction'], 0, False
            )
            if not ok:
                return

            self.current_story = StoryWeb(self.config)
            self.current_file_path = None

            # Create simple start node
            from story.story_web import StoryNode, NodeType, EndingType, Choice
            start_node = StoryNode(
                id="start",
                text="This is the beginning of your story. Edit this node to start creating your adventure!",
                node_type=NodeType.STORY,
                is_entry=True
            )

            # Create simple end node
            end_node = StoryNode(
                id="end",
                text="The End. This is where your story concludes.",
                node_type=NodeType.ENDING,
                is_ending=True,
                ending_type=EndingType.SUCCESS
            )

            # Connect start to end with a simple choice
            choice = Choice(
                id="choice_1",
                text="Continue to the end",
                target_node_id="end"
            )
            start_node.choices.append(choice)

            # Add nodes to story
            self.current_story.add_node(start_node)
            self.current_story.add_node(end_node)

            # Add the connection to the graph
            self.current_story.graph.add_edge("start", "end")

            # Set basic metadata (no fake analytics)
            self.current_story.metadata.update({
                "title": title.strip(),
                "description": f"A new {story_type.lower()}",
                "story_type": story_type,
                "created_at": str(datetime.now()),
                "version": "1.0",
                "author": "",
                "genre": "",
                "tags": ""
            })

            self.story_loaded.emit(self.current_story)
            self.update_status(f"New {story_type.lower()} '{title}' created", "success")

        except Exception as e:
            logger.error(f"Error creating new story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create new story: {e}")
    
    def open_story(self):
        """Open an existing story"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return
            
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Open Story", "data/storylines", "JSON files (*.json)"
            )
            
            if file_path:
                story = StoryWeb.load_from_file(file_path)
                if story:
                    self.current_story = story
                    self.current_file_path = file_path
                    
                    self.story_loaded.emit(self.current_story)
                    self.update_status(f"Opened story: {Path(file_path).name}", "success")
                else:
                    QMessageBox.critical(self, "Error", "Failed to load story file")
                    
        except Exception as e:
            logger.error(f"Error opening story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open story: {e}")
    
    def save_story(self):
        """Save the current story"""
        if not self.current_story:
            return
        
        if self.current_file_path:
            self._save_to_file(self.current_file_path)
        else:
            self.save_story_as()
    
    def save_story_as(self):
        """Save the current story with a new name"""
        if not self.current_story:
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Story As", "data/storylines", "JSON files (*.json)"
        )
        
        if file_path:
            self._save_to_file(file_path)
    
    def _save_to_file(self, file_path: str):
        """Save story to specified file"""
        try:
            if self.current_story.save_to_file(file_path):
                self.current_file_path = file_path
                self.story_saved.emit(file_path)
                self.update_status(f"Saved story: {Path(file_path).name}", "success")
            else:
                QMessageBox.critical(self, "Error", "Failed to save story")
                
        except Exception as e:
            logger.error(f"Error saving story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save story: {e}")
    
    def import_storyline(self):
        """Import a storyline from text"""
        try:
            self.story_editor_tab.import_storyline_dialog()
        except Exception as e:
            logger.error(f"Error importing storyline: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import storyline: {e}")
    
    def validate_story(self):
        """Validate the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Validation", "No story loaded")
            return
        
        try:
            is_valid, errors = self.current_story.validate_structure()
            
            if is_valid:
                QMessageBox.information(self, "Validation", "Story structure is valid!")
            else:
                error_text = "\n".join(errors)
                QMessageBox.warning(self, "Validation Errors", f"Story has validation errors:\n\n{error_text}")
                
        except Exception as e:
            logger.error(f"Error validating story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to validate story: {e}")
    
    def calculate_scores(self):
        """Calculate scores for the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Calculate Scores", "No story loaded")
            return
        
        try:
            self.current_story.calculate_scores()
            self.scoring_system.update_story_scores(self.current_story)
            
            # Refresh displays
            self.story_loaded.emit(self.current_story)
            self.update_status("Scores calculated", "success")
            
        except Exception as e:
            logger.error(f"Error calculating scores: {e}")
            QMessageBox.critical(self, "Error", f"Failed to calculate scores: {e}")
    
    def update_ratings(self):
        """Update content ratings for the current story"""
        if not self.current_story:
            QMessageBox.information(self, "Update Ratings", "No story loaded")
            return
        
        try:
            self.rating_system.update_story_ratings(self.current_story)
            
            # Refresh displays
            self.story_loaded.emit(self.current_story)
            self.update_status("Ratings updated", "success")
            
        except Exception as e:
            logger.error(f"Error updating ratings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to update ratings: {e}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self, 
            "About CYOA Automation System",
            """
            <h3>CYOA Automation System v0.1.0</h3>
            <p>Choose Your Own Adventure automation for X (Twitter)</p>
            <p>Features:</p>
            <ul>
            <li>Local AI story generation with Ollama</li>
            <li>Inventory and class systems</li>
            <li>Video generation with ComfyUI</li>
            <li>Automated X posting</li>
            <li>Content rating and scoring</li>
            </ul>
            <p>Built with PyQt5 and powered by local AI models.</p>
            """
        )
    
    def _check_unsaved_changes(self) -> bool:
        """Check for unsaved changes and prompt user"""
        # For now, always return False (no unsaved changes)
        # TODO: Implement proper change tracking
        return False
    
    def on_story_changed(self, story: StoryWeb):
        """Handle story changes from editor"""
        self.current_story = story

        # Update other tabs
        self.graph_viewer_tab.set_story(story)
        self.character_editor_tab.set_story(story)
        self.items_tab.set_story(story)
        self.x_manager_tab.set_story(story)

        # Update status
        node_count = len(story.nodes) if story else 0
        self.story_status.setText(f"Story: {node_count} nodes")
    
    def on_story_loaded(self, story: StoryWeb):
        """Handle story loaded signal"""
        # Update all tabs
        self.story_editor_tab.set_story(story)
        self.graph_viewer_tab.set_story(story)
        self.character_editor_tab.set_story(story)
        self.items_tab.set_story(story)
        self.x_manager_tab.set_story(story)
        self.analytics_tab.set_story(story)
        self.voice_tab.set_story(story)
        self.ab_testing_tab.set_story(story)
        self.media_generation_tab.set_story(story)
        self.chat_sidebar.set_story(story)
        self.chat_floating.set_story(story)

        # Update status
        node_count = len(story.nodes) if story else 0
        self.story_status.setText(f"Story: {node_count} nodes")
    
    def on_story_saved(self, file_path: str):
        """Handle story saved signal"""
        self.setWindowTitle(f"CYOA Automation System - {Path(file_path).name}")
    
    def on_node_selected(self, node_id: str):
        """Handle node selection from graph viewer"""
        if self.current_story and node_id in self.current_story.nodes:
            self.story_editor_tab.select_node(node_id)
    
    def on_optimization_requested(self, story_id: str):
        """Handle optimization request from analytics"""
        try:
            self.update_status("Running story optimization...", "info")
            # TODO: Implement optimization logic
            self.update_status("Optimization completed", "success")
        except Exception as e:
            logger.error(f"Error running optimization: {e}")
            self.update_status("Optimization failed", "error")

    def on_quiz_created(self, quiz):
        """Handle quiz creation"""
        try:
            self.update_status("Quiz created successfully", "success")
            # TODO: Handle quiz integration with story system
        except Exception as e:
            logger.error(f"Error handling quiz creation: {e}")
            self.update_status("Quiz creation failed", "error")

    def on_voice_sample_added(self, character_id: str, sample_path: str):
        """Handle voice sample addition"""
        try:
            self.update_status(f"Voice sample added for {character_id}", "success")
            # TODO: Trigger voice model training if enough samples
        except Exception as e:
            logger.error(f"Error handling voice sample: {e}")
            self.update_status("Voice sample addition failed", "error")

    def on_ab_test_created(self, test_id: str):
        """Handle A/B test creation"""
        try:
            self.update_status(f"A/B test {test_id} created", "success")
            # TODO: Start test monitoring
        except Exception as e:
            logger.error(f"Error handling A/B test creation: {e}")
            self.update_status("A/B test creation failed", "error")

    def on_authentication_changed(self, authenticated: bool):
        """Handle authentication status change"""
        try:
            if authenticated:
                self.update_status("Successfully authenticated with X", "success")
                # Update X manager with authentication
                authenticator = self.auth_tab.get_authenticator()
                if hasattr(self.x_manager_tab, 'set_authenticator'):
                    self.x_manager_tab.set_authenticator(authenticator)
            else:
                self.update_status("Logged out from X", "info")
        except Exception as e:
            logger.error(f"Error handling authentication change: {e}")

    def on_subscription_changed(self, tier: str):
        """Handle subscription tier change"""
        try:
            self.update_status(f"Subscription tier updated to {tier}", "info")
            # Update rate limiting across the application
            rate_limiter = self.auth_tab.get_rate_limiter()
            if rate_limiter and hasattr(self.x_manager_tab, 'set_rate_limiter'):
                self.x_manager_tab.set_rate_limiter(rate_limiter)
        except Exception as e:
            logger.error(f"Error handling subscription change: {e}")

    def on_rate_limits_updated(self, rate_limits: dict):
        """Handle rate limits update"""
        try:
            posts_per_day = rate_limits.get('posts_per_day', 0)
            self.update_status(f"Rate limits updated: {posts_per_day} posts/day", "info")
        except Exception as e:
            logger.error(f"Error handling rate limits update: {e}")

    def on_chat_node_creation(self, params: Dict[str, Any]):
        """Handle node creation request from chat"""
        try:
            # Switch to story editor tab
            self.tab_widget.setCurrentWidget(self.story_editor_tab)

            # Create node with provided parameters
            if "text" in params:
                # TODO: Implement node creation with text
                self.update_status("Node creation from chat not yet implemented", "info")
            else:
                self.update_status("Switched to Story Editor for node creation", "info")

        except Exception as e:
            logger.error(f"Error handling chat node creation: {e}")
            self.update_status("Chat node creation failed", "error")

    def on_chat_tab_navigation(self, tab_name: str):
        """Handle tab navigation request from chat"""
        try:
            # Find tab by name
            for i in range(self.tab_widget.count()):
                if tab_name in self.tab_widget.tabText(i):
                    self.tab_widget.setCurrentIndex(i)
                    self.update_status(f"Navigated to {tab_name}", "success")
                    return

            self.update_status(f"Tab not found: {tab_name}", "warning")

        except Exception as e:
            logger.error(f"Error handling chat tab navigation: {e}")
            self.update_status("Chat navigation failed", "error")

    def _setup_chat_shortcuts(self):
        """Setup keyboard shortcuts for chat assistant"""
        from PyQt6.QtGui import QShortcut, QKeySequence

        # Sidebar toggle shortcut
        sidebar_shortcut = QShortcut(QKeySequence("Ctrl+Shift+A"), self)
        sidebar_shortcut.activated.connect(self.toggle_chat_sidebar)

        # Floating window shortcut
        floating_shortcut = QShortcut(QKeySequence("Ctrl+Alt+A"), self)
        floating_shortcut.activated.connect(self.show_chat_floating)

    def toggle_chat_sidebar(self):
        """Toggle chat assistant sidebar"""
        try:
            self.chat_sidebar.toggle_visibility()
            status = "shown" if self.chat_sidebar.is_visible else "hidden"
            self.update_status(f"Chat sidebar {status}", "info")
        except Exception as e:
            logger.error(f"Error toggling chat sidebar: {e}")
            self.update_status("Failed to toggle chat sidebar", "error")

    def show_chat_floating(self):
        """Show chat assistant floating window"""
        try:
            if self.chat_floating.isVisible():
                self.chat_floating.raise_()
                self.chat_floating.activateWindow()
            else:
                self.chat_floating.show()
                # Position near main window
                main_rect = self.geometry()
                self.chat_floating.move(
                    main_rect.x() + main_rect.width() + 10,
                    main_rect.y()
                )

            self.update_status("Chat assistant window opened", "info")
        except Exception as e:
            logger.error(f"Error showing chat floating window: {e}")
            self.update_status("Failed to show chat window", "error")

    def resizeEvent(self, event):
        """Handle window resize to reposition sidebar"""
        super().resizeEvent(event)

        # Reposition sidebar if visible
        if hasattr(self, 'chat_sidebar') and self.chat_sidebar.is_visible:
            self.chat_sidebar.setGeometry(
                self.width() - self.chat_sidebar.width(),
                0,
                self.chat_sidebar.width(),
                self.height()
            )

    def closeEvent(self, event):
        """Handle window close event"""
        if self._check_unsaved_changes():
            event.ignore()
            return

        # Save window state
        # TODO: Implement settings persistence

        event.accept()
        logger.info("Main window closed")

    def _update_service_menu_states(self):
        """Update service menu states based on authentication status"""
        try:
            # Update X authentication menu
            if self.x_authenticator and hasattr(self.x_authenticator, 'is_authenticated') and self.x_authenticator.is_authenticated():
                self.x_login_action.setText('🚪 Logout from X')
            else:
                self.x_login_action.setText('🔑 Login to X')

            # Update Google Drive authentication menu
            if self.backup_manager and hasattr(self.backup_manager, 'is_authenticated') and self.backup_manager.is_authenticated():
                self.drive_login_action.setText('🚪 Logout from Google Drive')
            else:
                self.drive_login_action.setText('🔑 Login to Google Drive')

        except Exception as e:
            logger.error(f"Error updating service menu states: {e}")

    def toggle_x_authentication(self):
        """Toggle X authentication"""
        try:
            if self.x_authenticator and self.x_authenticator.is_authenticated():
                # Logout
                self.x_authenticator.logout()
                self.update_status("Logged out from X", "info")
            else:
                # Login directly
                if not self.x_authenticator:
                    QMessageBox.warning(self, "X Authentication",
                                      "X authenticator not available. Please configure X first.")
                    return

                self.update_status("Starting X authentication...", "info")
                success = self._perform_x_authentication()
                if success:
                    self.update_status("Successfully logged into X", "success")
                else:
                    self.update_status("X authentication failed", "error")

            self._update_service_menu_states()

        except Exception as e:
            logger.error(f"Error toggling X authentication: {e}")
            QMessageBox.critical(self, "Error", f"Authentication error: {e}")

    def toggle_drive_authentication(self):
        """Toggle Google Drive authentication"""
        try:
            if self.backup_manager and hasattr(self.backup_manager, 'is_authenticated') and self.backup_manager.is_authenticated():
                # Logout
                self.backup_manager.logout()
                self.update_status("Logged out from Google Drive", "info")
            else:
                # Login directly
                self.update_status("Starting Google Drive authentication...", "info")
                success = self._perform_drive_authentication()
                if success:
                    self.update_status("Successfully logged into Google Drive", "success")
                else:
                    self.update_status("Google Drive authentication failed", "error")

            self._update_service_menu_states()

        except Exception as e:
            logger.error(f"Error toggling Google Drive authentication: {e}")
            QMessageBox.critical(self, "Error", f"Authentication error: {e}")

    def show_backup_options(self):
        """Show backup options dialog"""
        try:
            if not self.current_story:
                QMessageBox.information(self, "Backup", "Please load a story first")
                return

            if not self.backup_manager or not self.backup_manager.is_authenticated():
                QMessageBox.warning(self, "Backup", "Please login to Google Drive first")
                return

            from .backup_dialog import BackupOptionsDialog
            dialog = BackupOptionsDialog(self.current_story, self.backup_manager, self)
            dialog.exec()

        except Exception as e:
            logger.error(f"Error showing backup options: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show backup options: {e}")

    def show_restore_options(self):
        """Show restore options dialog"""
        try:
            if not self.backup_manager or not self.backup_manager.is_authenticated():
                QMessageBox.warning(self, "Restore", "Please login to Google Drive first")
                return

            from .restore_dialog import RestoreOptionsDialog
            dialog = RestoreOptionsDialog(self.backup_manager, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                restored_story = dialog.get_restored_story()
                if restored_story:
                    self.current_story = restored_story
                    self.current_file_path = None
                    self.story_loaded.emit(self.current_story)
                    self.update_status("Story restored from backup", "success")

        except Exception as e:
            logger.error(f"Error showing restore options: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show restore options: {e}")

    def export_story_data(self):
        """Export story data to file"""
        try:
            if not self.current_story:
                QMessageBox.information(self, "Export", "Please load a story first")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Story Data", "story_export.json", "JSON files (*.json)"
            )

            if file_path:
                if self.current_story.save_to_file(file_path):
                    self.update_status(f"Story exported to {Path(file_path).name}", "success")
                else:
                    QMessageBox.critical(self, "Error", "Failed to export story")

        except Exception as e:
            logger.error(f"Error exporting story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export story: {e}")

    def import_story_data(self):
        """Import story data from file"""
        try:
            if self.current_story and self._check_unsaved_changes():
                return

            file_path, _ = QFileDialog.getOpenFileName(
                self, "Import Story Data", "", "JSON files (*.json)"
            )

            if file_path:
                story = StoryWeb.load_from_file(file_path)
                if story:
                    self.current_story = story
                    self.current_file_path = None
                    self.story_loaded.emit(self.current_story)
                    self.update_status(f"Story imported from {Path(file_path).name}", "success")
                else:
                    QMessageBox.critical(self, "Error", "Failed to import story")

        except Exception as e:
            logger.error(f"Error importing story: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import story: {e}")

    def _perform_x_authentication(self) -> bool:
        """Perform X authentication process - simplified approach"""
        try:
            # Show explanation dialog first
            reply = QMessageBox.question(
                self,
                "🐦 X Authentication",
                """<h3>X (Twitter) Integration</h3>

                <p>Unfortunately, X/Twitter requires developer API credentials for automated posting, which involves:</p>

                <ul>
                <li>Creating a developer account at developer.twitter.com</li>
                <li>Getting approved for API access (can take days/weeks)</li>
                <li>Setting up OAuth credentials</li>
                <li>Monthly API fees for higher usage</li>
                </ul>

                <p><b>Alternative options:</b></p>
                <ul>
                <li><b>Manual posting:</b> The app can generate posts for you to copy/paste</li>
                <li><b>Browser automation:</b> Use browser automation (experimental)</li>
                <li><b>Third-party services:</b> Use Zapier, IFTTT, or Buffer integration</li>
                </ul>

                <p>Would you like to try the <b>browser automation</b> approach instead?<br>
                <small>(This will open X in your browser and try to automate posting)</small></p>
                """,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                return self._try_browser_automation()
            else:
                # Show manual posting option
                self._show_manual_posting_option()
                return False

        except Exception as e:
            logger.error(f"X authentication failed: {e}")
            QMessageBox.critical(self, "Authentication Error", f"X authentication failed: {e}")
            return False

    def _try_browser_automation(self) -> bool:
        """Try browser automation approach for X posting"""
        try:
            QMessageBox.information(
                self,
                "🤖 Browser Automation",
                """<h3>Browser Automation Setup</h3>

                <p>This experimental feature will:</p>
                <ul>
                <li>Open X (Twitter) in your default browser</li>
                <li>Try to automate posting using browser automation</li>
                <li>Require you to be logged into X in your browser</li>
                </ul>

                <p><b>Note:</b> This is experimental and may not work reliably due to X's anti-automation measures.</p>

                <p>For now, this will open X and show you the generated post content to copy/paste manually.</p>
                """
            )

            # For now, just open X and show manual posting
            from PyQt6.QtGui import QDesktopServices
            from PyQt6.QtCore import QUrl

            QDesktopServices.openUrl(QUrl("https://twitter.com/compose/tweet"))
            self._show_manual_posting_option()

            return True

        except Exception as e:
            logger.error(f"Browser automation failed: {e}")
            QMessageBox.critical(self, "Browser Automation Error", f"Browser automation failed: {e}")
            return False

    def _show_manual_posting_option(self):
        """Show manual posting option with generated content"""
        try:
            # Create a dialog for manual posting
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle("📝 Manual X Posting")
            dialog.resize(500, 400)

            layout = QVBoxLayout(dialog)

            # Header
            header = QLabel("📝 Manual X (Twitter) Posting")
            header.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
            layout.addWidget(header)

            # Instructions
            instructions = QLabel("""
            <p>Since automated posting requires API credentials, you can use this manual approach:</p>
            <ol>
            <li>The app will generate post content for your story nodes</li>
            <li>Copy the generated text below</li>
            <li>Paste it into X (Twitter) manually</li>
            <li>Add any images or videos as needed</li>
            </ol>
            """)
            instructions.setWordWrap(True)
            layout.addWidget(instructions)

            # Generated content area
            content_label = QLabel("Generated Post Content:")
            content_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
            layout.addWidget(content_label)

            self.post_content_edit = QTextEdit()
            self.post_content_edit.setPlainText("""🎮 New interactive story available!

What path will you choose in this thrilling adventure? Every decision matters!

#CYOA #InteractiveStory #Gaming #ChooseYourAdventure

[This is sample content - connect a story to generate real posts]""")
            layout.addWidget(self.post_content_edit)

            # Buttons
            button_layout = QVBoxLayout()

            copy_btn = QPushButton("📋 Copy to Clipboard")
            copy_btn.clicked.connect(self._copy_post_content)
            button_layout.addWidget(copy_btn)

            open_x_btn = QPushButton("🐦 Open X (Twitter)")
            def open_twitter():
                from PyQt6.QtGui import QDesktopServices
                from PyQt6.QtCore import QUrl
                QDesktopServices.openUrl(QUrl("https://twitter.com/compose/tweet"))
            open_x_btn.clicked.connect(open_twitter)
            button_layout.addWidget(open_x_btn)

            close_btn = QPushButton("✅ Done")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            logger.error(f"Error showing manual posting option: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show manual posting option: {e}")

    def _copy_post_content(self):
        """Copy post content to clipboard"""
        try:
            from PyQt6.QtWidgets import QApplication

            clipboard = QApplication.clipboard()
            clipboard.setText(self.post_content_edit.toPlainText())

            self.update_status("Post content copied to clipboard", "success")

        except Exception as e:
            logger.error(f"Error copying to clipboard: {e}")

    def show_settings(self):
        """Show settings window"""
        try:
            if self.settings_window is None:
                from .settings_tab import SettingsTab
                self.settings_window = SettingsTab(self.config, self.lmstudio_client, getattr(self, 'comfyui_client', None))
                self.settings_window.setWindowTitle("⚙️ Settings")
                self.settings_window.setWindowFlags(self.settings_window.windowFlags() | Qt.WindowType.Window)
                self.settings_window.resize(800, 600)

            self.settings_window.show()
            self.settings_window.raise_()
            self.settings_window.activateWindow()

        except Exception as e:
            logger.error(f"Error showing settings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show settings: {e}")

    def show_logs(self):
        """Show logs window"""
        try:
            if self.logs_window is None:
                from .logs_tab import LogsTab
                self.logs_window = LogsTab(self.config)
                self.logs_window.setWindowTitle("📋 Application Logs")
                self.logs_window.setWindowFlags(self.logs_window.windowFlags() | Qt.WindowType.Window)
                self.logs_window.resize(1000, 700)

            self.logs_window.show()
            self.logs_window.raise_()
            self.logs_window.activateWindow()

        except Exception as e:
            logger.error(f"Error showing logs: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show logs: {e}")

    def show_help(self):
        """Show help window"""
        try:
            if self.help_window is None:
                from .help_system import HelpTab
                self.help_window = HelpTab()
                self.help_window.setWindowTitle("📖 Help & Documentation")
                self.help_window.setWindowFlags(self.help_window.windowFlags() | Qt.WindowType.Window)
                self.help_window.resize(900, 650)

            self.help_window.show()
            self.help_window.raise_()
            self.help_window.activateWindow()

        except Exception as e:
            logger.error(f"Error showing help: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show help: {e}")

    def _perform_drive_authentication(self) -> bool:
        """Perform Google Drive authentication process in background thread"""
        try:
            # Initialize backup manager if not available
            if not self.backup_manager:
                try:
                    from backup.google_drive_backup import BackupManager
                    self.backup_manager = BackupManager(self.config)
                except Exception as e:
                    logger.error(f"Failed to initialize backup manager: {e}")
                    QMessageBox.critical(
                        self,
                        "Google Drive Error",
                        "Google Drive backup system not available. Please check your configuration."
                    )
                    return False

            # Create and start authentication worker
            from .auth_worker import DriveAuthWorker
            self.drive_auth_worker = DriveAuthWorker(self.backup_manager)
            self.drive_auth_worker.auth_completed.connect(self._on_drive_auth_completed)
            self.drive_auth_worker.auth_failed.connect(self._on_drive_auth_failed)
            self.drive_auth_worker.auth_progress.connect(self._on_drive_auth_progress)

            # Show progress dialog
            self.drive_auth_dialog = QMessageBox(self)
            self.drive_auth_dialog.setWindowTitle("☁️ Google Drive Authentication")
            self.drive_auth_dialog.setText("🚀 Starting Google Drive authentication...")
            self.drive_auth_dialog.setStandardButtons(QMessageBox.StandardButton.Cancel)
            self.drive_auth_dialog.setModal(True)

            # Handle cancel
            def on_cancel():
                if hasattr(self, 'drive_auth_worker'):
                    self.drive_auth_worker.cancel()
                self.drive_auth_dialog.close()

            self.drive_auth_dialog.rejected.connect(on_cancel)

            # Start worker and show dialog
            self.drive_auth_worker.start()
            self.drive_auth_dialog.show()

            return True  # Will be handled by callbacks

        except Exception as e:
            logger.error(f"Google Drive authentication failed: {e}")
            QMessageBox.critical(self, "Authentication Error", f"Google Drive authentication failed: {e}")
            return False

    def show_x_config(self):
        """Show X configuration dialog"""
        try:
            from .x_config_dialog import XConfigDialog
            dialog = XConfigDialog(self.config, self.x_authenticator, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Reinitialize authenticator with new config
                try:
                    from social.x_auth import XAuthenticator
                    self.x_authenticator = XAuthenticator(self.config)
                    self.update_status("X configuration updated", "success")
                except Exception as e:
                    logger.error(f"Failed to reinitialize X authenticator: {e}")
                    self.update_status("Failed to update X configuration", "error")

        except ImportError:
            # Create a simple config dialog if the dedicated one doesn't exist
            self._show_simple_x_config()
        except Exception as e:
            logger.error(f"Error showing X config: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show X configuration: {e}")

    def show_drive_config(self):
        """Show Google Drive configuration dialog"""
        try:
            from .drive_config_dialog import DriveConfigDialog
            dialog = DriveConfigDialog(self.config, self.backup_manager, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Reinitialize backup manager with new config
                try:
                    from backup.google_drive_backup import BackupManager
                    self.backup_manager = BackupManager(self.config)
                    self.update_status("Google Drive configuration updated", "success")
                except Exception as e:
                    logger.error(f"Failed to reinitialize backup manager: {e}")
                    self.update_status("Failed to update Google Drive configuration", "error")

        except ImportError:
            # Create a simple config dialog if the dedicated one doesn't exist
            self._show_simple_drive_config()
        except Exception as e:
            logger.error(f"Error showing Google Drive config: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show Google Drive configuration: {e}")

    def _show_simple_x_config(self):
        """Show simple X configuration dialog"""
        QMessageBox.information(
            self,
            "X Configuration",
            """
            <h3>🐦 X (Twitter) Configuration</h3>
            <p>The X configuration dialog will open to set up your API credentials.</p>
            <p>You can configure:</p>
            <ul>
            <li><b>API Credentials</b> - Client ID and Secret</li>
            <li><b>Posting Settings</b> - Templates and hashtags</li>
            <li><b>Rate Limits</b> - Based on your subscription tier</li>
            </ul>
            """
        )

    def _show_simple_drive_config(self):
        """Show simple Google Drive configuration dialog"""
        QMessageBox.information(
            self,
            "Google Drive Configuration",
            """
            <h3>☁️ Google Drive Configuration</h3>
            <p>The Google Drive configuration dialog will open to set up your backup settings.</p>
            <p>You can configure:</p>
            <ul>
            <li><b>API Credentials</b> - OAuth settings</li>
            <li><b>Backup Options</b> - What to backup by default</li>
            <li><b>Storage Settings</b> - Folder structure and limits</li>
            </ul>
            """
        )

    def _on_x_auth_progress(self, message: str):
        """Handle X authentication progress updates"""
        if hasattr(self, 'x_auth_dialog'):
            self.x_auth_dialog.setText(message)
        self.update_status(f"X Auth: {message}", "info")

    def _on_x_auth_completed(self, user_info: dict):
        """Handle X authentication completion"""
        try:
            if hasattr(self, 'x_auth_dialog'):
                self.x_auth_dialog.close()

            username = user_info.get('username', 'Unknown')
            self.update_status(f"Successfully logged into X as @{username}", "success")
            self._update_service_menu_states()

            QMessageBox.information(
                self,
                "X Authentication Successful",
                f"✅ Successfully logged into X!\n\nWelcome @{username}!"
            )

        except Exception as e:
            logger.error(f"Error handling X auth completion: {e}")

    def _on_x_auth_failed(self, error_message: str):
        """Handle X authentication failure"""
        try:
            if hasattr(self, 'x_auth_dialog'):
                self.x_auth_dialog.close()

            self.update_status(f"X authentication failed: {error_message}", "error")
            self._update_service_menu_states()

            QMessageBox.critical(
                self,
                "X Authentication Failed",
                f"❌ X authentication failed:\n\n{error_message}\n\nPlease try again or check your configuration."
            )

        except Exception as e:
            logger.error(f"Error handling X auth failure: {e}")

    def _on_drive_auth_progress(self, message: str):
        """Handle Google Drive authentication progress updates"""
        if hasattr(self, 'drive_auth_dialog'):
            self.drive_auth_dialog.setText(message)
        self.update_status(f"Drive Auth: {message}", "info")

    def _on_drive_auth_completed(self, user_info: dict):
        """Handle Google Drive authentication completion"""
        try:
            if hasattr(self, 'drive_auth_dialog'):
                self.drive_auth_dialog.close()

            name = user_info.get('name', 'Unknown')
            email = user_info.get('email', 'Unknown')
            self.update_status(f"Successfully logged into Google Drive as {name}", "success")
            self._update_service_menu_states()

            QMessageBox.information(
                self,
                "Google Drive Authentication Successful",
                f"✅ Successfully logged into Google Drive!\n\nWelcome {name} ({email})!"
            )

        except Exception as e:
            logger.error(f"Error handling Google Drive auth completion: {e}")

    def _on_drive_auth_failed(self, error_message: str):
        """Handle Google Drive authentication failure"""
        try:
            if hasattr(self, 'drive_auth_dialog'):
                self.drive_auth_dialog.close()

            self.update_status(f"Google Drive authentication failed: {error_message}", "error")
            self._update_service_menu_states()

            QMessageBox.critical(
                self,
                "Google Drive Authentication Failed",
                f"❌ Google Drive authentication failed:\n\n{error_message}\n\nPlease try again or check your configuration."
            )

        except Exception as e:
            logger.error(f"Error handling Google Drive auth failure: {e}")

    def show_story_details(self):
        """Show story details dialog"""
        try:
            if not self.current_story:
                QMessageBox.information(
                    self,
                    "No Story Loaded",
                    "Please load or create a story first to view its details."
                )
                return

            # Prepare story data for the dialog
            story_data = {
                'title': self.current_story.metadata.get('title', 'Untitled Story'),
                'author': self.current_story.metadata.get('author', ''),
                'description': self.current_story.metadata.get('description', ''),
                'genre': self.current_story.metadata.get('genre', ''),
                'story_type': self.current_story.metadata.get('story_type', 'Story Web'),
                'difficulty': self.current_story.metadata.get('difficulty', 'Intermediate'),
                'target_age': self.current_story.metadata.get('target_age', 'All Ages'),
                'content_rating': self.current_story.metadata.get('content_rating', 'G - General'),
                'tags': self.current_story.metadata.get('tags', ''),
                'version': self.current_story.metadata.get('version', '1.0'),
                'created_date': self.current_story.metadata.get('created_at', 'Not set'),
                'modified_date': self.current_story.metadata.get('modified_at', 'Not set'),
                'visibility': self.current_story.metadata.get('visibility', 'Private'),
                'allow_comments': self.current_story.metadata.get('allow_comments', True),
                'allow_sharing': self.current_story.metadata.get('allow_sharing', True),
                'auto_descriptions': self.current_story.metadata.get('auto_descriptions', False),
                'auto_images': self.current_story.metadata.get('auto_images', False),
                'auto_audio': self.current_story.metadata.get('auto_audio', False),
                'export_media': self.current_story.metadata.get('export_media', True),
                'compress_exports': self.current_story.metadata.get('compress_exports', True),
                'image_path': self.current_story.metadata.get('image_path', ''),
                'nodes': {node_id: {
                    'content': node.text,
                    'choices': [choice.text for choice in node.choices],
                    'is_start': node.is_entry
                } for node_id, node in self.current_story.nodes.items()}
            }

            from .story_details_dialog import StoryDetailsDialog
            dialog = StoryDetailsDialog(story_data, self)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Update story metadata with changes
                self.current_story.metadata.update({
                    'title': story_data['title'],
                    'author': story_data['author'],
                    'description': story_data['description'],
                    'genre': story_data['genre'],
                    'story_type': story_data['story_type'],
                    'difficulty': story_data['difficulty'],
                    'target_age': story_data['target_age'],
                    'content_rating': story_data['content_rating'],
                    'tags': story_data['tags'],
                    'version': story_data['version'],
                    'visibility': story_data['visibility'],
                    'allow_comments': story_data['allow_comments'],
                    'allow_sharing': story_data['allow_sharing'],
                    'auto_descriptions': story_data['auto_descriptions'],
                    'auto_images': story_data['auto_images'],
                    'auto_audio': story_data['auto_audio'],
                    'export_media': story_data['export_media'],
                    'compress_exports': story_data['compress_exports'],
                    'image_path': story_data['image_path'],
                    'modified_at': story_data['modified_date']
                })

                self.update_status("Story details updated", "success")

                # Refresh story display
                self.story_loaded.emit(self.current_story)

        except Exception as e:
            logger.error(f"Error showing story details: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show story details: {e}")
