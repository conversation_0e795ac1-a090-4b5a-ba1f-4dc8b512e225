"""
Help System - Comprehensive User Guide and Documentation
Provides in-app help, tutorials, and context-sensitive assistance
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QTreeWidget, 
    QTreeWidgetItem, QSplitter, QGroupBox, QLabel, QPushButton,
    QLineEdit, QTabWidget, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

logger = logging.getLogger(__name__)


class HelpContent:
    """Help content data structure"""
    
    HELP_SECTIONS = {
        "getting_started": {
            "title": "🚀 Getting Started",
            "content": """
# Getting Started with CYOA Automation System

Welcome to the Choose Your Own Adventure Automation System! This guide will help you get started creating engaging interactive stories for X (Twitter).

## Quick Start Steps

1. **Create Your First Story**
   - Go to File → New Story Wizard
   - Choose a template or start from scratch
   - Follow the guided setup process

2. **Add Story Nodes**
   - Use the Story Editor tab to create nodes
   - Write engaging content for each scene
   - Add choices that lead to other nodes

3. **Set Up Characters**
   - Go to the Characters tab
   - Create character profiles with voices
   - Assign characters to story nodes

4. **Generate Media**
   - Use the Voice Studio for text-to-speech
   - Generate videos in the X Manager tab
   - Preview your content before posting

5. **Post to X**
   - Configure your X API credentials
   - Use the X Manager to schedule posts
   - Track engagement in Analytics

## Key Features

- **AI-Powered Story Generation**: Use LM Studio for creative assistance
- **Voice Cloning**: Create unique character voices
- **Analytics Tracking**: Monitor story performance
- **Automated Posting**: Schedule X posts with videos
- **Character Management**: Develop consistent personalities
"""
        },
        
        "story_editor": {
            "title": "📝 Story Editor",
            "content": """
# Story Editor Guide

The Story Editor is your main workspace for creating and editing interactive stories.

## Interface Overview

### Node Tree (Left Panel)
- Shows all story nodes in a hierarchical view
- Entry points are marked with 🚪
- Endings are marked with 🏁
- Premium content has a 💎 icon

### Node Editor (Right Panel)
- **Node ID**: Unique identifier for the node
- **Text Content**: The main story text
- **Node Type**: Story, choice, ending, etc.
- **Choices**: Links to other nodes
- **Properties**: Rating, premium status, scoring

## Creating Nodes

1. **Add New Node**
   - Click "Add Node" button
   - Enter unique node ID
   - Write compelling content

2. **Add Choices**
   - Click "Add Choice" in the choices section
   - Write choice text (keep under 100 characters)
   - Select target node from dropdown

3. **Set Properties**
   - Mark entry points for story beginnings
   - Set content rating (Safe, Spicy, Premium)
   - Add scoring for game mechanics

## Best Practices

- **Keep nodes focused**: Each node should advance the story
- **Meaningful choices**: Give players real agency
- **Consistent tone**: Maintain voice throughout
- **Test paths**: Ensure all routes work properly
- **Character consistency**: Use the same voice for characters

## Keyboard Shortcuts

- **Ctrl+N**: New node
- **Ctrl+S**: Save story
- **Ctrl+D**: Duplicate node
- **Delete**: Remove selected node
- **F5**: Refresh tree view
"""
        },
        
        "characters": {
            "title": "👥 Character Management",
            "content": """
# Character Management Guide

Create and manage compelling characters for your stories.

## Character Profiles

### Basic Information
- **Name**: Character's display name
- **Role**: Protagonist, antagonist, neutral, etc.
- **Importance**: 1-5 scale for story significance
- **Description**: Character background and traits

### Voice Configuration
- **Voice Samples**: Upload audio files for cloning
- **TTS Settings**: Rate, pitch, emotion settings
- **Character Voice**: Assign unique voice profile

## Creating Characters

1. **Add Character**
   - Click "Add Character" button
   - Fill in basic information
   - Set importance level

2. **Record Voice Sample**
   - Use high-quality microphone
   - Record 30-60 seconds of clear speech
   - Include emotional range
   - Save as WAV or MP3

3. **Configure Voice**
   - Go to Voice Studio tab
   - Create voice profile for character
   - Test with sample text
   - Adjust settings as needed

## Character Consistency

- **Personality Traits**: Define clear characteristics
- **Speech Patterns**: Unique vocabulary and style
- **Emotional Range**: How they react to situations
- **Relationships**: How they interact with others

## Voice Cloning Tips

- **Quality Audio**: Use noise-free recordings
- **Consistent Tone**: Record in same environment
- **Emotional Variety**: Include different emotions
- **Clear Speech**: Enunciate clearly
- **Sufficient Length**: 30+ seconds minimum
"""
        },
        
        "x_manager": {
            "title": "📱 X (Twitter) Integration",
            "content": """
# X Manager Guide

Automate your story posting and engagement on X (Twitter).

## Setup

### API Configuration
1. **Get X API Credentials**
   - Visit developer.twitter.com
   - Create a new app
   - Generate API keys and tokens

2. **Configure in App**
   - Go to Authentication tab
   - Enter your API credentials
   - Test connection

### Account Tiers
- **Free**: Basic posting, limited features
- **Premium**: Enhanced analytics, longer videos
- **Spicy**: Adult content, special handling

## Posting Stories

### Content Preparation
- **Node Selection**: Choose which nodes to post
- **Video Generation**: Create engaging visuals
- **Choice Links**: Set up navigation between posts
- **Scheduling**: Plan posting timeline

### Post Types
- **Story Posts**: Main narrative content
- **Choice Posts**: Interactive decision points
- **Character Posts**: Character introductions
- **Analytics Posts**: Engagement summaries

## Video Features

### Video Ads (Premium)
- **Clickable Choices**: Use video ads for navigation
- **Enhanced Engagement**: Higher interaction rates
- **Analytics Tracking**: Detailed performance data

### Standard Videos
- **Text Overlay**: Story content on visuals
- **Character Voices**: TTS narration
- **Background Music**: Mood enhancement
- **Transitions**: Smooth scene changes

## Best Practices

- **Consistent Posting**: Regular schedule builds audience
- **Engage Responses**: Reply to comments and questions
- **Use Analytics**: Track what content performs best
- **Test Timing**: Find optimal posting times
- **Cross-Promote**: Link between story parts
"""
        },
        
        "voice_studio": {
            "title": "🎤 Voice Studio",
            "content": """
# Voice Studio Guide

Create and manage text-to-speech voices for your characters.

## Voice Profiles

### Creating Profiles
1. **Basic Profile**
   - Name and description
   - Character assignment
   - Engine selection (pyttsx3, custom)

2. **Voice Settings**
   - **Rate**: Speaking speed (words per minute)
   - **Volume**: Audio level (0.0 - 1.0)
   - **Pitch**: Voice pitch adjustment
   - **Emotion**: Neutral, happy, sad, etc.

### Voice Cloning
1. **Record Samples**
   - Use Voice Studio recording interface
   - Record multiple emotional states
   - Ensure high audio quality

2. **Train Model** (Advanced)
   - Upload samples to voice cloning service
   - Wait for model training completion
   - Test with sample text

## Recording Tips

### Equipment
- **Microphone**: Use quality USB or XLR mic
- **Environment**: Quiet room with minimal echo
- **Position**: 6-8 inches from microphone
- **Levels**: Avoid clipping, maintain consistent volume

### Technique
- **Clear Speech**: Enunciate clearly
- **Natural Pace**: Don't rush or drag
- **Emotional Range**: Include various emotions
- **Consistency**: Same tone throughout session

## Usage in Stories

### Character Assignment
- Assign voice profiles to specific characters
- Use consistent voices across story nodes
- Test voice quality with story content

### Narration
- Create narrator voice for story text
- Use different voice for character dialogue
- Adjust settings for mood and tone

## Troubleshooting

### Common Issues
- **Robotic Sound**: Adjust pitch and rate
- **Unclear Speech**: Check pronunciation settings
- **Volume Issues**: Normalize audio levels
- **Sync Problems**: Check audio device settings
"""
        },
        
        "analytics": {
            "title": "📊 Analytics & Optimization",
            "content": """
# Analytics Guide

Track and optimize your story performance.

## Metrics Overview

### Engagement Metrics
- **Views**: Total story node views
- **Interactions**: Likes, retweets, replies
- **Choice Clicks**: Decision point engagement
- **Completion Rate**: Users who finish stories

### Path Analysis
- **Popular Routes**: Most taken story paths
- **Drop-off Points**: Where users stop reading
- **Choice Distribution**: Which options are chosen
- **Ending Reach**: How many reach each ending

## Using Analytics

### Performance Tracking
1. **Monitor Regularly**: Check metrics daily
2. **Identify Trends**: Look for patterns over time
3. **Compare Stories**: See what works best
4. **A/B Testing**: Test different approaches

### Optimization Strategies
- **Improve Weak Nodes**: Rewrite low-performing content
- **Enhance Popular Paths**: Expand successful routes
- **Fix Drop-offs**: Address points where users leave
- **Optimize Timing**: Post when audience is active

## A/B Testing

### Test Elements
- **Story Openings**: Different hooks
- **Choice Wording**: Various option texts
- **Character Voices**: Different TTS settings
- **Posting Times**: Optimal scheduling

### Running Tests
1. **Create Variants**: Develop alternative versions
2. **Split Traffic**: Divide audience randomly
3. **Measure Results**: Compare performance metrics
4. **Implement Winners**: Use best-performing version

## Reports

### Automated Reports
- **Daily Summaries**: Key metrics overview
- **Weekly Analysis**: Trend identification
- **Monthly Reviews**: Comprehensive performance
- **Custom Reports**: Specific metric focus

### Export Options
- **CSV Data**: Raw metrics for analysis
- **PDF Reports**: Formatted summaries
- **Charts**: Visual performance data
- **API Access**: Programmatic data retrieval
"""
        },
        
        "authentication": {
            "title": "🔐 Authentication & Services",
            "content": """
# Authentication & Services Guide

Manage authentication with external services for enhanced functionality.

## Services Menu

Access all authentication features through the **Services** menu in the top menu bar.

### X (Twitter) Authentication
- **Access**: Services → Login to X
- **Flow**: Automatic OAuth 2.0 authentication
- **Features**: No manual code copying required
- **Credentials**: Automatically saved and persisted
- **Logout**: Services → Logout from X (when authenticated)

### Google Drive Authentication
- **Access**: Services → Login to Google Drive
- **Purpose**: Required for backup and restore functionality
- **Flow**: Automatic OAuth 2.0 authentication
- **Credentials**: Automatically saved and persisted
- **Logout**: Services → Logout from Google Drive (when authenticated)

## Authentication Process

### Automatic OAuth Flow
1. **Click Login**: Select service from Services menu
2. **Browser Opens**: Authorization page loads automatically
3. **Authorize**: Grant permissions in browser
4. **Complete**: Authentication completes automatically
5. **No Manual Steps**: No code copying required

### Security Features
- **OAuth 2.0**: Industry-standard secure authentication
- **Local Storage**: Credentials stored locally, never transmitted
- **Automatic Refresh**: Tokens refreshed automatically
- **State Validation**: CSRF protection for all flows
- **Secure Callbacks**: Localhost-only callback servers

## Setup Requirements

### X (Twitter) API Setup
1. **Developer Account**: Create at developer.twitter.com
2. **Create App**: Set up OAuth 2.0 application
3. **Callback URL**: Set to http://localhost:8080/callback
4. **Scopes**: tweet.read, tweet.write, users.read, offline.access
5. **App Type**: Web app with PKCE enabled

### Google Drive API Setup
1. **Google Cloud Console**: Create project at console.cloud.google.com
2. **Enable APIs**: Enable Google Drive API
3. **Create Credentials**: OAuth 2.0 client ID for desktop app
4. **Callback URL**: Set to http://localhost:8081/callback
5. **Scopes**: Drive file access, app data folder, user profile

## Troubleshooting Authentication

### Common Issues
- **Browser Doesn't Open**: Check default browser settings
- **Callback Timeout**: Ensure ports 8080/8081 are available
- **Permission Denied**: Verify API credentials and scopes
- **Token Expired**: Logout and login again to refresh

### Error Messages
- **"Authentication failed"**: Check API credentials
- **"Callback server error"**: Restart application
- **"Invalid state parameter"**: Clear saved credentials and retry
- **"Network error"**: Check internet connection
"""
        },

        "backup_restore": {
            "title": "☁️ Backup & Restore",
            "content": """
# Backup & Restore Guide

Safely backup your stories to Google Drive and restore them when needed.

## Backup Menu

Access backup features through the **Backup** menu in the top menu bar.

### Available Options
- **📤 Backup to Google Drive**: Create backup of current story
- **📥 Restore from Google Drive**: Restore story from backup
- **📁 Export Story Data**: Export to local file
- **📁 Import Story Data**: Import from local file

## Creating Backups

### Prerequisites
1. **Google Drive Authentication**: Login through Services menu
2. **Story Loaded**: Have a story open in the application
3. **Internet Connection**: Required for Google Drive access

### Backup Process
1. **Open Dialog**: Backup → Backup to Google Drive
2. **Select Options**: Choose what to include in backup
3. **Configure Settings**: Set backup name and options
4. **Start Backup**: Click "Start Backup" button
5. **Monitor Progress**: Watch progress bar and status
6. **Completion**: Receive confirmation with backup ID

### Backup Options

#### What to Include
- **📝 Story Data**: Nodes, connections, metadata (recommended)
- **👥 Characters**: Profiles, personalities, voices (recommended)
- **🎒 Items**: Inventory system and item definitions (recommended)
- **🎬 Media Files**: Images, audio, videos (optional - large files)
- **🤖 AI Settings**: Prompts, voice profiles (recommended)
- **⚙️ App Settings**: Application preferences (optional)

#### Advanced Settings
- **Compression**: Reduce backup file size (recommended)
- **Max Versions**: Number of backup versions to keep (default: 5)
- **Custom Name**: Override default backup naming

## Restoring Backups

### Restore Process
1. **Open Dialog**: Backup → Restore from Google Drive
2. **Browse Backups**: View available backups in list
3. **Select Backup**: Click on backup to see details
4. **Preview Contents**: Review backup information
5. **Confirm Restore**: Click "Restore Selected"
6. **Monitor Progress**: Watch download and extraction
7. **Completion**: Story loaded automatically

### Backup Management
- **🔄 Refresh List**: Update available backups
- **📄 View Details**: See backup contents and metadata
- **🗑️ Delete Backup**: Remove unwanted backups
- **📊 Size Information**: Check backup file sizes

## Local Export/Import

### Export Story Data
1. **Select Story**: Ensure story is loaded
2. **Export**: Backup → Export Story Data
3. **Choose Location**: Select save location
4. **File Format**: JSON format with all story data

### Import Story Data
1. **Import**: Backup → Import Story Data
2. **Select File**: Choose JSON file to import
3. **Load Story**: Story loaded automatically
4. **Verify**: Check that all data imported correctly

## Best Practices

### Regular Backups
- **Before Major Changes**: Backup before significant edits
- **Daily Backups**: For active story development
- **Version Control**: Keep multiple backup versions
- **Test Restores**: Periodically test restore process

### Backup Strategy
- **Cloud + Local**: Use both Google Drive and local exports
- **Multiple Versions**: Keep several backup versions
- **Descriptive Names**: Use clear backup naming
- **Regular Cleanup**: Remove old unnecessary backups

### Security Considerations
- **Private Stories**: Backups stored in your Google Drive
- **Credential Safety**: Never share backup files with credentials
- **Access Control**: Only you can access your backups
- **Encryption**: Backups are encrypted in transit

## Troubleshooting

### Common Issues
- **"Not authenticated"**: Login to Google Drive first
- **"Upload failed"**: Check internet connection
- **"Backup not found"**: Refresh backup list
- **"Restore failed"**: Verify backup integrity

### Error Recovery
- **Partial Backup**: Try again with fewer options selected
- **Network Issues**: Check connection and retry
- **Storage Full**: Clean up old backups in Google Drive
- **Corrupted Backup**: Use alternative backup version
"""
        },

        "troubleshooting": {
            "title": "🔧 Troubleshooting",
            "content": """
# Troubleshooting Guide

Common issues and solutions.

## Installation Issues

### LM Studio Connection
- **Check Status**: Verify LM Studio is running
- **Port Configuration**: Ensure port 1234 is available
- **Model Loading**: Load a compatible model
- **Firewall**: Check firewall settings

### Audio Problems
- **Microphone Access**: Grant microphone permissions
- **Audio Drivers**: Update audio drivers
- **Device Selection**: Choose correct input device
- **Sample Rate**: Use 44.1kHz or 48kHz

## Story Creation Issues

### Node Errors
- **Duplicate IDs**: Ensure unique node identifiers
- **Broken Links**: Check choice target nodes exist
- **Missing Content**: Add text to all nodes
- **Circular References**: Avoid infinite loops

### Character Problems
- **Voice Loading**: Check voice file formats
- **TTS Errors**: Verify TTS engine installation
- **Profile Conflicts**: Ensure unique profile names
- **Sample Quality**: Use high-quality audio

## X Integration Issues

### API Errors
- **Rate Limits**: Respect X API rate limits
- **Authentication**: Verify API credentials
- **Permissions**: Check app permissions
- **Account Status**: Ensure account is active

### Posting Problems
- **Content Length**: Stay within character limits
- **Media Upload**: Check file size and format
- **Scheduling**: Verify posting times
- **Link Shortening**: Use proper URL formats

## Performance Issues

### Slow Response
- **Model Size**: Use smaller LM Studio models
- **Memory Usage**: Close unnecessary applications
- **Storage Space**: Ensure adequate disk space
- **Network Speed**: Check internet connection

### Crashes
- **Log Files**: Check logs directory for errors
- **Memory Leaks**: Restart application regularly
- **Corrupted Data**: Backup and restore data
- **System Resources**: Monitor CPU and RAM usage

## Getting Help

### Support Channels
- **Chat Assistant**: Use in-app AI assistant
- **Documentation**: Check help sections
- **Community**: Join user forums
- **Bug Reports**: Submit detailed issue reports

### Information to Include
- **Error Messages**: Copy exact error text
- **Steps to Reproduce**: Detailed reproduction steps
- **System Info**: OS, Python version, hardware
- **Log Files**: Relevant log entries
"""
        }
    }


class HelpTab(QWidget):
    """Help and documentation tab"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        logger.info("Help tab initialized")
    
    def setup_ui(self):
        """Setup the help interface"""
        layout = QVBoxLayout(self)
        
        # Create splitter for navigation and content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Navigation tree
        nav_widget = self._create_navigation()
        splitter.addWidget(nav_widget)
        
        # Content area
        content_widget = self._create_content_area()
        splitter.addWidget(content_widget)
        
        # Set splitter proportions
        splitter.setSizes([250, 600])
    
    def _create_navigation(self) -> QWidget:
        """Create navigation tree"""
        widget = QGroupBox("📚 Help Topics")
        layout = QVBoxLayout(widget)
        
        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search help topics...")
        self.search_box.textChanged.connect(self._filter_topics)
        layout.addWidget(self.search_box)
        
        # Navigation tree
        self.nav_tree = QTreeWidget()
        self.nav_tree.setHeaderHidden(True)
        self.nav_tree.itemClicked.connect(self._on_topic_selected)
        layout.addWidget(self.nav_tree)
        
        # Populate navigation
        self._populate_navigation()
        
        return widget
    
    def _create_content_area(self) -> QWidget:
        """Create content display area"""
        widget = QGroupBox("📖 Help Content")
        layout = QVBoxLayout(widget)
        
        # Content display
        self.content_display = QTextEdit()
        self.content_display.setReadOnly(True)
        self.content_display.setFont(QFont("Consolas", 11))
        layout.addWidget(self.content_display)
        
        # Show welcome content
        self._show_welcome_content()
        
        return widget
    
    def _populate_navigation(self):
        """Populate the navigation tree"""
        for section_id, section_data in HelpContent.HELP_SECTIONS.items():
            item = QTreeWidgetItem([section_data["title"]])
            item.setData(0, Qt.ItemDataRole.UserRole, section_id)
            self.nav_tree.addTopLevelItem(item)
    
    def _filter_topics(self, search_text: str):
        """Filter help topics based on search"""
        for i in range(self.nav_tree.topLevelItemCount()):
            item = self.nav_tree.topLevelItem(i)
            if search_text.lower() in item.text(0).lower():
                item.setHidden(False)
            else:
                item.setHidden(True)
    
    def _on_topic_selected(self, item: QTreeWidgetItem, column: int):
        """Handle topic selection"""
        section_id = item.data(0, Qt.ItemDataRole.UserRole)
        if section_id and section_id in HelpContent.HELP_SECTIONS:
            content = HelpContent.HELP_SECTIONS[section_id]["content"]
            self.content_display.setMarkdown(content)
    
    def _show_welcome_content(self):
        """Show welcome content"""
        welcome_content = """
# 🎉 Welcome to CYOA Automation System Help

This comprehensive help system will guide you through all aspects of creating and managing Choose Your Own Adventure stories.

## 🚀 Quick Navigation

- **Getting Started**: New to the system? Start here!
- **Story Editor**: Learn to create compelling interactive stories
- **Characters**: Develop memorable characters with unique voices
- **X Manager**: Automate your social media presence
- **Voice Studio**: Create professional text-to-speech voices
- **Analytics**: Track and optimize your story performance
- **Troubleshooting**: Solve common issues quickly

## 💡 Tips for Success

1. **Start Small**: Begin with simple stories and expand
2. **Test Everything**: Preview content before publishing
3. **Engage Audience**: Respond to comments and feedback
4. **Use Analytics**: Data-driven decisions improve performance
5. **Be Consistent**: Regular posting builds loyal audience

## 🤖 AI Assistant

Don't forget about the Chat tab! Our AI assistant can answer questions and help you navigate the app in real-time.

Select a topic from the left to get started, or use the search box to find specific information.
"""
        self.content_display.setMarkdown(welcome_content)
    
    def show_context_help(self, context: str):
        """Show help for specific context"""
        # Map contexts to help sections
        context_map = {
            "story_editor": "story_editor",
            "characters": "characters", 
            "x_manager": "x_manager",
            "voice_studio": "voice_studio",
            "analytics": "analytics"
        }
        
        section_id = context_map.get(context)
        if section_id:
            # Find and select the item
            for i in range(self.nav_tree.topLevelItemCount()):
                item = self.nav_tree.topLevelItem(i)
                if item.data(0, Qt.ItemDataRole.UserRole) == section_id:
                    self.nav_tree.setCurrentItem(item)
                    self._on_topic_selected(item, 0)
                    break
