"""
Restore Options Dialog
Allows users to restore stories from Google Drive backups
"""

import logging
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QListWidget, 
    QListWidgetItem, QLabel, QPushButton, QProgressBar, QTextEdit,
    QDialogButtonBox, QMessageBox, QSplitter, QFormLayout
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)

class RestoreWorker(QThread):
    """Worker thread for restore operations"""
    
    progress_updated = pyqtSignal(int, str)
    restore_completed = pyqtSignal(dict)  # story_data
    restore_failed = pyqtSignal(str)      # error message
    
    def __init__(self, backup_manager, backup_id):
        super().__init__()
        self.backup_manager = backup_manager
        self.backup_id = backup_id
    
    def run(self):
        """Run restore process"""
        try:
            self.progress_updated.emit(10, "Downloading backup from Google Drive...")
            
            # Restore backup
            self.progress_updated.emit(50, "Extracting backup data...")
            story_data = self.backup_manager.restore_backup(self.backup_id)
            
            if story_data:
                self.progress_updated.emit(100, "Restore completed successfully")
                self.restore_completed.emit(story_data)
            else:
                self.restore_failed.emit("Failed to restore backup")
                
        except Exception as e:
            logger.error(f"Restore failed: {e}")
            self.restore_failed.emit(str(e))


class RestoreOptionsDialog(QDialog):
    """Dialog for restoring from Google Drive backups"""
    
    def __init__(self, backup_manager, parent=None):
        super().__init__(parent)
        self.backup_manager = backup_manager
        self.restore_worker = None
        self.restored_story = None
        self.available_backups = []
        
        self.setWindowTitle("📥 Restore from Google Drive")
        self.setModal(True)
        self.resize(800, 600)
        
        self._setup_ui()
        self._load_backups()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("📥 Restore Story from Google Drive")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("padding: 10px; color: #2c3e50;")
        layout.addWidget(header)
        
        # Main content with splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left side - backup list
        left_widget = QGroupBox("📋 Available Backups")
        left_layout = QVBoxLayout(left_widget)
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh List")
        refresh_btn.clicked.connect(self._load_backups)
        left_layout.addWidget(refresh_btn)
        
        # Backup list
        self.backup_list = QListWidget()
        self.backup_list.itemSelectionChanged.connect(self._on_backup_selected)
        left_layout.addWidget(self.backup_list)
        
        splitter.addWidget(left_widget)
        
        # Right side - backup details
        right_widget = QGroupBox("📄 Backup Details")
        right_layout = QVBoxLayout(right_widget)
        
        # Details form
        self.details_form = QFormLayout()
        
        self.backup_name_label = QLabel("No backup selected")
        self.details_form.addRow("Name:", self.backup_name_label)
        
        self.backup_date_label = QLabel("-")
        self.details_form.addRow("Created:", self.backup_date_label)
        
        self.backup_size_label = QLabel("-")
        self.details_form.addRow("Size:", self.backup_size_label)
        
        self.story_title_label = QLabel("-")
        self.details_form.addRow("Story:", self.story_title_label)
        
        right_layout.addLayout(self.details_form)
        
        # Preview area
        preview_label = QLabel("📖 Backup Contents:")
        preview_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMaximumHeight(200)
        self.preview_text.setPlainText("Select a backup to see its contents...")
        right_layout.addWidget(self.preview_text)
        
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([300, 500])
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Delete backup button
        self.delete_btn = QPushButton("🗑️ Delete Backup")
        self.delete_btn.clicked.connect(self._delete_backup)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 8px 16px;")
        button_layout.addWidget(self.delete_btn)
        
        button_layout.addStretch()
        
        # Main action buttons
        self.restore_btn = QPushButton("📥 Restore Selected")
        self.restore_btn.clicked.connect(self._start_restore)
        self.restore_btn.setEnabled(False)
        self.restore_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 8px 16px;")
        button_layout.addWidget(self.restore_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _load_backups(self):
        """Load available backups from Google Drive"""
        try:
            self.status_label.setText("Loading backups...")
            self.backup_list.clear()
            
            # Get backups from manager
            self.available_backups = self.backup_manager.list_backups()
            
            if not self.available_backups:
                item = QListWidgetItem("No backups found")
                item.setFlags(Qt.ItemFlag.NoItemFlags)  # Make it unselectable
                self.backup_list.addItem(item)
                self.status_label.setText("No backups found in Google Drive")
            else:
                for backup in self.available_backups:
                    item = QListWidgetItem()
                    item.setText(f"📦 {backup['name']}")
                    item.setData(Qt.ItemDataRole.UserRole, backup)
                    self.backup_list.addItem(item)
                
                self.status_label.setText(f"Found {len(self.available_backups)} backups")
            
        except Exception as e:
            logger.error(f"Error loading backups: {e}")
            self.status_label.setText("Error loading backups")
            QMessageBox.critical(self, "Error", f"Failed to load backups: {e}")
    
    def _on_backup_selected(self):
        """Handle backup selection"""
        try:
            current_item = self.backup_list.currentItem()
            if not current_item or not current_item.data(Qt.ItemDataRole.UserRole):
                self._clear_details()
                return
            
            backup = current_item.data(Qt.ItemDataRole.UserRole)
            
            # Update details
            self.backup_name_label.setText(backup['name'])
            self.backup_date_label.setText(backup['created'])
            self.backup_size_label.setText(backup['size'])
            self.story_title_label.setText(backup.get('story_title', 'Unknown'))
            
            # Update preview
            preview_text = f"""📖 Story: {backup.get('story_title', 'Unknown')}
📅 Created: {backup['created']}
📦 Size: {backup['size']}
🆔 Backup ID: {backup['id']}

This backup contains:
• Story data (nodes, connections, metadata)
• Character profiles and personalities
• Item definitions and inventory data
• AI settings and voice profiles

Click 'Restore Selected' to restore this backup."""
            
            self.preview_text.setPlainText(preview_text)
            
            # Enable buttons
            self.restore_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
        except Exception as e:
            logger.error(f"Error handling backup selection: {e}")
            self._clear_details()
    
    def _clear_details(self):
        """Clear backup details"""
        self.backup_name_label.setText("No backup selected")
        self.backup_date_label.setText("-")
        self.backup_size_label.setText("-")
        self.story_title_label.setText("-")
        self.preview_text.setPlainText("Select a backup to see its contents...")
        self.restore_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
    
    def _delete_backup(self):
        """Delete selected backup"""
        try:
            current_item = self.backup_list.currentItem()
            if not current_item or not current_item.data(Qt.ItemDataRole.UserRole):
                return
            
            backup = current_item.data(Qt.ItemDataRole.UserRole)
            
            # Confirm deletion
            reply = QMessageBox.question(
                self,
                "Delete Backup",
                f"Are you sure you want to delete this backup?\n\n"
                f"Name: {backup['name']}\n"
                f"Story: {backup.get('story_title', 'Unknown')}\n\n"
                f"This action cannot be undone.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.backup_manager.delete_backup(backup['id'])
                if success:
                    self.status_label.setText("Backup deleted successfully")
                    self._load_backups()  # Refresh list
                else:
                    QMessageBox.critical(self, "Error", "Failed to delete backup")
            
        except Exception as e:
            logger.error(f"Error deleting backup: {e}")
            QMessageBox.critical(self, "Error", f"Failed to delete backup: {e}")
    
    def _start_restore(self):
        """Start the restore process"""
        try:
            current_item = self.backup_list.currentItem()
            if not current_item or not current_item.data(Qt.ItemDataRole.UserRole):
                QMessageBox.warning(self, "No Selection", "Please select a backup to restore.")
                return
            
            backup = current_item.data(Qt.ItemDataRole.UserRole)
            
            # Confirm restore
            reply = QMessageBox.question(
                self,
                "Restore Backup",
                f"Are you sure you want to restore this backup?\n\n"
                f"Name: {backup['name']}\n"
                f"Story: {backup.get('story_title', 'Unknown')}\n\n"
                f"This will replace your current story.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # Start restore worker
            self.restore_worker = RestoreWorker(self.backup_manager, backup['id'])
            self.restore_worker.progress_updated.connect(self._on_progress_updated)
            self.restore_worker.restore_completed.connect(self._on_restore_completed)
            self.restore_worker.restore_failed.connect(self._on_restore_failed)
            
            # Update UI
            self.progress_bar.setVisible(True)
            self.restore_btn.setEnabled(False)
            self.restore_btn.setText("⏳ Restoring...")
            
            # Start restore
            self.restore_worker.start()
            
        except Exception as e:
            logger.error(f"Error starting restore: {e}")
            QMessageBox.critical(self, "Restore Error", f"Failed to start restore: {e}")
    
    def _on_progress_updated(self, progress: int, message: str):
        """Handle progress updates"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def _on_restore_completed(self, story_data: dict):
        """Handle restore completion"""
        self.progress_bar.setVisible(False)
        self.restore_btn.setEnabled(True)
        self.restore_btn.setText("📥 Restore Selected")
        self.status_label.setText("")
        
        self.restored_story = story_data
        
        QMessageBox.information(
            self, 
            "Restore Complete", 
            f"✅ Backup restored successfully!\n\n"
            f"Story: {story_data.get('title', 'Unknown')}\n\n"
            f"Your story has been restored from the backup."
        )
        self.accept()
    
    def _on_restore_failed(self, error_message: str):
        """Handle restore failure"""
        self.progress_bar.setVisible(False)
        self.restore_btn.setEnabled(True)
        self.restore_btn.setText("📥 Restore Selected")
        self.status_label.setText("")
        
        QMessageBox.critical(
            self, 
            "Restore Failed", 
            f"❌ Restore failed:\n\n{error_message}\n\n"
            f"Please check your Google Drive connection and try again."
        )
    
    def get_restored_story(self):
        """Get the restored story data"""
        return self.restored_story
