"""
Media Generation Tab
Tab for generating images, videos, and other media content
"""

import logging
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QFormLayout, QComboBox, QLineEdit, QPushButton, QSpinBox,
    QCheckBox, QLabel, QTextEdit, QSlider, QFrame, QMessageBox,
    QProgressBar, QListWidget, QListWidgetItem, QSplitter,
    QScrollArea, QGridLayout
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap

logger = logging.getLogger(__name__)

class MediaGenerationWorker(QThread):
    """Background worker for media generation"""
    
    generation_started = pyqtSignal(str)  # task_id
    generation_progress = pyqtSignal(str, int)  # task_id, progress
    generation_completed = pyqtSignal(str, str)  # task_id, file_path
    generation_failed = pyqtSignal(str, str)  # task_id, error
    
    def __init__(self, task_type: str, params: Dict[str, Any]):
        super().__init__()
        self.task_type = task_type
        self.params = params
        self.task_id = f"{task_type}_{id(self)}"
    
    def run(self):
        """Run media generation task"""
        try:
            self.generation_started.emit(self.task_id)
            
            if self.task_type == "image":
                self._generate_image()
            elif self.task_type == "video":
                self._generate_video()
            elif self.task_type == "audio":
                self._generate_audio()
            
        except Exception as e:
            logger.error(f"Media generation failed: {e}")
            self.generation_failed.emit(self.task_id, str(e))
    
    def _generate_image(self):
        """Generate image using ComfyUI"""
        # Simulate image generation
        import time
        for i in range(10):
            time.sleep(0.5)
            self.generation_progress.emit(self.task_id, i * 10)
        
        # For now, just create a placeholder
        output_path = f"data/media/generated_image_{self.task_id}.png"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create a simple placeholder image
        from PyQt6.QtGui import QPixmap, QPainter, QColor
        pixmap = QPixmap(512, 512)
        pixmap.fill(QColor(100, 150, 200))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "Generated Image\nPlaceholder")
        painter.end()
        
        pixmap.save(output_path)
        self.generation_completed.emit(self.task_id, output_path)
    
    def _generate_video(self):
        """Generate video using ComfyUI"""
        # Simulate video generation
        import time
        for i in range(20):
            time.sleep(0.3)
            self.generation_progress.emit(self.task_id, i * 5)
        
        # For now, just create a placeholder
        output_path = f"data/media/generated_video_{self.task_id}.mp4"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create placeholder file
        with open(output_path, 'w') as f:
            f.write("# Video placeholder file")
        
        self.generation_completed.emit(self.task_id, output_path)
    
    def _generate_audio(self):
        """Generate audio/voice"""
        # Simulate audio generation
        import time
        for i in range(15):
            time.sleep(0.2)
            self.generation_progress.emit(self.task_id, i * 7)
        
        # For now, just create a placeholder
        output_path = f"data/media/generated_audio_{self.task_id}.wav"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create placeholder file
        with open(output_path, 'w') as f:
            f.write("# Audio placeholder file")
        
        self.generation_completed.emit(self.task_id, output_path)

class MediaGenerationTab(QWidget):
    """Tab for media generation"""
    
    def __init__(self, config: Dict[str, Any], comfyui_client=None):
        super().__init__()
        self.config = config
        self.comfyui_client = comfyui_client
        self.current_story = None
        self.generation_tasks = {}
        
        self._setup_ui()
        
        logger.info("Media generation tab initialized")
    
    def _setup_ui(self):
        """Setup the media generation interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content - splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left side - generation controls
        controls = self._create_generation_controls()
        splitter.addWidget(controls)
        
        # Right side - generated media gallery
        gallery = self._create_media_gallery()
        splitter.addWidget(gallery)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
    
    def _create_header(self) -> QWidget:
        """Create media generation header"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🎬 Media Generation Studio")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Status
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        return header
    
    def _create_generation_controls(self) -> QWidget:
        """Create generation controls"""
        controls = QWidget()
        layout = QVBoxLayout(controls)
        
        # Generation tabs
        self.gen_tabs = QTabWidget()
        layout.addWidget(self.gen_tabs)
        
        # Create generation tabs
        self._create_audio_generation_tab()
        self._create_video_generation_tab()
        self._create_batch_generation_tab()
        self._create_post_preview_tab()
        self._create_approval_tab()
        
        # Generation queue
        queue_group = QGroupBox("📋 Generation Queue")
        queue_layout = QVBoxLayout(queue_group)
        
        self.queue_list = QListWidget()
        queue_layout.addWidget(self.queue_list)
        
        # Queue controls
        queue_controls = QHBoxLayout()
        
        self.clear_queue_btn = QPushButton("🗑️ Clear Queue")
        self.clear_queue_btn.clicked.connect(self._clear_queue)
        queue_controls.addWidget(self.clear_queue_btn)
        
        self.pause_queue_btn = QPushButton("⏸️ Pause Queue")
        self.pause_queue_btn.clicked.connect(self._toggle_queue)
        queue_controls.addWidget(self.pause_queue_btn)
        
        queue_layout.addLayout(queue_controls)
        layout.addWidget(queue_group)
        
        return controls
    
    def _create_post_preview_tab(self):
        """Create post preview tab for Twitter-like preview"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Node selection
        selection_group = QGroupBox("📝 Node Selection")
        selection_layout = QFormLayout(selection_group)

        self.preview_node_combo = QComboBox()
        self.preview_node_combo.addItem("Select a story node...")
        self.preview_node_combo.currentTextChanged.connect(self._update_post_preview)
        selection_layout.addRow("Story Node:", self.preview_node_combo)

        layout.addWidget(selection_group)

        # Twitter-like preview
        preview_group = QGroupBox("🐦 Post Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Mock Twitter header
        header_layout = QHBoxLayout()
        profile_pic = QLabel("👤")
        profile_pic.setFixedSize(40, 40)
        profile_pic.setStyleSheet("border-radius: 20px; background-color: #1DA1F2; color: white; text-align: center; font-size: 20px;")
        header_layout.addWidget(profile_pic)

        user_info = QVBoxLayout()
        username = QLabel("Your Story Account")
        username.setStyleSheet("font-weight: bold; color: #14171A;")
        handle = QLabel("@yourstory • now")
        handle.setStyleSheet("color: #657786; font-size: 12px;")
        user_info.addWidget(username)
        user_info.addWidget(handle)
        header_layout.addLayout(user_info)
        header_layout.addStretch()

        preview_layout.addLayout(header_layout)

        # Post content
        self.post_text = QLabel("Select a node to see the post preview...")
        self.post_text.setWordWrap(True)
        self.post_text.setStyleSheet("color: #14171A; font-size: 14px; margin: 10px 0;")
        preview_layout.addWidget(self.post_text)

        # Video player placeholder
        self.video_player = QLabel("📹 Video will appear here")
        self.video_player.setMinimumHeight(300)
        self.video_player.setStyleSheet("border: 2px solid #E1E8ED; border-radius: 12px; background-color: #F7F9FA; text-align: center; color: #657786;")
        self.video_player.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(self.video_player)

        # Note: Audio is embedded in video for lip sync
        audio_note = QLabel("🎵 Audio is embedded in the video for lip sync")
        audio_note.setStyleSheet("color: #657786; font-style: italic; margin: 5px 0;")
        preview_layout.addWidget(audio_note)

        # Choice buttons (Twitter-like)
        choices_layout = QVBoxLayout()
        self.choice_buttons = []
        for i in range(4):  # Max 4 choices
            btn = QPushButton(f"Choice {i+1}")
            btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #1DA1F2;
                    border-radius: 20px;
                    padding: 8px 16px;
                    color: #1DA1F2;
                    background-color: white;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #E8F5FE;
                }
            """)
            btn.setVisible(False)
            choices_layout.addWidget(btn)
            self.choice_buttons.append(btn)

        preview_layout.addLayout(choices_layout)

        # Twitter engagement buttons
        engagement_layout = QHBoxLayout()
        engagement_layout.addWidget(QLabel("💬 Reply"))
        engagement_layout.addWidget(QLabel("🔄 Retweet"))
        engagement_layout.addWidget(QLabel("❤️ Like"))
        engagement_layout.addWidget(QLabel("📤 Share"))
        engagement_layout.addStretch()
        preview_layout.addLayout(engagement_layout)

        layout.addWidget(preview_group)

        layout.addStretch()
        self.gen_tabs.addTab(tab, "🐦 Post Preview")
    
    def _create_video_generation_tab(self):
        """Create video generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Video settings
        settings_group = QGroupBox("🎬 Video Settings")
        settings_layout = QFormLayout(settings_group)
        
        self.video_prompt_edit = QTextEdit()
        self.video_prompt_edit.setMaximumHeight(100)
        self.video_prompt_edit.setPlaceholderText("Describe the video scene...")
        settings_layout.addRow("Prompt:", self.video_prompt_edit)
        
        self.video_duration_spin = QSpinBox()
        self.video_duration_spin.setRange(1, 30)
        self.video_duration_spin.setValue(5)
        self.video_duration_spin.setSuffix(" seconds")
        settings_layout.addRow("Duration:", self.video_duration_spin)
        
        self.video_fps_combo = QComboBox()
        self.video_fps_combo.addItems(["24", "30", "60"])
        settings_layout.addRow("FPS:", self.video_fps_combo)
        
        self.video_resolution_combo = QComboBox()
        self.video_resolution_combo.addItems([
            "720p (1280x720)", "1080p (1920x1080)", "4K (3840x2160)"
        ])
        settings_layout.addRow("Resolution:", self.video_resolution_combo)
        
        layout.addWidget(settings_group)
        
        # Generate button
        self.generate_video_btn = QPushButton("🎬 Generate Video")
        self.generate_video_btn.clicked.connect(self._generate_video)
        layout.addWidget(self.generate_video_btn)
        
        layout.addStretch()
        self.gen_tabs.addTab(tab, "🎬 Videos")
    
    def _create_audio_generation_tab(self):
        """Create audio generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Audio settings
        settings_group = QGroupBox("🎵 Audio Settings")
        settings_layout = QFormLayout(settings_group)
        
        self.audio_text_edit = QTextEdit()
        self.audio_text_edit.setMaximumHeight(100)
        self.audio_text_edit.setPlaceholderText("Enter text to convert to speech...")
        settings_layout.addRow("Text:", self.audio_text_edit)
        
        self.voice_template_combo = QComboBox()
        self.voice_template_combo.addItems([
            "Default", "Male Young", "Male Old", "Female Young", 
            "Female Old", "Child", "Narrator", "Character"
        ])
        settings_layout.addRow("Voice Template:", self.voice_template_combo)
        
        self.audio_speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.audio_speed_slider.setRange(50, 200)
        self.audio_speed_slider.setValue(100)
        settings_layout.addRow("Speed (%):", self.audio_speed_slider)
        
        self.audio_pitch_slider = QSlider(Qt.Orientation.Horizontal)
        self.audio_pitch_slider.setRange(50, 200)
        self.audio_pitch_slider.setValue(100)
        settings_layout.addRow("Pitch (%):", self.audio_pitch_slider)
        
        layout.addWidget(settings_group)
        
        # Generate button
        self.generate_audio_btn = QPushButton("🎵 Generate Audio")
        self.generate_audio_btn.clicked.connect(self._generate_audio)
        layout.addWidget(self.generate_audio_btn)
        
        # Audio generation note
        note_label = QLabel("💡 Note: Audio must be generated before video to ensure proper synchronization")
        note_label.setStyleSheet("color: #657786; font-style: italic; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)

        layout.addStretch()
        self.gen_tabs.addTab(tab, "🎵 Audio First")
    
    def _create_batch_generation_tab(self):
        """Create batch generation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Batch settings
        settings_group = QGroupBox("📦 Batch Generation")
        settings_layout = QFormLayout(settings_group)
        
        self.batch_type_combo = QComboBox()
        self.batch_type_combo.addItems([
            "All Story Nodes", "Selected Nodes", "Characters", "Items"
        ])
        settings_layout.addRow("Generate for:", self.batch_type_combo)
        
        self.batch_media_type_combo = QComboBox()
        self.batch_media_type_combo.addItems([
            "Audio + Video", "Videos Only", "Audio Only"
        ])
        settings_layout.addRow("Media Type:", self.batch_media_type_combo)

        # Auto-detect concurrent jobs based on system resources
        import os
        cpu_count = os.cpu_count() or 2
        optimal_jobs = max(1, min(cpu_count // 2, 4))  # Use half CPU cores, max 4

        concurrent_info = QLabel(f"Concurrent Jobs: {optimal_jobs} (auto-detected)")
        concurrent_info.setStyleSheet("color: #657786; font-style: italic;")
        settings_layout.addRow("Performance:", concurrent_info)
        
        layout.addWidget(settings_group)
        
        # Progress
        progress_group = QGroupBox("📊 Batch Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.batch_progress = QProgressBar()
        progress_layout.addWidget(self.batch_progress)
        
        self.batch_status_label = QLabel("Ready for batch generation")
        progress_layout.addWidget(self.batch_status_label)
        
        layout.addWidget(progress_group)
        
        # Generate button
        self.generate_batch_btn = QPushButton("📦 Start Batch Generation")
        self.generate_batch_btn.clicked.connect(self._generate_batch)
        layout.addWidget(self.generate_batch_btn)
        
        layout.addStretch()
        self.gen_tabs.addTab(tab, "📦 Batch")

    def _create_approval_tab(self):
        """Create approval tab for reviewing generated content"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Header with stats
        header_layout = QHBoxLayout()

        self.approval_stats = QLabel("📊 Pending: 0 | Approved: 0 | Rejected: 0")
        self.approval_stats.setStyleSheet("font-weight: bold; color: #14171A; padding: 10px;")
        header_layout.addWidget(self.approval_stats)

        header_layout.addStretch()

        self.auto_play_check = QCheckBox("Auto-play videos")
        self.auto_play_check.setChecked(True)
        header_layout.addWidget(self.auto_play_check)

        layout.addLayout(header_layout)

        # Main approval interface
        approval_splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(approval_splitter)

        # Left side - content list
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)

        list_header = QLabel("📋 Content Queue")
        list_header.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        list_layout.addWidget(list_header)

        self.approval_list = QListWidget()
        self.approval_list.itemClicked.connect(self._load_approval_item)
        list_layout.addWidget(self.approval_list)

        approval_splitter.addWidget(list_widget)

        # Right side - preview and controls
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)

        # Current item info
        self.current_item_label = QLabel("Select an item to review")
        self.current_item_label.setStyleSheet("font-weight: bold; font-size: 16px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        preview_layout.addWidget(self.current_item_label)

        # Video preview
        self.approval_video_player = QLabel("📹 Video Preview")
        self.approval_video_player.setMinimumHeight(400)
        self.approval_video_player.setStyleSheet("border: 2px solid #E1E8ED; border-radius: 12px; background-color: #F7F9FA; text-align: center; color: #657786;")
        self.approval_video_player.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(self.approval_video_player)

        # Audio controls
        audio_controls = QHBoxLayout()

        self.approval_play_btn = QPushButton("▶️ Play")
        self.approval_play_btn.setEnabled(False)
        audio_controls.addWidget(self.approval_play_btn)

        self.approval_pause_btn = QPushButton("⏸️ Pause")
        self.approval_pause_btn.setEnabled(False)
        audio_controls.addWidget(self.approval_pause_btn)

        self.approval_restart_btn = QPushButton("🔄 Restart")
        self.approval_restart_btn.setEnabled(False)
        audio_controls.addWidget(self.approval_restart_btn)

        audio_controls.addStretch()

        self.sync_status = QLabel("🎵 Audio: Not loaded")
        self.sync_status.setStyleSheet("color: #657786;")
        audio_controls.addWidget(self.sync_status)

        preview_layout.addLayout(audio_controls)

        # Feedback section
        feedback_group = QGroupBox("📝 Feedback & Notes")
        feedback_layout = QVBoxLayout(feedback_group)

        # Quick rejection reasons
        reasons_layout = QHBoxLayout()

        self.reject_video_btn = QPushButton("❌ Video Issue")
        self.reject_video_btn.clicked.connect(lambda: self._quick_reject("video"))
        reasons_layout.addWidget(self.reject_video_btn)

        self.reject_audio_btn = QPushButton("🔇 Audio Issue")
        self.reject_audio_btn.clicked.connect(lambda: self._quick_reject("audio"))
        reasons_layout.addWidget(self.reject_audio_btn)

        self.reject_sync_btn = QPushButton("⏱️ Sync Issue")
        self.reject_sync_btn.clicked.connect(lambda: self._quick_reject("sync"))
        reasons_layout.addWidget(self.reject_sync_btn)

        reasons_layout.addStretch()
        feedback_layout.addLayout(reasons_layout)

        # Custom feedback
        self.feedback_text = QTextEdit()
        self.feedback_text.setMaximumHeight(100)
        self.feedback_text.setPlaceholderText("Optional: Describe what needs to be changed or improved...")
        feedback_layout.addWidget(self.feedback_text)

        preview_layout.addWidget(feedback_group)

        # Approval buttons
        approval_buttons = QHBoxLayout()

        self.reject_btn = QPushButton("❌ Reject")
        self.reject_btn.setStyleSheet("background-color: #dc3545; color: white; font-weight: bold; padding: 10px 20px; border-radius: 5px;")
        self.reject_btn.clicked.connect(self._reject_current)
        self.reject_btn.setEnabled(False)
        approval_buttons.addWidget(self.reject_btn)

        approval_buttons.addStretch()

        self.approve_btn = QPushButton("✅ Approve")
        self.approve_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold; padding: 10px 20px; border-radius: 5px;")
        self.approve_btn.clicked.connect(self._approve_current)
        self.approve_btn.setEnabled(False)
        approval_buttons.addWidget(self.approve_btn)

        preview_layout.addLayout(approval_buttons)

        approval_splitter.addWidget(preview_widget)

        # Set splitter proportions
        approval_splitter.setSizes([300, 700])

        self.gen_tabs.addTab(tab, "✅ Approval")
    
    def _create_media_gallery(self) -> QWidget:
        """Create media gallery"""
        gallery = QGroupBox("🖼️ Generated Media Gallery")
        layout = QVBoxLayout(gallery)
        
        # Gallery controls
        controls = QHBoxLayout()
        
        self.gallery_filter_combo = QComboBox()
        self.gallery_filter_combo.addItems(["All Media", "Images", "Videos", "Audio"])
        self.gallery_filter_combo.currentTextChanged.connect(self._filter_gallery)
        controls.addWidget(QLabel("Filter:"))
        controls.addWidget(self.gallery_filter_combo)
        
        controls.addStretch()
        
        self.refresh_gallery_btn = QPushButton("🔄 Refresh")
        self.refresh_gallery_btn.clicked.connect(self._refresh_gallery)
        controls.addWidget(self.refresh_gallery_btn)
        
        layout.addLayout(controls)
        
        # Gallery grid
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.gallery_layout = QGridLayout(scroll_widget)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return gallery
    

    
    def _generate_video(self):
        """Generate video"""
        prompt = self.video_prompt_edit.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "Missing Prompt", "Please enter a video prompt.")
            return
        
        params = {
            'prompt': prompt,
            'duration': self.video_duration_spin.value(),
            'fps': int(self.video_fps_combo.currentText()),
            'resolution': self.video_resolution_combo.currentText()
        }
        
        self._start_generation_task("video", params)
    
    def _generate_audio(self):
        """Generate audio"""
        text = self.audio_text_edit.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "Missing Text", "Please enter text for audio generation.")
            return
        
        params = {
            'text': text,
            'voice': self.voice_template_combo.currentText(),
            'speed': self.audio_speed_slider.value(),
            'pitch': self.audio_pitch_slider.value()
        }
        
        self._start_generation_task("audio", params)
    
    def _generate_batch(self):
        """Start batch generation"""
        if not self.current_story:
            QMessageBox.warning(self, "No Story", "Please load a story first.")
            return
        
        # TODO: Implement batch generation logic
        QMessageBox.information(self, "Batch Generation", "Batch generation will be implemented soon!")
    
    def _start_generation_task(self, task_type: str, params: Dict[str, Any]):
        """Start a generation task"""
        worker = MediaGenerationWorker(task_type, params)
        worker.generation_started.connect(self._on_generation_started)
        worker.generation_progress.connect(self._on_generation_progress)
        worker.generation_completed.connect(self._on_generation_completed)
        worker.generation_failed.connect(self._on_generation_failed)
        
        self.generation_tasks[worker.task_id] = worker
        worker.start()
        
        # Add to queue display
        item = QListWidgetItem(f"{task_type.title()}: {params.get('prompt', params.get('text', 'Unknown'))[:50]}...")
        item.setData(Qt.ItemDataRole.UserRole, worker.task_id)
        self.queue_list.addItem(item)
    
    def _on_generation_started(self, task_id: str):
        """Handle generation started"""
        self.status_label.setText(f"Generating {task_id}...")
        logger.info(f"Generation started: {task_id}")
    
    def _on_generation_progress(self, task_id: str, progress: int):
        """Handle generation progress"""
        # Update queue item
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == task_id:
                item.setText(f"{item.text().split(' (')[0]} ({progress}%)")
                break
    
    def _on_generation_completed(self, task_id: str, file_path: str):
        """Handle generation completed"""
        self.status_label.setText("Ready")
        
        # Remove from queue
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == task_id:
                self.queue_list.takeItem(i)
                break
        
        # Clean up worker
        if task_id in self.generation_tasks:
            del self.generation_tasks[task_id]
        
        # Refresh gallery
        self._refresh_gallery()
        
        logger.info(f"Generation completed: {task_id} -> {file_path}")
    
    def _on_generation_failed(self, task_id: str, error: str):
        """Handle generation failed"""
        self.status_label.setText("Ready")
        
        # Remove from queue
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == task_id:
                self.queue_list.takeItem(i)
                break
        
        # Clean up worker
        if task_id in self.generation_tasks:
            del self.generation_tasks[task_id]
        
        QMessageBox.critical(self, "Generation Failed", f"Media generation failed: {error}")
        logger.error(f"Generation failed: {task_id} - {error}")
    
    def _clear_queue(self):
        """Clear generation queue"""
        # Stop all running tasks
        for worker in self.generation_tasks.values():
            worker.terminate()
        
        self.generation_tasks.clear()
        self.queue_list.clear()
        self.status_label.setText("Ready")
    
    def _toggle_queue(self):
        """Toggle queue pause/resume"""
        # TODO: Implement queue pause/resume
        pass
    
    def _filter_gallery(self):
        """Filter gallery by media type"""
        self._refresh_gallery()
    
    def _refresh_gallery(self):
        """Refresh media gallery"""
        # Clear existing items
        for i in reversed(range(self.gallery_layout.count())):
            self.gallery_layout.itemAt(i).widget().setParent(None)
        
        # Load media files
        media_dir = Path("data/media")
        if not media_dir.exists():
            return
        
        filter_type = self.gallery_filter_combo.currentText()
        
        # Get media files based on filter
        files = []
        if filter_type in ["All Media", "Images"]:
            files.extend(media_dir.glob("*.png"))
            files.extend(media_dir.glob("*.jpg"))
            files.extend(media_dir.glob("*.jpeg"))
        
        if filter_type in ["All Media", "Videos"]:
            files.extend(media_dir.glob("*.mp4"))
            files.extend(media_dir.glob("*.avi"))
        
        if filter_type in ["All Media", "Audio"]:
            files.extend(media_dir.glob("*.wav"))
            files.extend(media_dir.glob("*.mp3"))
        
        # Add files to gallery
        row, col = 0, 0
        for file_path in sorted(files):
            media_widget = self._create_media_widget(file_path)
            self.gallery_layout.addWidget(media_widget, row, col)
            
            col += 1
            if col >= 3:  # 3 columns
                col = 0
                row += 1
    
    def _create_media_widget(self, file_path: Path) -> QWidget:
        """Create widget for media file"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        widget.setMaximumSize(200, 200)
        layout = QVBoxLayout(widget)
        
        # Preview
        if file_path.suffix.lower() in ['.png', '.jpg', '.jpeg']:
            # Image preview
            label = QLabel()
            pixmap = QPixmap(str(file_path))
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio)
                label.setPixmap(scaled_pixmap)
            else:
                label.setText("🖼️ Image")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(label)
        else:
            # Other media types
            icon_label = QLabel()
            if file_path.suffix.lower() in ['.mp4', '.avi']:
                icon_label.setText("🎬 Video")
            elif file_path.suffix.lower() in ['.wav', '.mp3']:
                icon_label.setText("🎵 Audio")
            else:
                icon_label.setText("📄 File")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(icon_label)
        
        # File name
        name_label = QLabel(file_path.name)
        name_label.setWordWrap(True)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)
        
        return widget
    
    def set_story(self, story):
        """Set current story"""
        self.current_story = story
        self._refresh_gallery()
        self._update_node_list()

    def _update_node_list(self):
        """Update node list for preview"""
        self.preview_node_combo.clear()
        self.preview_node_combo.addItem("Select a story node...")

        if self.current_story:
            for node_id, node in self.current_story.nodes.items():
                display_text = f"{node_id}: {node.text[:50]}..." if len(node.text) > 50 else f"{node_id}: {node.text}"
                self.preview_node_combo.addItem(display_text, node_id)

    def _update_post_preview(self, text):
        """Update post preview based on selected node"""
        if text == "Select a story node..." or not self.current_story:
            self.post_text.setText("Select a node to see the post preview...")
            for btn in self.choice_buttons:
                btn.setVisible(False)
            return

        # Get selected node
        node_id = self.preview_node_combo.currentData()
        if node_id and node_id in self.current_story.nodes:
            node = self.current_story.nodes[node_id]

            # Generate post text
            post_content = f"🎮 {node.text[:200]}..."
            if len(node.text) > 200:
                post_content += "\n\nWhat happens next? 👇"

            self.post_text.setText(post_content)

            # Show choice buttons
            for i, btn in enumerate(self.choice_buttons):
                if i < len(node.choices):
                    btn.setText(node.choices[i].text)
                    btn.setVisible(True)
                else:
                    btn.setVisible(False)

    def _load_approval_item(self, item):
        """Load item for approval"""
        self.current_item_label.setText(f"Reviewing: {item.text()}")
        self.approve_btn.setEnabled(True)
        self.reject_btn.setEnabled(True)
        self.approval_play_btn.setEnabled(True)
        self.approval_pause_btn.setEnabled(True)
        self.approval_restart_btn.setEnabled(True)

        # TODO: Load actual video and audio files
        self.approval_video_player.setText("📹 Video loaded - click play to preview")
        self.sync_status.setText("🎵 Audio: Loaded and synced")

    def _quick_reject(self, reason):
        """Quick reject with predefined reason"""
        reason_text = {
            "video": "Video quality issues - needs regeneration",
            "audio": "Audio quality issues - needs regeneration",
            "sync": "Audio/video sync issues - needs adjustment"
        }.get(reason, "General issues")

        self.feedback_text.setPlainText(reason_text)
        self._reject_current()

    def _approve_current(self):
        """Approve current item"""
        current_item = self.approval_list.currentItem()
        if current_item:
            current_item.setText(f"✅ {current_item.text()}")
            current_item.setStyleSheet("color: green;")
            self._move_to_next_item()

    def _reject_current(self):
        """Reject current item"""
        current_item = self.approval_list.currentItem()
        if current_item:
            feedback = self.feedback_text.toPlainText().strip()
            current_item.setText(f"❌ {current_item.text()}")
            current_item.setStyleSheet("color: red;")

            # Store feedback for regeneration
            if feedback:
                current_item.setToolTip(f"Rejection reason: {feedback}")

            self.feedback_text.clear()
            self._move_to_next_item()

    def _move_to_next_item(self):
        """Move to next item in approval queue"""
        current_row = self.approval_list.currentRow()
        if current_row < self.approval_list.count() - 1:
            self.approval_list.setCurrentRow(current_row + 1)
            self._load_approval_item(self.approval_list.currentItem())
        else:
            # No more items
            self.current_item_label.setText("All items reviewed!")
            self.approve_btn.setEnabled(False)
            self.reject_btn.setEnabled(False)
