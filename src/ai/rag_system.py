"""
RAG System - Retrieval Augmented Generation for Story Context
Provides vector-based retrieval of story nodes and app knowledge for LLMs
"""

import logging
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
import pickle

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from story.story_web import StoryWeb, StoryNode

logger = logging.getLogger(__name__)


@dataclass
class RAGDocument:
    """Document for RAG retrieval"""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[np.ndarray] = None


class RAGSystem:
    """RAG system for story and app knowledge retrieval"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self.story_documents: List[RAGDocument] = []
        self.app_documents: List[RAGDocument] = []
        self.embeddings_cache_path = Path("data/rag_embeddings.pkl")
        
        self._initialize_model()
        self._load_app_knowledge()
        
        logger.info("RAG system initialized")
    
    def _initialize_model(self):
        """Initialize the sentence transformer model"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("SentenceTransformers not available - RAG system disabled")
            return
        
        try:
            # Use a lightweight model for local deployment
            model_name = self.config.get('rag', {}).get('model', 'all-MiniLM-L6-v2')
            self.model = SentenceTransformer(model_name)
            logger.info(f"Loaded RAG model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to load RAG model: {e}")
            self.model = None
    
    def _load_app_knowledge(self):
        """Load app knowledge base for chat agent"""
        app_knowledge = [
            {
                "id": "story_editor",
                "content": "Story Editor tab allows creating and editing story nodes. Located in first tab. Features: node tree view, text editor, choice management, node properties (type, rating, premium status).",
                "metadata": {"type": "ui", "category": "editor"}
            },
            {
                "id": "graph_viewer", 
                "content": "Graph Viewer tab visualizes story structure as interactive graph. Located in second tab. Features: node visualization, connection display, auto-layout, zoom controls.",
                "metadata": {"type": "ui", "category": "visualization"}
            },
            {
                "id": "character_editor",
                "content": "Characters tab manages story characters. Features: character creation, personality editing, voice samples, importance levels, role assignment, character randomization, import from other stories.",
                "metadata": {"type": "ui", "category": "characters"}
            },
            {
                "id": "items_tab",
                "content": "Items tab manages story items and inventory system. Features: item creation, effects system, story integration, rarity levels, import from other stories, choice unlocking/blocking.",
                "metadata": {"type": "ui", "category": "items"}
            },
            {
                "id": "x_manager",
                "content": "X Manager tab handles Twitter/X posting. Features: post scheduling, content rating, video upload, choice link management, analytics tracking.",
                "metadata": {"type": "ui", "category": "social"}
            },
            {
                "id": "analytics",
                "content": "Analytics tab shows story performance metrics. Features: engagement tracking, path analysis, conversion rates, A/B testing results.",
                "metadata": {"type": "ui", "category": "analytics"}
            },
            {
                "id": "quiz_creator",
                "content": "Quiz Creator tab builds personality quizzes. Features: template selection, question creation, outcome mapping, viral optimization.",
                "metadata": {"type": "ui", "category": "quiz"}
            },
            {
                "id": "voice_studio",
                "content": "Voice Studio tab manages text-to-speech and voice cloning. Features: voice profile creation, sample recording, TTS settings, character voice assignment.",
                "metadata": {"type": "ui", "category": "voice"}
            },
            {
                "id": "settings_location",
                "content": "Settings are accessed through File menu > Preferences. Includes: LM Studio configuration, API keys, backup settings, voice preferences.",
                "metadata": {"type": "navigation", "category": "settings"}
            },
            {
                "id": "story_creation",
                "content": "Create new stories via File menu > New Story Wizard or New Empty Story. Wizard provides guided creation with templates and AI assistance.",
                "metadata": {"type": "workflow", "category": "creation"}
            },
            {
                "id": "backup_system",
                "content": "Backup system supports Google Drive integration with selective backup options. Configure in File menu > Backup Settings.",
                "metadata": {"type": "feature", "category": "backup"}
            }
        ]
        
        for knowledge in app_knowledge:
            doc = RAGDocument(
                id=knowledge["id"],
                content=knowledge["content"],
                metadata=knowledge["metadata"]
            )
            self.app_documents.append(doc)
        
        # Generate embeddings for app knowledge
        self._generate_embeddings(self.app_documents)
        logger.info(f"Loaded {len(self.app_documents)} app knowledge documents")
    
    def index_story(self, story: StoryWeb):
        """Index a story web for RAG retrieval"""
        if not self.model:
            return

        self.story_documents.clear()

        # Index story nodes
        for node_id, node in story.nodes.items():
            # Create document for node content
            content = f"Node {node_id}: {node.text}"
            if node.choices:
                choices_text = " Choices: " + ", ".join([f"{choice.text} -> {choice.target_node}" for choice in node.choices])
                content += choices_text

            doc = RAGDocument(
                id=f"node_{node_id}",
                content=content,
                metadata={
                    "type": "story_node",
                    "node_id": node_id,
                    "node_type": node.node_type.value,
                    "is_entry": node.is_entry_point,
                    "is_ending": node.is_ending,
                    "rating": node.content_rating.value if node.content_rating else "safe",
                    "premium": node.is_premium
                }
            )
            self.story_documents.append(doc)

        # Index characters
        try:
            char_manager = story.get_character_manager()
            for char_id, character in char_manager.characters.items():
                content = f"Character {character.name}: {character.personality.background_story or 'No background'}"
                content += f" Role: {character.role.value}. Importance: {character.importance_level}/5."
                content += f" Traits: {', '.join(character.personality.traits) if character.personality.traits else 'None'}."
                content += f" Appearance: {character.appearance.physical_description or 'Not described'}."
                content += f" Voice: {character.voice.voice_description or 'Not described'}."

                doc = RAGDocument(
                    id=f"character_{char_id}",
                    content=content,
                    metadata={
                        "type": "character",
                        "character_id": char_id,
                        "character_name": character.name,
                        "role": character.role.value,
                        "importance": character.importance_level
                    }
                )
                self.story_documents.append(doc)
        except Exception as e:
            logger.warning(f"Could not index characters: {e}")

        # Index items
        try:
            item_manager = story.get_item_manager()
            for item_id, item in item_manager.items.items():
                content = f"Item {item.name}: {item.description or 'No description'}"
                content += f" Type: {item.item_type.value}. Rarity: {item.rarity.value}."
                if item.unlocks_choices:
                    content += f" Unlocks choices: {', '.join(item.unlocks_choices)}."
                if item.effects:
                    effects_text = ", ".join([f"{effect.effect_type}: {effect.description}" for effect in item.effects])
                    content += f" Effects: {effects_text}."

                doc = RAGDocument(
                    id=f"item_{item_id}",
                    content=content,
                    metadata={
                        "type": "item",
                        "item_id": item_id,
                        "item_name": item.name,
                        "item_type": item.item_type.value,
                        "rarity": item.rarity.value
                    }
                )
                self.story_documents.append(doc)
        except Exception as e:
            logger.warning(f"Could not index items: {e}")

        # Generate embeddings
        self._generate_embeddings(self.story_documents)

        node_count = len([d for d in self.story_documents if d.metadata.get("type") == "story_node"])
        char_count = len([d for d in self.story_documents if d.metadata.get("type") == "character"])
        item_count = len([d for d in self.story_documents if d.metadata.get("type") == "item"])

        logger.info(f"Indexed for RAG: {node_count} nodes, {char_count} characters, {item_count} items")
    
    def _generate_embeddings(self, documents: List[RAGDocument]):
        """Generate embeddings for documents"""
        if not self.model:
            return
        
        try:
            texts = [doc.content for doc in documents]
            embeddings = self.model.encode(texts)
            
            for doc, embedding in zip(documents, embeddings):
                doc.embedding = embedding
                
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
    
    def search_story_context(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant story context"""
        if not self.model or not self.story_documents:
            return []
        
        return self._search_documents(query, self.story_documents, top_k)
    
    def search_app_knowledge(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Search for relevant app knowledge"""
        if not self.model or not self.app_documents:
            return []
        
        return self._search_documents(query, self.app_documents, top_k)
    
    def _search_documents(self, query: str, documents: List[RAGDocument], top_k: int) -> List[Dict[str, Any]]:
        """Search documents using semantic similarity"""
        try:
            # Generate query embedding
            query_embedding = self.model.encode([query])[0]
            
            # Calculate similarities
            similarities = []
            for doc in documents:
                if doc.embedding is not None:
                    similarity = np.dot(query_embedding, doc.embedding) / (
                        np.linalg.norm(query_embedding) * np.linalg.norm(doc.embedding)
                    )
                    similarities.append((similarity, doc))
            
            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x[0], reverse=True)
            
            results = []
            for similarity, doc in similarities[:top_k]:
                results.append({
                    "content": doc.content,
                    "metadata": doc.metadata,
                    "similarity": float(similarity),
                    "id": doc.id
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    def get_context_for_llm(self, query: str, include_story: bool = True, include_app: bool = True) -> str:
        """Get formatted context for LLM prompt"""
        context_parts = []
        
        if include_story:
            story_results = self.search_story_context(query)
            if story_results:
                context_parts.append("=== STORY CONTEXT ===")
                for result in story_results:
                    context_parts.append(f"• {result['content']}")
        
        if include_app:
            app_results = self.search_app_knowledge(query)
            if app_results:
                context_parts.append("\n=== APP KNOWLEDGE ===")
                for result in app_results:
                    context_parts.append(f"• {result['content']}")
        
        return "\n".join(context_parts)
    
    def save_embeddings_cache(self):
        """Save embeddings to cache file"""
        try:
            cache_data = {
                "story_documents": self.story_documents,
                "app_documents": self.app_documents
            }
            
            self.embeddings_cache_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.embeddings_cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
                
            logger.info("RAG embeddings cache saved")
        except Exception as e:
            logger.error(f"Failed to save embeddings cache: {e}")
    
    def load_embeddings_cache(self):
        """Load embeddings from cache file"""
        try:
            if self.embeddings_cache_path.exists():
                with open(self.embeddings_cache_path, 'rb') as f:
                    cache_data = pickle.load(f)
                
                self.story_documents = cache_data.get("story_documents", [])
                self.app_documents = cache_data.get("app_documents", [])
                
                logger.info("RAG embeddings cache loaded")
                return True
        except Exception as e:
            logger.error(f"Failed to load embeddings cache: {e}")
        
        return False
