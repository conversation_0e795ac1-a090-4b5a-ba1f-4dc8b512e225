"""
Chat Agent - AI Assistant with App Awareness and Control
Provides contextual help and can control app functionality through MCP
"""

import logging
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

from .rag_system import RAGSystem
from utils.lmstudio_client import LMStudioClient

logger = logging.getLogger(__name__)


@dataclass
class ChatMessage:
    """Chat message structure"""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class AppController:
    """Controls app functionality for chat agent"""
    
    def __init__(self):
        self.callbacks: Dict[str, Callable] = {}
        logger.info("App controller initialized")
    
    def register_callback(self, action: str, callback: Callable):
        """Register a callback for an action"""
        self.callbacks[action] = callback
        logger.debug(f"Registered callback for action: {action}")
    
    def execute_action(self, action: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute an app action"""
        if action not in self.callbacks:
            return {"success": False, "error": f"Unknown action: {action}"}
        
        try:
            result = self.callbacks[action](params or {})
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_available_actions(self) -> List[str]:
        """Get list of available actions"""
        return list(self.callbacks.keys())


class ChatAgent:
    """AI chat agent with app awareness and control capabilities"""
    
    def __init__(self, config: Dict[str, Any], lmstudio_client: LMStudioClient, rag_system: RAGSystem):
        self.config = config
        self.lmstudio_client = lmstudio_client
        self.rag_system = rag_system
        self.app_controller = AppController()
        
        self.conversation_history: List[ChatMessage] = []
        self.max_history = config.get('chat', {}).get('max_history', 20)
        
        # System prompt for the chat agent
        self.system_prompt = self._create_system_prompt()
        
        logger.info("Chat agent initialized")
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the chat agent"""
        return """You are an AI assistant for the CYOA Automation System, a tool for creating Choose Your Own Adventure stories and posting them to X (Twitter).

You have access to:
1. The current story web structure and content
2. App functionality and UI knowledge
3. The ability to control certain app functions

Your capabilities:
- Help users navigate the app and find features
- Provide guidance on story creation and editing
- Suggest improvements to stories based on analytics
- Control app settings and create story nodes when requested
- Answer questions about the app's functionality

Available app actions you can perform:
- create_node: Create a new story node
- edit_node: Modify an existing story node
- get_story_stats: Get statistics about the current story
- change_setting: Modify app settings
- navigate_to_tab: Switch to a specific tab
- export_story: Export the current story
- import_story: Import a story file

Always be helpful, concise, and focus on the user's creative goals. When users ask about app functionality, provide specific instructions on where to find features."""
    
    def register_app_actions(self, callbacks: Dict[str, Callable]):
        """Register app action callbacks"""
        for action, callback in callbacks.items():
            self.app_controller.register_callback(action, callback)
        
        logger.info(f"Registered {len(callbacks)} app actions")
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to conversation history"""
        message = ChatMessage(
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        self.conversation_history.append(message)
        
        # Trim history if too long
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
    
    def get_response(self, user_message: str, include_story_context: bool = True) -> str:
        """Get AI response to user message"""
        try:
            # Add user message to history
            self.add_message("user", user_message)
            
            # Check if this is an action request
            action_result = self._check_for_actions(user_message)
            if action_result:
                return action_result
            
            # Get relevant context from RAG
            context = ""
            if include_story_context:
                context = self.rag_system.get_context_for_llm(
                    user_message, 
                    include_story=True, 
                    include_app=True
                )
            
            # Build prompt
            prompt = self._build_prompt(user_message, context)
            
            # Get response from LM Studio
            response = self.lmstudio_client.generate_text(
                prompt=prompt,
                max_tokens=self.config.get('chat', {}).get('max_tokens', 500),
                temperature=self.config.get('chat', {}).get('temperature', 0.7)
            )
            
            # Add assistant response to history
            self.add_message("assistant", response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return "I apologize, but I encountered an error while processing your request. Please try again."
    
    def _check_for_actions(self, message: str) -> Optional[str]:
        """Check if message contains action requests"""
        message_lower = message.lower()
        
        # Simple action detection - could be enhanced with NLP
        action_patterns = {
            "create node": "create_node",
            "add node": "create_node", 
            "new node": "create_node",
            "edit node": "edit_node",
            "modify node": "edit_node",
            "change node": "edit_node",
            "story stats": "get_story_stats",
            "story statistics": "get_story_stats",
            "go to": "navigate_to_tab",
            "switch to": "navigate_to_tab",
            "open": "navigate_to_tab",
            "export story": "export_story",
            "save story": "export_story",
            "import story": "import_story",
            "load story": "import_story"
        }
        
        for pattern, action in action_patterns.items():
            if pattern in message_lower:
                return self._execute_detected_action(action, message)
        
        return None
    
    def _execute_detected_action(self, action: str, original_message: str) -> str:
        """Execute a detected action"""
        try:
            # Extract parameters from message (simplified)
            params = self._extract_action_params(action, original_message)
            
            result = self.app_controller.execute_action(action, params)
            
            if result["success"]:
                return f"✅ Action completed successfully: {action}"
            else:
                return f"❌ Action failed: {result.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return f"❌ Error executing action: {e}"
    
    def _extract_action_params(self, action: str, message: str) -> Dict[str, Any]:
        """Extract parameters for actions from message"""
        params = {}
        message_lower = message.lower()
        
        if action == "navigate_to_tab":
            # Extract tab name
            tab_keywords = {
                "story": "Story Editor",
                "editor": "Story Editor", 
                "graph": "Graph Viewer",
                "character": "Characters",
                "x manager": "📱 X Manager",
                "twitter": "📱 X Manager",
                "analytics": "📊 Analytics",
                "quiz": "🧠 Quiz Creator",
                "voice": "🎤 Voice Studio"
            }
            
            for keyword, tab_name in tab_keywords.items():
                if keyword in message_lower:
                    params["tab_name"] = tab_name
                    break
        
        elif action == "create_node":
            # Try to extract node text
            if "with text" in message_lower:
                # Simple extraction - could be enhanced
                parts = message.split("with text")
                if len(parts) > 1:
                    params["text"] = parts[1].strip().strip('"\'')
        
        return params
    
    def _build_prompt(self, user_message: str, context: str) -> str:
        """Build the complete prompt for the LLM"""
        # Build conversation history
        history_text = ""
        for msg in self.conversation_history[-10:]:  # Last 10 messages
            if msg.role != "system":
                history_text += f"{msg.role.title()}: {msg.content}\n"
        
        prompt = f"""{self.system_prompt}

{context}

Conversation History:
{history_text}

User: {user_message}
Assistant:"""
        
        return prompt
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.info("Chat history cleared")
    
    def export_conversation(self) -> List[Dict[str, Any]]:
        """Export conversation history"""
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat(),
                "metadata": msg.metadata
            }
            for msg in self.conversation_history
        ]
    
    def import_conversation(self, conversation_data: List[Dict[str, Any]]):
        """Import conversation history"""
        self.conversation_history.clear()
        
        for msg_data in conversation_data:
            message = ChatMessage(
                role=msg_data["role"],
                content=msg_data["content"],
                timestamp=datetime.fromisoformat(msg_data["timestamp"]),
                metadata=msg_data.get("metadata", {})
            )
            self.conversation_history.append(message)
        
        logger.info(f"Imported {len(conversation_data)} chat messages")
    
    def get_suggestions(self, current_context: str) -> List[str]:
        """Get contextual suggestions for the user"""
        suggestions = [
            "How do I create a new story node?",
            "Show me the story statistics",
            "Help me improve my story's engagement",
            "How do I add character voices?",
            "Export my story to a file",
            "Switch to the analytics tab"
        ]
        
        # Could enhance with context-aware suggestions
        return suggestions
