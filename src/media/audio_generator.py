"""
Audio Generator - Creates TTS audio for CYOA story nodes
Handles text-to-speech, audio processing, and lip-sync preparation
"""

import logging
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import pyttsx3
import soundfile as sf
import numpy as np
from scipy.io import wavfile
import librosa

from utils.lmstudio_client import LMStudioClient

logger = logging.getLogger(__name__)


class AudioGenerator:
    """Generates audio for CYOA story nodes"""
    
    def __init__(self, config: Dict[str, Any], lmstudio_client: LMStudioClient):
        self.config = config
        self.lmstudio_client = lmstudio_client
        
        self.media_config = config.get('media_generation', {})
        self.sample_rate = self.media_config.get('audio_sample_rate', 22050)
        self.audio_duration = self.media_config.get('video_duration_seconds', 10)
        
        self.videos_dir = Path(config.get('paths', {}).get('videos_dir', 'videos'))
        self.videos_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize TTS engine
        self.tts_engine = None
        self._init_tts_engine()
        
    def _init_tts_engine(self):
        """Initialize the TTS engine"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure TTS settings
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Prefer female voice for variety
                for voice in voices:
                    if 'female' in voice.name.lower() or 'woman' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                else:
                    # Use first available voice
                    self.tts_engine.setProperty('voice', voices[0].id)
            
            # Set speech rate and volume
            self.tts_engine.setProperty('rate', 150)  # Words per minute
            self.tts_engine.setProperty('volume', 0.9)
            
            logger.info("TTS engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            self.tts_engine = None
    
    def generate_audio_for_node(self, story_node, progress_callback: Optional[Callable] = None) -> Optional[str]:
        """Generate audio for a story node"""
        try:
            node_id = story_node.id
            logger.info(f"Generating audio for node: {node_id}")
            
            if progress_callback:
                progress_callback(10, "Generating audio script...")
            
            # Generate audio script
            audio_script = self._generate_audio_script(story_node)
            if not audio_script:
                logger.error("Failed to generate audio script")
                return None
            
            if progress_callback:
                progress_callback(40, "Converting text to speech...")
            
            # Generate TTS audio
            audio_path = self._generate_tts_audio(audio_script, node_id)
            if not audio_path:
                logger.error("Failed to generate TTS audio")
                return None
            
            if progress_callback:
                progress_callback(70, "Processing audio...")
            
            # Process audio (normalize, enhance)
            processed_path = self._process_audio(audio_path, node_id)
            if not processed_path:
                logger.warning("Audio processing failed, using original")
                processed_path = audio_path
            
            if progress_callback:
                progress_callback(100, "Audio generation complete")
            
            logger.info(f"Generated audio: {processed_path}")
            return processed_path
            
        except Exception as e:
            logger.error(f"Error generating audio for node {node_id}: {e}")
            return None
    
    def _generate_audio_script(self, story_node) -> Optional[str]:
        """Generate audio script for the story node"""
        try:
            # Use LMStudio to create natural-sounding script
            response = self.lmstudio_client.generate_audio_script(
                story_node.text,
                story_node.rating == "spicy"
            )
            
            if response.success:
                return response.text
            else:
                logger.error(f"Failed to generate audio script: {response.error}")
                # Fallback to original text
                return story_node.text
                
        except Exception as e:
            logger.error(f"Error generating audio script: {e}")
            # Fallback to original text
            return story_node.text
    
    def _generate_tts_audio(self, script: str, node_id: str) -> Optional[str]:
        """Generate TTS audio from script"""
        try:
            if not self.tts_engine:
                logger.error("TTS engine not available")
                return None
            
            output_path = self.videos_dir / f"{node_id}_audio_raw.wav"
            
            # Clean script for TTS
            clean_script = self._clean_script_for_tts(script)
            
            # Generate audio
            self.tts_engine.save_to_file(clean_script, str(output_path))
            self.tts_engine.runAndWait()
            
            # Verify file was created
            if output_path.exists() and output_path.stat().st_size > 0:
                logger.info(f"Generated TTS audio: {output_path}")
                return str(output_path)
            else:
                logger.error("TTS audio file was not created or is empty")
                return None
                
        except Exception as e:
            logger.error(f"Error generating TTS audio: {e}")
            return None
    
    def _clean_script_for_tts(self, script: str) -> str:
        """Clean script for better TTS pronunciation"""
        try:
            # Remove timing markers
            clean_script = script.replace('[PAUSE]', ', ')
            clean_script = clean_script.replace('[EMPHASIS]', '')
            
            # Remove excessive punctuation
            clean_script = clean_script.replace('...', '.')
            clean_script = clean_script.replace('!!', '!')
            clean_script = clean_script.replace('??', '?')
            
            # Ensure proper sentence endings
            if not clean_script.endswith(('.', '!', '?')):
                clean_script += '.'
            
            return clean_script.strip()
            
        except Exception as e:
            logger.error(f"Error cleaning script: {e}")
            return script
    
    def _process_audio(self, audio_path: str, node_id: str) -> Optional[str]:
        """Process audio for better quality"""
        try:
            output_path = self.videos_dir / f"{node_id}_audio.wav"
            
            # Load audio
            audio_data, sample_rate = librosa.load(audio_path, sr=self.sample_rate)
            
            # Normalize audio
            audio_data = librosa.util.normalize(audio_data)
            
            # Apply noise reduction (simple high-pass filter)
            audio_data = librosa.effects.preemphasis(audio_data)
            
            # Ensure target duration
            target_samples = int(self.audio_duration * self.sample_rate)
            current_samples = len(audio_data)
            
            if current_samples < target_samples:
                # Pad with silence
                padding = target_samples - current_samples
                audio_data = np.pad(audio_data, (0, padding), mode='constant')
            elif current_samples > target_samples:
                # Trim to target duration
                audio_data = audio_data[:target_samples]
            
            # Save processed audio
            sf.write(str(output_path), audio_data, self.sample_rate)
            
            logger.info(f"Processed audio: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            return None
    
    def generate_choice_audio(self, choice, node_id: str, choice_index: int) -> Optional[str]:
        """Generate audio for a specific choice"""
        try:
            choice_id = f"{node_id}_choice_{choice_index}"
            logger.info(f"Generating choice audio: {choice_id}")
            
            # Create choice script
            choice_text = choice.text
            if choice.is_spicy:
                choice_text = f"Spicy option: {choice_text}"
            if hasattr(choice, 'is_premium') and choice.is_premium:
                choice_text = f"Premium option: {choice_text}"
            
            # Generate short audio for choice
            if self.tts_engine:
                output_path = self.videos_dir / f"{choice_id}_audio.wav"
                
                clean_text = self._clean_script_for_tts(choice_text)
                self.tts_engine.save_to_file(clean_text, str(output_path))
                self.tts_engine.runAndWait()
                
                if output_path.exists():
                    return str(output_path)
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating choice audio: {e}")
            return None
    
    def create_lip_sync_data(self, audio_path: str, node_id: str) -> Optional[Dict[str, Any]]:
        """Create lip-sync data for Wav2Lip integration"""
        try:
            # Load audio for analysis
            audio_data, sample_rate = librosa.load(audio_path, sr=self.sample_rate)
            
            # Extract features for lip-sync
            # This is a simplified version - real lip-sync would need more sophisticated analysis
            
            # Get audio envelope
            envelope = librosa.onset.onset_strength(y=audio_data, sr=sample_rate)
            
            # Get tempo and beats
            tempo, beats = librosa.beat.beat_track(y=audio_data, sr=sample_rate)
            
            # Create phoneme-like segments (simplified)
            hop_length = 512
            frame_times = librosa.frames_to_time(np.arange(len(envelope)), sr=sample_rate, hop_length=hop_length)
            
            # Create lip-sync data structure
            lip_sync_data = {
                'audio_path': audio_path,
                'duration': len(audio_data) / sample_rate,
                'sample_rate': sample_rate,
                'tempo': float(tempo),
                'beats': beats.tolist(),
                'envelope': envelope.tolist(),
                'frame_times': frame_times.tolist(),
                'phoneme_segments': self._create_phoneme_segments(envelope, frame_times)
            }
            
            # Save lip-sync data
            lip_sync_path = self.videos_dir / f"{node_id}_lipsync.json"
            import json
            with open(lip_sync_path, 'w') as f:
                json.dump(lip_sync_data, f, indent=2)
            
            logger.info(f"Created lip-sync data: {lip_sync_path}")
            return lip_sync_data
            
        except Exception as e:
            logger.error(f"Error creating lip-sync data: {e}")
            return None
    
    def _create_phoneme_segments(self, envelope: np.ndarray, frame_times: np.ndarray) -> List[Dict[str, Any]]:
        """Create simplified phoneme segments for lip-sync"""
        try:
            segments = []
            
            # Find peaks in the envelope (speech activity)
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(envelope, height=np.mean(envelope), distance=10)
            
            # Create segments between peaks
            for i in range(len(peaks) - 1):
                start_frame = peaks[i]
                end_frame = peaks[i + 1]
                
                if start_frame < len(frame_times) and end_frame < len(frame_times):
                    segment = {
                        'start_time': float(frame_times[start_frame]),
                        'end_time': float(frame_times[end_frame]),
                        'intensity': float(np.mean(envelope[start_frame:end_frame])),
                        'phoneme': 'speech'  # Simplified - real implementation would detect actual phonemes
                    }
                    segments.append(segment)
            
            return segments
            
        except Exception as e:
            logger.error(f"Error creating phoneme segments: {e}")
            return []
    
    def generate_batch_audio(self, story_web, progress_callback: Optional[Callable] = None) -> Dict[str, str]:
        """Generate audio for all nodes in a story web"""
        results = {}
        total_nodes = len(story_web.nodes)
        
        try:
            for i, (node_id, node) in enumerate(story_web.nodes.items()):
                if progress_callback:
                    progress = int((i / total_nodes) * 100)
                    progress_callback(progress, f"Generating audio for {node_id}...")
                
                audio_path = self.generate_audio_for_node(node)
                if audio_path:
                    results[node_id] = audio_path
                    
                    # Also create lip-sync data
                    self.create_lip_sync_data(audio_path, node_id)
                else:
                    logger.error(f"Failed to generate audio for node: {node_id}")
                
                # Small delay to prevent overwhelming the TTS engine
                time.sleep(0.5)
            
            if progress_callback:
                progress_callback(100, "Batch audio generation complete")
            
            logger.info(f"Generated {len(results)} audio files out of {total_nodes} nodes")
            return results
            
        except Exception as e:
            logger.error(f"Error in batch audio generation: {e}")
            return results
    
    def cleanup_temp_files(self, node_id: str):
        """Clean up temporary audio files"""
        try:
            temp_files = [
                self.videos_dir / f"{node_id}_audio_raw.wav",
            ]
            
            for temp_file in temp_files:
                if temp_file.exists():
                    temp_file.unlink()
                    logger.debug(f"Cleaned up temp file: {temp_file}")
                    
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
    
    def get_audio_info(self, audio_path: str) -> Dict[str, Any]:
        """Get information about an audio file"""
        try:
            audio_data, sample_rate = librosa.load(audio_path)
            
            return {
                'duration': len(audio_data) / sample_rate,
                'sample_rate': sample_rate,
                'channels': 1,  # librosa loads as mono by default
                'file_size': Path(audio_path).stat().st_size,
                'format': Path(audio_path).suffix.lower()
            }
            
        except Exception as e:
            logger.error(f"Error getting audio info: {e}")
            return {}
