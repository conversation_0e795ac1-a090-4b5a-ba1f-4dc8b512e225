"""
ComfyUI Client - Interface for ComfyUI video and image generation
Handles workflow execution, queue management, and media creation
"""

import json
import requests
import time
import logging
import uuid
try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    websocket = None
import threading
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import base64
import io
from PIL import Image

logger = logging.getLogger(__name__)


class ComfyUIClient:
    """Client for communicating with ComfyUI server"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8188", timeout: int = 300):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.client_id = str(uuid.uuid4())
        self.ws = None
        self.ws_thread = None
        self.execution_callbacks = {}
        
    def is_available(self) -> bool:
        """Check if ComfyUI server is available"""
        try:
            response = self.session.get(f"{self.base_url}/system_stats", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"ComfyUI server not available: {e}")
            return False
    
    def connect_websocket(self):
        """Connect to ComfyUI WebSocket for real-time updates"""
        if not WEBSOCKET_AVAILABLE:
            logger.warning("WebSocket not available, real-time updates disabled")
            return

        try:
            ws_url = f"ws://{self.base_url.split('://')[-1]}/ws?clientId={self.client_id}"
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_message=self._on_ws_message,
                on_error=self._on_ws_error,
                on_close=self._on_ws_close
            )
            
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            logger.info("Connected to ComfyUI WebSocket")
            
        except Exception as e:
            logger.error(f"Failed to connect to ComfyUI WebSocket: {e}")
    
    def _on_ws_message(self, ws, message):
        """Handle WebSocket messages"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'executing':
                node_id = data.get('data', {}).get('node')
                prompt_id = data.get('data', {}).get('prompt_id')
                
                if prompt_id in self.execution_callbacks:
                    callback = self.execution_callbacks[prompt_id]
                    if node_id is None:  # Execution finished
                        callback('completed', {'prompt_id': prompt_id})
                    else:
                        callback('executing', {'node_id': node_id, 'prompt_id': prompt_id})
            
            elif msg_type == 'progress':
                prompt_id = data.get('data', {}).get('prompt_id')
                if prompt_id in self.execution_callbacks:
                    callback = self.execution_callbacks[prompt_id]
                    callback('progress', data.get('data', {}))
                    
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    def _on_ws_error(self, ws, error):
        """Handle WebSocket errors"""
        logger.error(f"ComfyUI WebSocket error: {error}")
    
    def _on_ws_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        logger.info("ComfyUI WebSocket connection closed")
    
    def load_workflow(self, workflow_path: str) -> Optional[Dict[str, Any]]:
        """Load workflow from JSON file"""
        try:
            with open(workflow_path, 'r', encoding='utf-8') as f:
                workflow = json.load(f)
            logger.info(f"Loaded workflow from {workflow_path}")
            return workflow
        except Exception as e:
            logger.error(f"Error loading workflow from {workflow_path}: {e}")
            return None
    
    def queue_prompt(self, workflow: Dict[str, Any], 
                    callback: Optional[Callable] = None) -> Optional[str]:
        """Queue a workflow for execution"""
        try:
            prompt_data = {
                "prompt": workflow.get("nodes", {}),
                "client_id": self.client_id
            }
            
            response = self.session.post(
                f"{self.base_url}/prompt",
                json=prompt_data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                prompt_id = result.get("prompt_id")
                
                if callback and prompt_id:
                    self.execution_callbacks[prompt_id] = callback
                
                logger.info(f"Queued prompt with ID: {prompt_id}")
                return prompt_id
            else:
                logger.error(f"Failed to queue prompt: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error queuing prompt: {e}")
            return None
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        try:
            response = self.session.get(f"{self.base_url}/queue", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get queue status: {response.status_code}")
                return {}
        except Exception as e:
            logger.error(f"Error getting queue status: {e}")
            return {}
    
    def get_history(self, prompt_id: str) -> Optional[Dict[str, Any]]:
        """Get execution history for a prompt"""
        try:
            response = self.session.get(f"{self.base_url}/history/{prompt_id}", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get history: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting history: {e}")
            return None
    
    def get_image(self, filename: str, subfolder: str = "", folder_type: str = "output") -> Optional[Image.Image]:
        """Download generated image"""
        try:
            params = {
                "filename": filename,
                "type": folder_type
            }
            if subfolder:
                params["subfolder"] = subfolder
            
            response = self.session.get(f"{self.base_url}/view", params=params, timeout=30)
            
            if response.status_code == 200:
                image = Image.open(io.BytesIO(response.content))
                logger.info(f"Downloaded image: {filename}")
                return image
            else:
                logger.error(f"Failed to download image: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading image {filename}: {e}")
            return None
    
    def wait_for_completion(self, prompt_id: str, timeout: int = None) -> bool:
        """Wait for prompt execution to complete"""
        if timeout is None:
            timeout = self.timeout
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            history = self.get_history(prompt_id)
            if history and prompt_id in history:
                logger.info(f"Prompt {prompt_id} completed")
                return True
            
            time.sleep(2)
        
        logger.error(f"Timeout waiting for prompt {prompt_id}")
        return False
    
    def generate_image(self, workflow_path: str, prompt: str, 
                      output_path: str, **kwargs) -> bool:
        """Generate image using workflow"""
        try:
            # Load workflow
            workflow = self.load_workflow(workflow_path)
            if not workflow:
                return False
            
            # Update workflow with parameters
            self._update_workflow_prompt(workflow, prompt)
            self._update_workflow_parameters(workflow, kwargs)
            
            # Queue for execution
            prompt_id = self.queue_prompt(workflow)
            if not prompt_id:
                return False
            
            # Wait for completion
            if not self.wait_for_completion(prompt_id):
                return False
            
            # Get results
            history = self.get_history(prompt_id)
            if not history or prompt_id not in history:
                logger.error("No history found for completed prompt")
                return False
            
            # Find output images
            outputs = history[prompt_id].get("outputs", {})
            for node_id, node_output in outputs.items():
                if "images" in node_output:
                    for image_info in node_output["images"]:
                        filename = image_info["filename"]
                        subfolder = image_info.get("subfolder", "")
                        
                        # Download image
                        image = self.get_image(filename, subfolder)
                        if image:
                            # Save to output path
                            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                            image.save(output_path)
                            logger.info(f"Saved generated image to {output_path}")
                            return True
            
            logger.error("No output images found")
            return False
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return False
    
    def _update_workflow_prompt(self, workflow: Dict[str, Any], prompt: str):
        """Update workflow with text prompt"""
        nodes = workflow.get("nodes", {})
        
        # Look for text input nodes
        for node_id, node in nodes.items():
            if node.get("class_type") == "CLIPTextEncode":
                inputs = node.get("inputs", {})
                if "text" in inputs and inputs["text"] == "PROMPT_PLACEHOLDER":
                    inputs["text"] = prompt
                    logger.debug(f"Updated prompt in node {node_id}")
    
    def _update_workflow_parameters(self, workflow: Dict[str, Any], params: Dict[str, Any]):
        """Update workflow with parameters"""
        nodes = workflow.get("nodes", {})
        
        for param_name, param_value in params.items():
            # Update specific parameters based on name
            if param_name == "width" or param_name == "height":
                for node_id, node in nodes.items():
                    if node.get("class_type") == "EmptyLatentImage":
                        inputs = node.get("inputs", {})
                        if param_name in inputs:
                            inputs[param_name] = param_value
            
            elif param_name == "steps":
                for node_id, node in nodes.items():
                    if node.get("class_type") == "KSampler":
                        inputs = node.get("inputs", {})
                        if "steps" in inputs:
                            inputs["steps"] = param_value
            
            elif param_name == "cfg":
                for node_id, node in nodes.items():
                    if node.get("class_type") == "KSampler":
                        inputs = node.get("inputs", {})
                        if "cfg" in inputs:
                            inputs["cfg"] = param_value
    
    def get_models(self) -> Dict[str, List[str]]:
        """Get available models"""
        try:
            response = self.session.get(f"{self.base_url}/object_info", timeout=10)
            if response.status_code == 200:
                object_info = response.json()
                models = {}
                
                # Extract model information
                for node_type, node_info in object_info.items():
                    if "input" in node_info:
                        for input_name, input_info in node_info["input"]["required"].items():
                            if isinstance(input_info, list) and len(input_info) > 0:
                                if isinstance(input_info[0], list):
                                    models[f"{node_type}.{input_name}"] = input_info[0]
                
                return models
            else:
                logger.error(f"Failed to get models: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting models: {e}")
            return {}
    
    def interrupt_execution(self):
        """Interrupt current execution"""
        try:
            response = self.session.post(f"{self.base_url}/interrupt", timeout=10)
            if response.status_code == 200:
                logger.info("Execution interrupted")
                return True
            else:
                logger.error(f"Failed to interrupt execution: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error interrupting execution: {e}")
            return False
    
    def clear_queue(self):
        """Clear the execution queue"""
        try:
            response = self.session.post(f"{self.base_url}/queue", 
                                       json={"clear": True}, timeout=10)
            if response.status_code == 200:
                logger.info("Queue cleared")
                return True
            else:
                logger.error(f"Failed to clear queue: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error clearing queue: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from ComfyUI"""
        if WEBSOCKET_AVAILABLE and self.ws:
            self.ws.close()
        
        # Clear callbacks
        self.execution_callbacks.clear()
        
        logger.info("Disconnected from ComfyUI")
