"""
Story Analytics System - Track engagement and monetization across story paths
Analyzes X post performance to optimize story flow and revenue
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class PostMetrics:
    """Metrics for a single X post"""
    post_id: str
    node_id: str
    timestamp: datetime
    
    # Engagement metrics
    views: int = 0
    likes: int = 0
    retweets: int = 0
    replies: int = 0
    clicks: int = 0
    
    # Monetization metrics
    subscription_conversions: int = 0
    revenue_generated: float = 0.0
    
    # Path metrics
    choice_clicks: Dict[str, int] = field(default_factory=dict)  # choice_id -> click_count
    path_completions: int = 0
    drop_off_rate: float = 0.0


@dataclass
class PathAnalytics:
    """Analytics for a story path"""
    path_nodes: List[str]
    total_users: int
    completion_rate: float
    average_engagement: float
    total_revenue: float
    drop_off_points: List[Tuple[str, float]]  # (node_id, drop_off_percentage)


@dataclass
class StoryPerformance:
    """Overall story performance metrics"""
    story_id: str
    total_views: int
    total_engagement: int
    total_revenue: float
    conversion_rate: float
    most_popular_path: List[str]
    least_popular_path: List[str]
    optimization_suggestions: List[str]


class StoryAnalytics:
    """Analyzes story performance and provides optimization insights"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.analytics_dir = Path("data/analytics")
        self.analytics_dir.mkdir(parents=True, exist_ok=True)
        
        # X API client for fetching metrics
        self.x_client = None  # Will be initialized when needed
        
    def collect_post_metrics(self, story_id: str, post_mappings: Dict[str, str]) -> Dict[str, PostMetrics]:
        """
        Collect metrics for all posts in a story
        post_mappings: {node_id: post_id}
        """
        try:
            metrics = {}
            
            for node_id, post_id in post_mappings.items():
                post_metrics = self._fetch_post_metrics(post_id, node_id)
                if post_metrics:
                    metrics[node_id] = post_metrics
            
            # Save metrics
            self._save_metrics(story_id, metrics)
            
            logger.info(f"Collected metrics for {len(metrics)} posts in story {story_id}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting post metrics: {e}")
            return {}
    
    def _fetch_post_metrics(self, post_id: str, node_id: str) -> Optional[PostMetrics]:
        """Fetch metrics for a single post from X API"""
        try:
            # This would use X API v2 to get post metrics
            # For now, we'll simulate with sample data
            
            # In real implementation:
            # response = self.x_client.get_tweet_metrics(post_id)
            
            # Simulated metrics for demo
            import random
            base_views = random.randint(100, 10000)
            
            metrics = PostMetrics(
                post_id=post_id,
                node_id=node_id,
                timestamp=datetime.now(),
                views=base_views,
                likes=int(base_views * random.uniform(0.02, 0.08)),
                retweets=int(base_views * random.uniform(0.005, 0.02)),
                replies=int(base_views * random.uniform(0.01, 0.03)),
                clicks=int(base_views * random.uniform(0.1, 0.3)),
                subscription_conversions=int(base_views * random.uniform(0.001, 0.005)),
                revenue_generated=base_views * random.uniform(0.01, 0.05)
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error fetching metrics for post {post_id}: {e}")
            return None
    
    def analyze_story_paths(self, story, metrics: Dict[str, PostMetrics]) -> Dict[str, PathAnalytics]:
        """Analyze performance of different story paths"""
        try:
            path_analytics = {}
            
            # Find all possible paths through the story
            all_paths = self._find_all_paths(story)
            
            for i, path in enumerate(all_paths):
                path_id = f"path_{i}"
                
                # Calculate path metrics
                path_metrics = self._calculate_path_metrics(path, metrics)
                path_analytics[path_id] = path_metrics
            
            return path_analytics
            
        except Exception as e:
            logger.error(f"Error analyzing story paths: {e}")
            return {}
    
    def _find_all_paths(self, story) -> List[List[str]]:
        """Find all possible paths through the story"""
        paths = []
        
        def dfs(node_id, current_path, visited):
            if node_id in visited:
                return  # Avoid cycles
            
            visited.add(node_id)
            current_path.append(node_id)
            
            if node_id not in story.nodes:
                return
            
            node = story.nodes[node_id]
            
            # If this is an ending node, save the path
            if node.is_ending or not node.choices:
                paths.append(current_path.copy())
            else:
                # Continue down each choice
                for choice in node.choices:
                    dfs(choice.target_node_id, current_path, visited.copy())
            
            current_path.pop()
        
        # Start from each entry point
        for entry_point in story.entry_points:
            dfs(entry_point, [], set())
        
        return paths
    
    def _calculate_path_metrics(self, path: List[str], metrics: Dict[str, PostMetrics]) -> PathAnalytics:
        """Calculate metrics for a specific path"""
        try:
            total_users = 0
            total_engagement = 0
            total_revenue = 0.0
            drop_off_points = []
            
            for i, node_id in enumerate(path):
                if node_id in metrics:
                    node_metrics = metrics[node_id]
                    
                    if i == 0:  # First node sets baseline
                        total_users = node_metrics.views
                    
                    total_engagement += (node_metrics.likes + node_metrics.retweets + 
                                       node_metrics.replies + node_metrics.clicks)
                    total_revenue += node_metrics.revenue_generated
                    
                    # Calculate drop-off if not the first node
                    if i > 0 and total_users > 0:
                        drop_off_rate = 1 - (node_metrics.views / total_users)
                        if drop_off_rate > 0.3:  # Significant drop-off
                            drop_off_points.append((node_id, drop_off_rate))
            
            completion_rate = 1.0
            if len(path) > 1 and total_users > 0:
                final_node_id = path[-1]
                if final_node_id in metrics:
                    completion_rate = metrics[final_node_id].views / total_users
            
            average_engagement = total_engagement / len(path) if path else 0
            
            return PathAnalytics(
                path_nodes=path,
                total_users=total_users,
                completion_rate=completion_rate,
                average_engagement=average_engagement,
                total_revenue=total_revenue,
                drop_off_points=drop_off_points
            )
            
        except Exception as e:
            logger.error(f"Error calculating path metrics: {e}")
            return PathAnalytics(path, 0, 0.0, 0.0, 0.0, [])
    
    def generate_optimization_report(self, story, metrics: Dict[str, PostMetrics], 
                                   path_analytics: Dict[str, PathAnalytics]) -> StoryPerformance:
        """Generate comprehensive optimization report"""
        try:
            # Calculate overall metrics
            total_views = sum(m.views for m in metrics.values())
            total_engagement = sum(m.likes + m.retweets + m.replies + m.clicks for m in metrics.values())
            total_revenue = sum(m.revenue_generated for m in metrics.values())
            total_conversions = sum(m.subscription_conversions for m in metrics.values())
            
            conversion_rate = total_conversions / total_views if total_views > 0 else 0
            
            # Find best and worst performing paths
            best_path = max(path_analytics.values(), key=lambda p: p.total_revenue) if path_analytics else None
            worst_path = min(path_analytics.values(), key=lambda p: p.completion_rate) if path_analytics else None
            
            # Generate optimization suggestions
            suggestions = self._generate_optimization_suggestions(story, metrics, path_analytics)
            
            performance = StoryPerformance(
                story_id=story.metadata.get('id', 'unknown'),
                total_views=total_views,
                total_engagement=total_engagement,
                total_revenue=total_revenue,
                conversion_rate=conversion_rate,
                most_popular_path=best_path.path_nodes if best_path else [],
                least_popular_path=worst_path.path_nodes if worst_path else [],
                optimization_suggestions=suggestions
            )
            
            return performance
            
        except Exception as e:
            logger.error(f"Error generating optimization report: {e}")
            return StoryPerformance("error", 0, 0, 0.0, 0.0, [], [], [])
    
    def _generate_optimization_suggestions(self, story, metrics: Dict[str, PostMetrics], 
                                         path_analytics: Dict[str, PathAnalytics]) -> List[str]:
        """Generate AI-powered optimization suggestions"""
        suggestions = []
        
        try:
            # Analyze drop-off points
            high_dropoff_nodes = []
            for path_analytics_data in path_analytics.values():
                for node_id, drop_rate in path_analytics_data.drop_off_points:
                    if drop_rate > 0.5:
                        high_dropoff_nodes.append((node_id, drop_rate))
            
            if high_dropoff_nodes:
                suggestions.append(f"High drop-off detected at nodes: {[n[0] for n in high_dropoff_nodes[:3]]}. Consider revising content or choices.")
            
            # Analyze engagement patterns
            low_engagement_nodes = [node_id for node_id, m in metrics.items() 
                                  if (m.likes + m.retweets + m.replies) < m.views * 0.02]
            
            if low_engagement_nodes:
                suggestions.append(f"Low engagement on nodes: {low_engagement_nodes[:3]}. Try more compelling content or interactive elements.")
            
            # Revenue optimization
            high_revenue_nodes = [node_id for node_id, m in metrics.items() 
                                if m.revenue_generated > sum(metrics[n].revenue_generated for n in metrics) / len(metrics) * 1.5]
            
            if high_revenue_nodes:
                suggestions.append(f"High-revenue nodes: {high_revenue_nodes}. Consider similar content patterns for other nodes.")
            
            # Path optimization
            if path_analytics:
                best_path = max(path_analytics.values(), key=lambda p: p.completion_rate)
                if best_path.completion_rate > 0.7:
                    suggestions.append(f"Path with {len(best_path.path_nodes)} nodes has {best_path.completion_rate:.1%} completion rate. Consider similar structure for other paths.")
            
            # Choice optimization
            choice_performance = {}
            for node_id, node_metrics in metrics.items():
                if node_id in story.nodes:
                    node = story.nodes[node_id]
                    for choice in node.choices:
                        choice_clicks = node_metrics.choice_clicks.get(choice.id, 0)
                        choice_performance[choice.text] = choice_clicks
            
            if choice_performance:
                popular_choices = sorted(choice_performance.items(), key=lambda x: x[1], reverse=True)[:3]
                suggestions.append(f"Most popular choice types: {[c[0][:30] for c in popular_choices]}. Use similar phrasing in other nodes.")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
            return ["Error generating suggestions - check logs for details"]
    
    def suggest_story_edits(self, story, performance: StoryPerformance) -> List[Dict[str, Any]]:
        """Suggest specific edits to improve story performance (within 5 edit limit)"""
        edit_suggestions = []
        
        try:
            # Priority 1: Fix high drop-off nodes
            if performance.least_popular_path:
                worst_node = performance.least_popular_path[0] if performance.least_popular_path else None
                if worst_node and worst_node in story.nodes:
                    edit_suggestions.append({
                        'type': 'content_revision',
                        'node_id': worst_node,
                        'priority': 'high',
                        'suggestion': 'Revise content to be more engaging - current version has high drop-off',
                        'estimated_impact': 'High - could improve completion rate by 20-30%'
                    })
            
            # Priority 2: Optimize choice text
            edit_suggestions.append({
                'type': 'choice_optimization',
                'priority': 'medium',
                'suggestion': 'Update choice text to match high-performing patterns',
                'estimated_impact': 'Medium - could improve click-through by 10-15%'
            })
            
            # Priority 3: Add monetization hooks
            edit_suggestions.append({
                'type': 'monetization',
                'priority': 'medium',
                'suggestion': 'Add subscription prompts at high-engagement nodes',
                'estimated_impact': 'Medium - could improve conversion rate by 5-10%'
            })
            
            return edit_suggestions[:5]  # Respect 5 edit limit
            
        except Exception as e:
            logger.error(f"Error suggesting edits: {e}")
            return []
    
    def _save_metrics(self, story_id: str, metrics: Dict[str, PostMetrics]):
        """Save metrics to file"""
        try:
            metrics_file = self.analytics_dir / f"{story_id}_metrics.json"
            
            # Convert to serializable format
            serializable_metrics = {}
            for node_id, metric in metrics.items():
                serializable_metrics[node_id] = {
                    'post_id': metric.post_id,
                    'node_id': metric.node_id,
                    'timestamp': metric.timestamp.isoformat(),
                    'views': metric.views,
                    'likes': metric.likes,
                    'retweets': metric.retweets,
                    'replies': metric.replies,
                    'clicks': metric.clicks,
                    'subscription_conversions': metric.subscription_conversions,
                    'revenue_generated': metric.revenue_generated,
                    'choice_clicks': metric.choice_clicks
                }
            
            with open(metrics_file, 'w') as f:
                json.dump(serializable_metrics, f, indent=2)
            
            logger.info(f"Saved metrics for story {story_id}")
            
        except Exception as e:
            logger.error(f"Error saving metrics: {e}")
    
    def load_metrics(self, story_id: str) -> Dict[str, PostMetrics]:
        """Load metrics from file"""
        try:
            metrics_file = self.analytics_dir / f"{story_id}_metrics.json"
            
            if not metrics_file.exists():
                return {}
            
            with open(metrics_file) as f:
                data = json.load(f)
            
            metrics = {}
            for node_id, metric_data in data.items():
                metrics[node_id] = PostMetrics(
                    post_id=metric_data['post_id'],
                    node_id=metric_data['node_id'],
                    timestamp=datetime.fromisoformat(metric_data['timestamp']),
                    views=metric_data['views'],
                    likes=metric_data['likes'],
                    retweets=metric_data['retweets'],
                    replies=metric_data['replies'],
                    clicks=metric_data['clicks'],
                    subscription_conversions=metric_data['subscription_conversions'],
                    revenue_generated=metric_data['revenue_generated'],
                    choice_clicks=metric_data.get('choice_clicks', {})
                )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error loading metrics: {e}")
            return {}
