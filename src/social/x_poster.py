"""
X (Twitter) <PERSON><PERSON> - <PERSON><PERSON> posting CYOA content to X
Supports video ads for clickable choices and fallback link posting
"""

import logging
import time
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import tweepy
from datetime import datetime, timedelta
import os

logger = logging.getLogger(__name__)


class XPoster:
    """<PERSON><PERSON> posting CYOA stories to X (Twitter)"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.social_config = config.get('social_media', {})
        
        # X API configuration
        self.api_key = os.getenv('X_API_KEY')
        self.api_secret = os.getenv('X_API_SECRET')
        self.access_token = os.getenv('X_ACCESS_TOKEN')
        self.access_token_secret = os.getenv('X_ACCESS_TOKEN_SECRET')
        self.bearer_token = os.getenv('X_BEARER_TOKEN')
        
        # Posting configuration
        self.character_limit = self.social_config.get('character_limit', 280)
        self.post_delay = self.social_config.get('post_delay_seconds', 10)
        self.retry_attempts = self.social_config.get('retry_attempts', 3)
        self.retry_delay = self.social_config.get('retry_delay_minutes', 15)
        
        # Initialize API clients
        self.api_v1 = None
        self.api_v2 = None
        self._init_api_clients()
        
        # Track posted content
        self.posted_nodes = {}
        self.post_urls = {}
        
    def _init_api_clients(self):
        """Initialize X API clients"""
        try:
            if not all([self.api_key, self.api_secret, self.access_token, self.access_token_secret]):
                logger.error("Missing X API credentials")
                return
            
            # Initialize API v1.1 for media upload
            auth = tweepy.OAuthHandler(self.api_key, self.api_secret)
            auth.set_access_token(self.access_token, self.access_token_secret)
            self.api_v1 = tweepy.API(auth, wait_on_rate_limit=True)
            
            # Initialize API v2 for posting
            self.api_v2 = tweepy.Client(
                bearer_token=self.bearer_token,
                consumer_key=self.api_key,
                consumer_secret=self.api_secret,
                access_token=self.access_token,
                access_token_secret=self.access_token_secret,
                wait_on_rate_limit=True
            )
            
            # Test authentication
            try:
                user = self.api_v2.get_me()
                logger.info(f"X API authenticated for user: {user.data.username}")
            except Exception as e:
                logger.error(f"X API authentication test failed: {e}")
                
        except Exception as e:
            logger.error(f"Failed to initialize X API clients: {e}")
    
    def is_available(self) -> bool:
        """Check if X API is available"""
        return self.api_v1 is not None and self.api_v2 is not None
    
    def post_story_web(self, story_web, video_paths: Dict[str, str], 
                      progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """Post entire story web to X"""
        try:
            if not self.is_available():
                logger.error("X API not available")
                return {}
            
            logger.info(f"Posting story web: {story_web.metadata.get('title', 'Untitled')}")
            
            results = {}
            total_nodes = len(story_web.nodes)
            
            # Post in reverse topological order (endings first)
            posting_order = self._get_posting_order(story_web)
            
            for i, node_id in enumerate(posting_order):
                if progress_callback:
                    progress = int((i / total_nodes) * 100)
                    progress_callback(progress, f"Posting node {node_id}...")
                
                node = story_web.nodes[node_id]
                video_path = video_paths.get(node_id)
                
                post_result = self.post_node(story_web, node, video_path)
                if post_result:
                    results[node_id] = post_result
                    logger.info(f"Posted node {node_id}: {post_result.get('url', 'No URL')}")
                else:
                    logger.error(f"Failed to post node: {node_id}")
                
                # Delay between posts
                if i < len(posting_order) - 1:  # Don't delay after last post
                    time.sleep(self.post_delay)
            
            # Pin entry point posts
            self._pin_entry_posts(story_web, results)
            
            if progress_callback:
                progress_callback(100, "Story posting complete")
            
            logger.info(f"Posted {len(results)} out of {total_nodes} nodes")
            return results
            
        except Exception as e:
            logger.error(f"Error posting story web: {e}")
            return {}
    
    def post_node(self, story_web, node, video_path: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Post a single story node"""
        try:
            logger.info(f"Posting node: {node.id}")
            
            # Create post content
            post_content = self._create_post_content(story_web, node)
            
            # Upload media if available
            media_ids = []
            if video_path and Path(video_path).exists():
                media_id = self._upload_video(video_path)
                if media_id:
                    media_ids.append(media_id)
            
            # Determine posting strategy
            use_video_ads = self._should_use_video_ads(node)
            
            if use_video_ads and media_ids:
                # Try to create clickable video ad
                post_result = self._post_with_video_ad(post_content, media_ids[0], node, story_web)
            else:
                # Fallback to regular post with links
                post_result = self._post_with_links(post_content, media_ids, node, story_web)
            
            if post_result:
                # Store post information
                self.posted_nodes[node.id] = post_result
                self.post_urls[node.id] = post_result.get('url')
                
                # Save post log
                self._log_post(node, post_result)
            
            return post_result
            
        except Exception as e:
            logger.error(f"Error posting node {node.id}: {e}")
            return None
    
    def _create_post_content(self, story_web, node) -> str:
        """Create post content for a node"""
        try:
            content_parts = []
            
            # Add content warnings
            if node.rating == "spicy":
                content_parts.append("[Spicy Content]")
            if hasattr(node, 'is_premium') and node.is_premium:
                content_parts.append("[Premium]")
            
            # Add main story text
            story_text = node.text
            
            # Add score for death endings
            if node.ending_type and node.ending_type.value == "death" and node.score:
                story_text += f" Score: {node.score:.1f}%"
            
            content_parts.append(story_text)
            
            # Add hashtags
            hashtags = ["#CYOA", "#ChooseYourOwnAdventure"]
            if node.rating == "spicy":
                hashtags.append("#Spicy")
            if node.class_context:
                hashtags.append(f"#{node.class_context}")
            
            content_parts.append(" ".join(hashtags))
            
            # Join content
            full_content = "\n\n".join(content_parts)
            
            # Truncate if too long
            if len(full_content) > self.character_limit:
                # Calculate space for hashtags and ellipsis
                hashtag_text = " ".join(hashtags)
                available_space = self.character_limit - len(hashtag_text) - 10  # 10 for spacing and ellipsis
                
                # Truncate story text
                truncated_story = story_text[:available_space] + "..."
                
                content_parts = []
                if node.rating == "spicy":
                    content_parts.append("[Spicy Content]")
                if hasattr(node, 'is_premium') and node.is_premium:
                    content_parts.append("[Premium]")
                
                content_parts.extend([truncated_story, hashtag_text])
                full_content = "\n\n".join(content_parts)
            
            return full_content
            
        except Exception as e:
            logger.error(f"Error creating post content: {e}")
            return node.text[:self.character_limit]
    
    def _should_use_video_ads(self, node) -> bool:
        """Determine if video ads should be used for this node"""
        # For now, return False as video ads require special X API access
        # This can be enabled when ad API access is available
        return False
    
    def _post_with_video_ad(self, content: str, media_id: str, node, story_web) -> Optional[Dict[str, Any]]:
        """Post with clickable video ad (requires X Ads API)"""
        try:
            # This would require X Ads API integration
            # For now, fall back to regular posting
            logger.info("Video ads not implemented, falling back to regular post")
            return self._post_with_links(content, [media_id], node, story_web)
            
        except Exception as e:
            logger.error(f"Error posting video ad: {e}")
            return None
    
    def _post_with_links(self, content: str, media_ids: List[str], node, story_web) -> Optional[Dict[str, Any]]:
        """Post with choice links above video"""
        try:
            # Add choice links to content
            choice_links = self._create_choice_links(node, story_web)
            
            if choice_links:
                # Add choice links above the main content
                content_with_links = f"{choice_links}\n\n{content}"
                
                # Check length again
                if len(content_with_links) > self.character_limit:
                    # Prioritize links, truncate main content
                    available_space = self.character_limit - len(choice_links) - 10
                    truncated_content = content[:available_space] + "..."
                    content_with_links = f"{choice_links}\n\n{truncated_content}"
            else:
                content_with_links = content
            
            # Post tweet
            tweet_data = {"text": content_with_links}
            if media_ids:
                tweet_data["media"] = {"media_ids": media_ids}
            
            response = self.api_v2.create_tweet(**tweet_data)
            
            if response.data:
                tweet_id = response.data['id']
                tweet_url = f"https://twitter.com/user/status/{tweet_id}"
                
                return {
                    'tweet_id': tweet_id,
                    'url': tweet_url,
                    'content': content_with_links,
                    'media_ids': media_ids,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                logger.error("No tweet data in response")
                return None
                
        except Exception as e:
            logger.error(f"Error posting with links: {e}")
            return None
    
    def _create_choice_links(self, node, story_web) -> str:
        """Create choice links for a node"""
        try:
            if not node.choices:
                return ""
            
            choice_links = []
            
            for i, choice in enumerate(node.choices[:3]):  # Limit to 3 choices for space
                # Get target node URL if already posted
                target_url = self.post_urls.get(choice.target_node_id)
                
                if target_url:
                    # Format choice with link
                    choice_text = choice.text
                    if choice.is_spicy:
                        choice_text = f"[Spicy] {choice_text}"
                    if hasattr(choice, 'is_premium') and choice.is_premium:
                        choice_text = f"[Premium] {choice_text}"
                    
                    # Truncate choice text if too long
                    if len(choice_text) > 40:
                        choice_text = choice_text[:37] + "..."
                    
                    choice_links.append(f"{i+1}. {choice_text}: {target_url}")
                else:
                    # Placeholder for future post
                    choice_text = choice.text[:30] + "..." if len(choice.text) > 30 else choice.text
                    choice_links.append(f"{i+1}. {choice_text}: [Link coming soon]")
            
            if choice_links:
                return "Choices:\n" + "\n".join(choice_links)
            
            return ""
            
        except Exception as e:
            logger.error(f"Error creating choice links: {e}")
            return ""
    
    def _upload_video(self, video_path: str) -> Optional[str]:
        """Upload video to X"""
        try:
            logger.info(f"Uploading video: {video_path}")
            
            # Check file size (X limit is 512MB for videos)
            file_size = Path(video_path).stat().st_size
            max_size = 512 * 1024 * 1024  # 512MB
            
            if file_size > max_size:
                logger.error(f"Video file too large: {file_size / (1024*1024):.1f}MB > 512MB")
                return None
            
            # Upload video
            media = self.api_v1.media_upload(
                filename=video_path,
                media_category='tweet_video'
            )
            
            # Wait for processing
            media_id = media.media_id
            processing_info = media.processing_info
            
            while processing_info and processing_info.get('state') == 'in_progress':
                time.sleep(processing_info.get('check_after_secs', 5))
                
                # Check processing status
                media = self.api_v1.get_media_upload_status(media_id)
                processing_info = media.processing_info
            
            if processing_info and processing_info.get('state') == 'succeeded':
                logger.info(f"Video uploaded successfully: {media_id}")
                return str(media_id)
            else:
                logger.error(f"Video processing failed: {processing_info}")
                return None
                
        except Exception as e:
            logger.error(f"Error uploading video: {e}")
            return None
    
    def _get_posting_order(self, story_web) -> List[str]:
        """Get optimal posting order (endings first, then work backwards)"""
        try:
            import networkx as nx
            
            # Create reverse graph
            reverse_graph = story_web.graph.reverse()
            
            # Get topological order (endings first)
            try:
                topo_order = list(nx.topological_sort(reverse_graph))
                return topo_order
            except nx.NetworkXError:
                # Fallback: endings first, then others
                endings = [node_id for node_id in story_web.endings]
                others = [node_id for node_id in story_web.nodes.keys() if node_id not in endings]
                return endings + others
                
        except Exception as e:
            logger.error(f"Error determining posting order: {e}")
            # Simple fallback
            return list(story_web.nodes.keys())
    
    def _pin_entry_posts(self, story_web, results: Dict[str, Any]):
        """Pin entry point posts to profile"""
        try:
            # X API v2 doesn't support pinning tweets directly
            # This would need to be done manually or through additional API calls
            logger.info("Entry post pinning not implemented (requires manual action)")
            
        except Exception as e:
            logger.error(f"Error pinning entry posts: {e}")
    
    def _log_post(self, node, post_result: Dict[str, Any]):
        """Log post information"""
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'node_id': node.id,
                'tweet_id': post_result.get('tweet_id'),
                'url': post_result.get('url'),
                'content_length': len(post_result.get('content', '')),
                'media_count': len(post_result.get('media_ids', [])),
                'rating': node.rating,
                'is_premium': getattr(node, 'is_premium', False),
                'is_ending': node.is_ending,
                'ending_type': node.ending_type.value if node.ending_type else None,
                'score': node.score
            }
            
            log_file = log_dir / "posts.log"
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + '\n')
                
        except Exception as e:
            logger.error(f"Error logging post: {e}")
    
    def update_choice_links(self, story_web, results: Dict[str, Any]):
        """Update posts with correct choice links after all posts are created"""
        try:
            logger.info("Updating choice links in posts...")
            
            # This would require editing tweets, which is not supported by X API
            # Alternative: Create a thread with updated links
            
            for node_id, post_result in results.items():
                node = story_web.nodes[node_id]
                
                if node.choices:
                    # Create reply with updated links
                    choice_links = self._create_choice_links(node, story_web)
                    
                    if choice_links and "[Link coming soon]" not in choice_links:
                        reply_content = f"Updated choices:\n{choice_links}"
                        
                        try:
                            self.api_v2.create_tweet(
                                text=reply_content,
                                in_reply_to_tweet_id=post_result['tweet_id']
                            )
                            logger.info(f"Posted choice update for {node_id}")
                        except Exception as e:
                            logger.error(f"Failed to post choice update for {node_id}: {e}")
            
        except Exception as e:
            logger.error(f"Error updating choice links: {e}")
    
    def get_posting_stats(self) -> Dict[str, Any]:
        """Get statistics about posted content"""
        try:
            stats = {
                'total_posts': len(self.posted_nodes),
                'spicy_posts': 0,
                'premium_posts': 0,
                'ending_posts': 0,
                'death_posts': 0,
                'success_posts': 0,
                'posts_with_media': 0
            }
            
            for node_id, post_result in self.posted_nodes.items():
                # This would need to be tracked during posting
                # For now, return basic stats
                pass
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting posting stats: {e}")
            return {}
