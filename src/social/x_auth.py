"""
X (Twitter) Authentication System
OAuth 2.0 authentication and account management
"""

import logging
import json
import webbrowser
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
import secrets
import base64
from urllib.parse import urlencode, parse_qs

import requests

logger = logging.getLogger(__name__)


class XSubscriptionTier:
    """X subscription tier definitions"""
    
    FREE = {
        'name': 'Free',
        'posts_per_day': 300,
        'posts_per_hour': 50,
        'video_length_max': 140,  # seconds
        'features': ['basic_posting'],
        'api_rate_limit': 100,  # requests per 15 minutes
        'monthly_cost': 0
    }
    
    BASIC = {
        'name': 'X Basic',
        'posts_per_day': 1000,
        'posts_per_hour': 100,
        'video_length_max': 600,  # 10 minutes
        'features': ['basic_posting', 'longer_videos', 'edit_posts'],
        'api_rate_limit': 300,
        'monthly_cost': 3
    }
    
    PREMIUM = {
        'name': 'X Premium',
        'posts_per_day': 2500,
        'posts_per_hour': 200,
        'video_length_max': 3600,  # 1 hour
        'features': ['basic_posting', 'longer_videos', 'edit_posts', 'analytics', 'ads_api'],
        'api_rate_limit': 1000,
        'monthly_cost': 8
    }
    
    PREMIUM_PLUS = {
        'name': 'X Premium+',
        'posts_per_day': 10000,
        'posts_per_hour': 500,
        'video_length_max': 7200,  # 2 hours
        'features': ['basic_posting', 'longer_videos', 'edit_posts', 'analytics', 'ads_api', 'priority_support'],
        'api_rate_limit': 5000,
        'monthly_cost': 16
    }
    
    @classmethod
    def get_all_tiers(cls):
        """Get all subscription tiers"""
        return {
            'free': cls.FREE,
            'basic': cls.BASIC,
            'premium': cls.PREMIUM,
            'premium_plus': cls.PREMIUM_PLUS
        }
    
    @classmethod
    def get_tier_by_name(cls, name: str):
        """Get tier by name"""
        tiers = cls.get_all_tiers()
        for tier_id, tier_data in tiers.items():
            if tier_data['name'].lower() == name.lower():
                return tier_id, tier_data
        return 'free', cls.FREE


class XAuthenticator:
    """Handles X OAuth 2.0 authentication"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        # Try both 'x' and 'x_auth' for backwards compatibility
        self.x_config = config.get('x', config.get('x_auth', {}))

        # OAuth 2.0 settings
        self.client_id = self.x_config.get('client_id')
        self.client_secret = self.x_config.get('client_secret')
        self.redirect_uri = self.x_config.get('redirect_uri', 'http://localhost:8080/callback')
        
        # X API endpoints
        self.auth_url = 'https://twitter.com/i/oauth2/authorize'
        self.token_url = 'https://api.twitter.com/2/oauth2/token'
        self.user_url = 'https://api.twitter.com/2/users/me'
        
        # Token storage
        self.tokens_file = Path("data/auth/x_tokens.json")
        self.tokens_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Current session
        self.access_token = None
        self.refresh_token = None
        self.user_info = None
        self.subscription_tier = 'free'
        
        # Load existing tokens
        self._load_tokens()
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.access_token is not None

    def has_credentials(self) -> bool:
        """Check if API credentials are configured"""
        return bool(self.client_id and self.client_secret)


    
    def get_auth_url(self) -> Tuple[str, str]:
        """Generate OAuth 2.0 authorization URL"""
        try:
            # Generate state and code verifier for PKCE
            state = secrets.token_urlsafe(32)
            code_verifier = secrets.token_urlsafe(32)
            code_challenge = base64.urlsafe_b64encode(
                hashlib.sha256(code_verifier.encode()).digest()
            ).decode().rstrip('=')
            
            # Store for later verification
            self._store_oauth_state(state, code_verifier)
            
            # Build authorization URL
            params = {
                'response_type': 'code',
                'client_id': self.client_id,
                'redirect_uri': self.redirect_uri,
                'scope': 'tweet.read tweet.write users.read offline.access',
                'state': state,
                'code_challenge': code_challenge,
                'code_challenge_method': 'S256',
                'force_login': 'true'  # Force re-authentication even if already logged in
            }
            
            auth_url = f"{self.auth_url}?{urlencode(params)}"
            
            logger.info("Generated X OAuth authorization URL")
            return auth_url, state
            
        except Exception as e:
            logger.error(f"Error generating auth URL: {e}")
            return None, None
    
    def authenticate_with_code(self, code: str, state: str) -> bool:
        """Complete OAuth flow with authorization code"""
        try:
            # Verify state and get code verifier
            stored_state, code_verifier = self._get_oauth_state(state)
            if not stored_state or stored_state != state:
                logger.error("Invalid OAuth state")
                return False
            
            # Exchange code for tokens
            token_data = {
                'grant_type': 'authorization_code',
                'client_id': self.client_id,
                'code': code,
                'redirect_uri': self.redirect_uri,
                'code_verifier': code_verifier
            }
            
            # Add client authentication
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = requests.post(self.token_url, data=token_data, headers=headers)
            
            if response.status_code == 200:
                tokens = response.json()
                self.access_token = tokens.get('access_token')
                self.refresh_token = tokens.get('refresh_token')
                
                # Get user info
                if self._fetch_user_info():
                    self._save_tokens()
                    logger.info("X authentication successful")
                    return True
            else:
                logger.error(f"Token exchange failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error completing OAuth flow: {e}")
            return False
    
    def refresh_access_token(self) -> bool:
        """Refresh access token using refresh token"""
        try:
            if not self.refresh_token:
                logger.error("No refresh token available")
                return False
            
            token_data = {
                'grant_type': 'refresh_token',
                'refresh_token': self.refresh_token,
                'client_id': self.client_id
            }
            
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = requests.post(self.token_url, data=token_data, headers=headers)
            
            if response.status_code == 200:
                tokens = response.json()
                self.access_token = tokens.get('access_token')
                if 'refresh_token' in tokens:
                    self.refresh_token = tokens['refresh_token']
                
                self._save_tokens()
                logger.info("Access token refreshed successfully")
                return True
            else:
                logger.error(f"Token refresh failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return False
    
    def _fetch_user_info(self) -> bool:
        """Fetch user information from X API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # Get user info with subscription details
            params = {
                'user.fields': 'id,name,username,verified,verified_type,public_metrics'
            }
            
            response = requests.get(self.user_url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                self.user_info = data.get('data', {})
                
                # Detect subscription tier from verification status
                self._detect_subscription_tier()
                
                logger.info(f"Fetched user info for @{self.user_info.get('username')}")
                return True
            else:
                logger.error(f"Failed to fetch user info: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error fetching user info: {e}")
            return False
    
    def _detect_subscription_tier(self):
        """Detect subscription tier from user info"""
        try:
            if not self.user_info:
                self.subscription_tier = 'free'
                return
            
            # Check verification status
            verified = self.user_info.get('verified', False)
            verified_type = self.user_info.get('verified_type', '')
            
            # Basic heuristics for tier detection
            if verified_type == 'blue':
                self.subscription_tier = 'basic'
            elif verified and verified_type:
                self.subscription_tier = 'premium'
            else:
                self.subscription_tier = 'free'
            
            logger.info(f"Detected subscription tier: {self.subscription_tier}")
            
        except Exception as e:
            logger.error(f"Error detecting subscription tier: {e}")
            self.subscription_tier = 'free'
    
    def set_subscription_tier(self, tier: str):
        """Manually set subscription tier"""
        tiers = XSubscriptionTier.get_all_tiers()
        if tier in tiers:
            self.subscription_tier = tier
            self._save_tokens()  # Save the tier setting
            logger.info(f"Subscription tier set to: {tier}")
        else:
            logger.error(f"Invalid subscription tier: {tier}")
    
    def get_subscription_info(self) -> Dict[str, Any]:
        """Get current subscription information"""
        tiers = XSubscriptionTier.get_all_tiers()
        tier_data = tiers.get(self.subscription_tier, XSubscriptionTier.FREE)
        
        return {
            'tier_id': self.subscription_tier,
            'tier_name': tier_data['name'],
            'tier_data': tier_data,
            'user_info': self.user_info
        }
    
    def get_rate_limits(self) -> Dict[str, int]:
        """Get rate limits for current subscription tier"""
        tiers = XSubscriptionTier.get_all_tiers()
        tier_data = tiers.get(self.subscription_tier, XSubscriptionTier.FREE)
        
        return {
            'posts_per_day': tier_data['posts_per_day'],
            'posts_per_hour': tier_data['posts_per_hour'],
            'api_rate_limit': tier_data['api_rate_limit'],
            'video_length_max': tier_data['video_length_max']
        }
    
    def can_post(self, posts_today: int, posts_this_hour: int) -> Tuple[bool, str]:
        """Check if user can post based on rate limits"""
        limits = self.get_rate_limits()
        
        if posts_today >= limits['posts_per_day']:
            return False, f"Daily limit reached ({limits['posts_per_day']} posts)"
        
        if posts_this_hour >= limits['posts_per_hour']:
            return False, f"Hourly limit reached ({limits['posts_per_hour']} posts)"
        
        return True, "OK"
    
    def logout(self):
        """Logout and clear tokens"""
        self.access_token = None
        self.refresh_token = None
        self.user_info = None
        self.subscription_tier = 'free'
        
        # Remove tokens file
        if self.tokens_file.exists():
            self.tokens_file.unlink()
        
        logger.info("Logged out from X")
    
    def _store_oauth_state(self, state: str, code_verifier: str):
        """Store OAuth state for verification"""
        state_file = Path("data/auth/oauth_state.json")
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(state_file, 'w') as f:
            json.dump({
                'state': state,
                'code_verifier': code_verifier,
                'timestamp': datetime.now().isoformat()
            }, f)
    
    def _get_oauth_state(self, state: str) -> Tuple[Optional[str], Optional[str]]:
        """Get and verify OAuth state"""
        state_file = Path("data/auth/oauth_state.json")
        
        if not state_file.exists():
            return None, None
        
        try:
            with open(state_file) as f:
                data = json.load(f)
            
            # Check if state is recent (within 10 minutes)
            timestamp = datetime.fromisoformat(data['timestamp'])
            if datetime.now() - timestamp > timedelta(minutes=10):
                return None, None
            
            return data.get('state'), data.get('code_verifier')
            
        except Exception as e:
            logger.error(f"Error reading OAuth state: {e}")
            return None, None
    
    def _save_tokens(self):
        """Save tokens to file"""
        try:
            token_data = {
                'access_token': self.access_token,
                'refresh_token': self.refresh_token,
                'user_info': self.user_info,
                'subscription_tier': self.subscription_tier,
                'saved_at': datetime.now().isoformat()
            }
            
            with open(self.tokens_file, 'w') as f:
                json.dump(token_data, f, indent=2)
            
            logger.info("Tokens saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving tokens: {e}")
    
    def _load_tokens(self):
        """Load tokens from file"""
        try:
            if not self.tokens_file.exists():
                return
            
            with open(self.tokens_file) as f:
                token_data = json.load(f)
            
            self.access_token = token_data.get('access_token')
            self.refresh_token = token_data.get('refresh_token')
            self.user_info = token_data.get('user_info')
            self.subscription_tier = token_data.get('subscription_tier', 'free')
            
            # Check if tokens are still valid (try to refresh if needed)
            if self.access_token:
                if not self._validate_token():
                    if self.refresh_token:
                        self.refresh_access_token()
            
            logger.info("Tokens loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading tokens: {e}")
    
    def _validate_token(self) -> bool:
        """Validate current access token"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(self.user_url, headers=headers)
            return response.status_code == 200
            
        except Exception:
            return False
