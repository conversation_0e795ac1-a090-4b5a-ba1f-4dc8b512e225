"""
Paywall Manager - Handles premium content restrictions and subscriber access
Manages X Super Follows, subscription tiers, and content gating
"""

import logging
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
import hashlib
import uuid

logger = logging.getLogger(__name__)


class PaywallManager:
    """Manages premium content access and subscriptions"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.social_config = config.get('social_media', {})
        
        # Subscription tiers
        self.subscription_tiers = {
            'free': {
                'name': 'Free',
                'price': 0,
                'access_level': 0,
                'features': ['Basic story access']
            },
            'premium': {
                'name': 'Premium',
                'price': 4.99,
                'access_level': 1,
                'features': ['Premium story paths', 'Early access', 'Exclusive content']
            },
            'spicy': {
                'name': 'Spicy',
                'price': 9.99,
                'access_level': 2,
                'features': ['All premium features', 'Adult content', 'Spicy storylines']
            }
        }
        
        # Load subscriber data
        self.subscribers = self._load_subscribers()
        self.access_tokens = {}
        
        # Premium content tracking
        self.premium_nodes = set()
        self.spicy_nodes = set()
        
    def _load_subscribers(self) -> Dict[str, Dict[str, Any]]:
        """Load subscriber data from file"""
        try:
            subscribers_file = Path("data/subscribers.json")
            if subscribers_file.exists():
                with open(subscribers_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading subscribers: {e}")
            return {}
    
    def _save_subscribers(self):
        """Save subscriber data to file"""
        try:
            subscribers_file = Path("data/subscribers.json")
            subscribers_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(subscribers_file, 'w', encoding='utf-8') as f:
                json.dump(self.subscribers, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving subscribers: {e}")
    
    def add_subscriber(self, user_id: str, username: str, tier: str = 'free', 
                      payment_info: Optional[Dict[str, Any]] = None) -> bool:
        """Add a new subscriber"""
        try:
            if tier not in self.subscription_tiers:
                logger.error(f"Invalid subscription tier: {tier}")
                return False
            
            subscriber_data = {
                'user_id': user_id,
                'username': username,
                'tier': tier,
                'access_level': self.subscription_tiers[tier]['access_level'],
                'subscribed_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(days=30)).isoformat(),
                'payment_info': payment_info or {},
                'access_count': 0,
                'last_access': None
            }
            
            self.subscribers[user_id] = subscriber_data
            self._save_subscribers()
            
            logger.info(f"Added subscriber: {username} ({tier})")
            return True
            
        except Exception as e:
            logger.error(f"Error adding subscriber: {e}")
            return False
    
    def check_access(self, user_id: str, content_type: str, node_id: str) -> Dict[str, Any]:
        """Check if user has access to content"""
        try:
            # Get subscriber info
            subscriber = self.subscribers.get(user_id)
            
            if not subscriber:
                # Free user
                access_level = 0
                tier = 'free'
            else:
                # Check subscription status
                if self._is_subscription_expired(subscriber):
                    access_level = 0
                    tier = 'free'
                else:
                    access_level = subscriber['access_level']
                    tier = subscriber['tier']
            
            # Determine required access level
            required_level = self._get_required_access_level(content_type, node_id)
            
            # Check access
            has_access = access_level >= required_level
            
            result = {
                'has_access': has_access,
                'user_tier': tier,
                'required_tier': self._get_tier_name_for_level(required_level),
                'access_level': access_level,
                'required_level': required_level,
                'content_type': content_type,
                'node_id': node_id
            }
            
            # Update access tracking
            if subscriber and has_access:
                self._update_access_tracking(user_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking access: {e}")
            return {
                'has_access': False,
                'error': str(e)
            }
    
    def _is_subscription_expired(self, subscriber: Dict[str, Any]) -> bool:
        """Check if subscription is expired"""
        try:
            expires_at = datetime.fromisoformat(subscriber['expires_at'])
            return datetime.now() > expires_at
        except Exception as e:
            logger.error(f"Error checking subscription expiry: {e}")
            return True
    
    def _get_required_access_level(self, content_type: str, node_id: str) -> int:
        """Get required access level for content"""
        try:
            if content_type == 'spicy' or node_id in self.spicy_nodes:
                return 2  # Spicy tier required
            elif content_type == 'premium' or node_id in self.premium_nodes:
                return 1  # Premium tier required
            else:
                return 0  # Free access
                
        except Exception as e:
            logger.error(f"Error determining required access level: {e}")
            return 1  # Default to premium required
    
    def _get_tier_name_for_level(self, level: int) -> str:
        """Get tier name for access level"""
        for tier, info in self.subscription_tiers.items():
            if info['access_level'] == level:
                return tier
        return 'free'
    
    def _update_access_tracking(self, user_id: str):
        """Update access tracking for subscriber"""
        try:
            if user_id in self.subscribers:
                self.subscribers[user_id]['access_count'] += 1
                self.subscribers[user_id]['last_access'] = datetime.now().isoformat()
                self._save_subscribers()
                
        except Exception as e:
            logger.error(f"Error updating access tracking: {e}")
    
    def create_paywall_post(self, node, story_web, target_tier: str = 'premium') -> str:
        """Create paywall post content"""
        try:
            tier_info = self.subscription_tiers.get(target_tier, self.subscription_tiers['premium'])
            
            # Create teaser content
            teaser_text = node.text[:100] + "..."
            
            paywall_content = f"""
🔒 Premium Content Alert!

{teaser_text}

This exclusive story path is available to {tier_info['name']} subscribers only!

✨ Subscribe for ${tier_info['price']}/month to unlock:
{chr(10).join('• ' + feature for feature in tier_info['features'])}

Subscribe now: [SUBSCRIPTION_LINK]

#CYOA #Premium #ExclusiveContent
"""
            
            return paywall_content.strip()
            
        except Exception as e:
            logger.error(f"Error creating paywall post: {e}")
            return "Premium content available to subscribers only."
    
    def create_subscription_link(self, tier: str, user_id: Optional[str] = None) -> str:
        """Create subscription link for tier"""
        try:
            # Generate unique subscription token
            token_data = {
                'tier': tier,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'token': str(uuid.uuid4())
            }
            
            # In a real implementation, this would be a proper payment processor link
            # For now, create a placeholder link
            base_url = "https://your-domain.com/subscribe"
            token = hashlib.md5(json.dumps(token_data).encode()).hexdigest()
            
            subscription_link = f"{base_url}?tier={tier}&token={token}"
            
            # Store token for verification
            self.access_tokens[token] = token_data
            
            return subscription_link
            
        except Exception as e:
            logger.error(f"Error creating subscription link: {e}")
            return "https://your-domain.com/subscribe"
    
    def process_subscription(self, token: str, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process subscription payment"""
        try:
            # Verify token
            if token not in self.access_tokens:
                return {'success': False, 'error': 'Invalid token'}
            
            token_data = self.access_tokens[token]
            tier = token_data['tier']
            user_id = payment_data.get('user_id')
            
            if not user_id:
                return {'success': False, 'error': 'User ID required'}
            
            # In a real implementation, process payment here
            # For now, simulate successful payment
            
            # Add subscriber
            success = self.add_subscriber(
                user_id=user_id,
                username=payment_data.get('username', f'user_{user_id}'),
                tier=tier,
                payment_info=payment_data
            )
            
            if success:
                # Clean up token
                del self.access_tokens[token]
                
                return {
                    'success': True,
                    'tier': tier,
                    'access_level': self.subscription_tiers[tier]['access_level'],
                    'expires_at': (datetime.now() + timedelta(days=30)).isoformat()
                }
            else:
                return {'success': False, 'error': 'Failed to create subscription'}
                
        except Exception as e:
            logger.error(f"Error processing subscription: {e}")
            return {'success': False, 'error': str(e)}
    
    def mark_premium_content(self, story_web):
        """Mark premium and spicy content in story web"""
        try:
            self.premium_nodes.clear()
            self.spicy_nodes.clear()
            
            for node_id, node in story_web.nodes.items():
                if hasattr(node, 'is_premium') and node.is_premium:
                    self.premium_nodes.add(node_id)
                
                if node.rating == 'spicy':
                    self.spicy_nodes.add(node_id)
            
            logger.info(f"Marked {len(self.premium_nodes)} premium nodes and {len(self.spicy_nodes)} spicy nodes")
            
        except Exception as e:
            logger.error(f"Error marking premium content: {e}")
    
    def get_subscriber_stats(self) -> Dict[str, Any]:
        """Get subscriber statistics"""
        try:
            stats = {
                'total_subscribers': len(self.subscribers),
                'active_subscribers': 0,
                'tier_breakdown': {tier: 0 for tier in self.subscription_tiers.keys()},
                'revenue_estimate': 0.0,
                'access_count_total': 0
            }
            
            for subscriber in self.subscribers.values():
                if not self._is_subscription_expired(subscriber):
                    stats['active_subscribers'] += 1
                    tier = subscriber['tier']
                    stats['tier_breakdown'][tier] += 1
                    stats['revenue_estimate'] += self.subscription_tiers[tier]['price']
                
                stats['access_count_total'] += subscriber.get('access_count', 0)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting subscriber stats: {e}")
            return {}
    
    def create_super_follows_integration(self) -> Dict[str, Any]:
        """Create X Super Follows integration"""
        try:
            # This would integrate with X's Super Follows API
            # For now, return configuration for manual setup
            
            integration_config = {
                'super_follows_enabled': True,
                'pricing_tiers': [
                    {
                        'name': 'Premium CYOA',
                        'price': 4.99,
                        'description': 'Access to premium story paths and exclusive content'
                    },
                    {
                        'name': 'Spicy CYOA',
                        'price': 9.99,
                        'description': 'All premium features plus adult content'
                    }
                ],
                'benefits': [
                    'Exclusive story paths',
                    'Early access to new adventures',
                    'Behind-the-scenes content',
                    'Direct story influence'
                ],
                'setup_instructions': [
                    '1. Enable Super Follows in X Creator Studio',
                    '2. Set up pricing tiers as configured above',
                    '3. Link subscriber verification to this system',
                    '4. Configure webhook for subscription events'
                ]
            }
            
            return integration_config
            
        except Exception as e:
            logger.error(f"Error creating Super Follows integration: {e}")
            return {}
    
    def verify_super_follows_subscriber(self, user_id: str) -> bool:
        """Verify if user is a Super Follows subscriber"""
        try:
            # This would check with X's API
            # For now, check local subscriber database
            
            subscriber = self.subscribers.get(user_id)
            if subscriber and not self._is_subscription_expired(subscriber):
                return subscriber['access_level'] > 0
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying Super Follows subscriber: {e}")
            return False
    
    def create_content_gate(self, node, required_tier: str = 'premium') -> Dict[str, Any]:
        """Create content gate for premium content"""
        try:
            gate_config = {
                'node_id': node.id,
                'required_tier': required_tier,
                'required_level': self.subscription_tiers[required_tier]['access_level'],
                'gate_type': 'subscription',
                'preview_text': node.text[:150] + "...",
                'subscription_link': self.create_subscription_link(required_tier),
                'benefits': self.subscription_tiers[required_tier]['features'],
                'price': self.subscription_tiers[required_tier]['price']
            }
            
            return gate_config
            
        except Exception as e:
            logger.error(f"Error creating content gate: {e}")
            return {}
