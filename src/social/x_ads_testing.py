"""
X Ads A/B Testing System - Test different videos without editing posts
Uses X Ads API to swap videos and test performance
"""

import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class AdVariant:
    """Represents a video variant for A/B testing"""
    variant_id: str
    video_path: str
    video_url: str
    description: str
    created_at: datetime
    
    # Performance metrics
    impressions: int = 0
    clicks: int = 0
    engagement_rate: float = 0.0
    conversion_rate: float = 0.0
    cost: float = 0.0


@dataclass
class ABTest:
    """Represents an A/B test for a story node"""
    test_id: str
    node_id: str
    post_id: str
    original_video: str
    variants: List[AdVariant]
    
    # Test configuration
    test_duration_hours: int = 24
    budget_per_variant: float = 10.0
    target_audience: Dict[str, Any] = None
    
    # Test status
    status: str = "pending"  # pending, running, completed, paused
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Results
    winning_variant: Optional[str] = None
    confidence_level: float = 0.0


class XAdsABTesting:
    """Manages A/B testing through X Ads API"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ads_config = config.get('x_ads', {})
        self.tests_dir = Path("data/ab_tests")
        self.tests_dir.mkdir(parents=True, exist_ok=True)
        
        # X Ads API credentials
        self.ads_api_key = self.ads_config.get('api_key')
        self.ads_api_secret = self.ads_config.get('api_secret')
        self.ads_account_id = self.ads_config.get('account_id')
        
        # Active tests
        self.active_tests: Dict[str, ABTest] = {}
        
        # Load existing tests
        self._load_active_tests()
    
    def create_ab_test(self, node_id: str, post_id: str, original_video: str, 
                      variant_videos: List[str], test_config: Dict[str, Any] = None) -> Optional[ABTest]:
        """Create a new A/B test for a story node"""
        try:
            test_id = f"test_{node_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create variants
            variants = []
            for i, video_path in enumerate(variant_videos):
                variant = AdVariant(
                    variant_id=f"{test_id}_variant_{i}",
                    video_path=video_path,
                    video_url="",  # Will be set when uploaded
                    description=f"Variant {i+1}",
                    created_at=datetime.now()
                )
                variants.append(variant)
            
            # Create test
            ab_test = ABTest(
                test_id=test_id,
                node_id=node_id,
                post_id=post_id,
                original_video=original_video,
                variants=variants,
                test_duration_hours=test_config.get('duration_hours', 24) if test_config else 24,
                budget_per_variant=test_config.get('budget_per_variant', 10.0) if test_config else 10.0,
                target_audience=test_config.get('target_audience', {}) if test_config else {}
            )
            
            # Upload variant videos and create ads
            success = self._setup_ad_variants(ab_test)
            
            if success:
                self.active_tests[test_id] = ab_test
                self._save_test(ab_test)
                logger.info(f"Created A/B test {test_id} for node {node_id}")
                return ab_test
            else:
                logger.error(f"Failed to setup A/B test for node {node_id}")
                return None
            
        except Exception as e:
            logger.error(f"Error creating A/B test: {e}")
            return None
    
    def start_ab_test(self, test_id: str) -> bool:
        """Start an A/B test"""
        try:
            if test_id not in self.active_tests:
                logger.error(f"Test {test_id} not found")
                return False
            
            ab_test = self.active_tests[test_id]
            
            if ab_test.status != "pending":
                logger.error(f"Test {test_id} is not in pending status")
                return False
            
            # Start ads for each variant
            success = True
            for variant in ab_test.variants:
                if not self._start_ad_variant(ab_test, variant):
                    success = False
            
            if success:
                ab_test.status = "running"
                ab_test.started_at = datetime.now()
                self._save_test(ab_test)
                logger.info(f"Started A/B test {test_id}")
                
                # Schedule test completion
                self._schedule_test_completion(ab_test)
            
            return success
            
        except Exception as e:
            logger.error(f"Error starting A/B test: {e}")
            return False
    
    def _setup_ad_variants(self, ab_test: ABTest) -> bool:
        """Setup ad variants for A/B testing"""
        try:
            # This would integrate with X Ads API
            # For now, simulate the setup
            
            for variant in ab_test.variants:
                # Upload video to X
                video_url = self._upload_video_to_x(variant.video_path)
                if not video_url:
                    return False
                
                variant.video_url = video_url
                
                # Create ad campaign for this variant
                campaign_id = self._create_ad_campaign(ab_test, variant)
                if not campaign_id:
                    return False
                
                logger.info(f"Setup ad variant {variant.variant_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up ad variants: {e}")
            return False
    
    def _upload_video_to_x(self, video_path: str) -> Optional[str]:
        """Upload video to X and return media URL"""
        try:
            # This would use X API to upload video
            # For now, simulate upload
            
            logger.info(f"Uploading video: {video_path}")
            
            # In real implementation:
            # media_upload = self.x_api.media_upload(video_path)
            # return media_upload.media_url
            
            # Simulate successful upload
            return f"https://video.twimg.com/ext_tw_video/{hash(video_path)}/pu/vid/1280x720/video.mp4"
            
        except Exception as e:
            logger.error(f"Error uploading video: {e}")
            return None
    
    def _create_ad_campaign(self, ab_test: ABTest, variant: AdVariant) -> Optional[str]:
        """Create ad campaign for a variant"""
        try:
            # This would use X Ads API to create campaign
            logger.info(f"Creating ad campaign for variant {variant.variant_id}")
            
            campaign_config = {
                'name': f"ABTest_{ab_test.test_id}_{variant.variant_id}",
                'objective': 'ENGAGEMENT',
                'budget': ab_test.budget_per_variant,
                'duration_hours': ab_test.test_duration_hours,
                'target_audience': ab_test.target_audience,
                'promoted_post_id': ab_test.post_id,
                'video_url': variant.video_url
            }
            
            # In real implementation:
            # campaign = self.ads_api.create_campaign(campaign_config)
            # return campaign.id
            
            # Simulate campaign creation
            campaign_id = f"campaign_{variant.variant_id}"
            logger.info(f"Created campaign {campaign_id}")
            
            return campaign_id
            
        except Exception as e:
            logger.error(f"Error creating ad campaign: {e}")
            return None
    
    def _start_ad_variant(self, ab_test: ABTest, variant: AdVariant) -> bool:
        """Start advertising for a variant"""
        try:
            # This would start the ad campaign
            logger.info(f"Starting ad for variant {variant.variant_id}")
            
            # In real implementation:
            # self.ads_api.start_campaign(variant.campaign_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting ad variant: {e}")
            return False
    
    def _schedule_test_completion(self, ab_test: ABTest):
        """Schedule automatic test completion"""
        # This would set up a timer or scheduled task
        # For now, just log the schedule
        completion_time = datetime.now() + timedelta(hours=ab_test.test_duration_hours)
        logger.info(f"Test {ab_test.test_id} scheduled to complete at {completion_time}")
    
    def check_test_completion(self, test_id: str) -> bool:
        """Check if a test should be completed"""
        try:
            if test_id not in self.active_tests:
                return False
            
            ab_test = self.active_tests[test_id]
            
            if ab_test.status != "running":
                return False
            
            # Check if test duration has elapsed
            if ab_test.started_at:
                elapsed = datetime.now() - ab_test.started_at
                if elapsed.total_seconds() >= ab_test.test_duration_hours * 3600:
                    return self.complete_ab_test(test_id)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking test completion: {e}")
            return False
    
    def complete_ab_test(self, test_id: str) -> bool:
        """Complete an A/B test and determine winner"""
        try:
            if test_id not in self.active_tests:
                logger.error(f"Test {test_id} not found")
                return False
            
            ab_test = self.active_tests[test_id]
            
            # Collect final metrics
            self._collect_variant_metrics(ab_test)
            
            # Determine winner
            winner = self._determine_winner(ab_test)
            
            # Stop all ad campaigns
            self._stop_all_variants(ab_test)
            
            # Update test status
            ab_test.status = "completed"
            ab_test.completed_at = datetime.now()
            ab_test.winning_variant = winner.variant_id if winner else None
            
            # Calculate confidence level
            ab_test.confidence_level = self._calculate_confidence(ab_test)
            
            self._save_test(ab_test)
            
            logger.info(f"Completed A/B test {test_id}. Winner: {ab_test.winning_variant}")
            
            # Apply winning variant if confidence is high enough
            if ab_test.confidence_level > 0.95 and winner:
                self._apply_winning_variant(ab_test, winner)
            
            return True
            
        except Exception as e:
            logger.error(f"Error completing A/B test: {e}")
            return False
    
    def _collect_variant_metrics(self, ab_test: ABTest):
        """Collect performance metrics for all variants"""
        try:
            for variant in ab_test.variants:
                # This would fetch metrics from X Ads API
                # For now, simulate metrics
                
                import random
                variant.impressions = random.randint(1000, 10000)
                variant.clicks = random.randint(50, 500)
                variant.engagement_rate = variant.clicks / variant.impressions if variant.impressions > 0 else 0
                variant.conversion_rate = random.uniform(0.01, 0.05)
                variant.cost = ab_test.budget_per_variant
                
                logger.info(f"Variant {variant.variant_id}: {variant.impressions} impressions, {variant.clicks} clicks")
            
        except Exception as e:
            logger.error(f"Error collecting variant metrics: {e}")
    
    def _determine_winner(self, ab_test: ABTest) -> Optional[AdVariant]:
        """Determine the winning variant based on performance"""
        try:
            if not ab_test.variants:
                return None
            
            # Sort by engagement rate (or other metric)
            sorted_variants = sorted(ab_test.variants, key=lambda v: v.engagement_rate, reverse=True)
            
            return sorted_variants[0]
            
        except Exception as e:
            logger.error(f"Error determining winner: {e}")
            return None
    
    def _calculate_confidence(self, ab_test: ABTest) -> float:
        """Calculate statistical confidence in the results"""
        try:
            # This would perform statistical significance testing
            # For now, simulate based on sample size and difference
            
            if len(ab_test.variants) < 2:
                return 0.0
            
            # Simple simulation based on impressions
            total_impressions = sum(v.impressions for v in ab_test.variants)
            
            if total_impressions > 10000:
                return 0.95
            elif total_impressions > 5000:
                return 0.85
            elif total_impressions > 1000:
                return 0.70
            else:
                return 0.50
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.0
    
    def _stop_all_variants(self, ab_test: ABTest):
        """Stop all ad campaigns for the test"""
        try:
            for variant in ab_test.variants:
                # This would stop the ad campaign
                logger.info(f"Stopping ad for variant {variant.variant_id}")
                
                # In real implementation:
                # self.ads_api.stop_campaign(variant.campaign_id)
            
        except Exception as e:
            logger.error(f"Error stopping variants: {e}")
    
    def _apply_winning_variant(self, ab_test: ABTest, winner: AdVariant):
        """Apply the winning variant to the original post"""
        try:
            # This is where the magic happens - update the post with winning video
            # Since X has edit limits, we need to be strategic about this
            
            logger.info(f"Applying winning variant {winner.variant_id} to post {ab_test.post_id}")
            
            # In real implementation, this would:
            # 1. Edit the original post to use the winning video
            # 2. Or create a new promoted post with the winning video
            # 3. Track that this edit was used (for the 5-edit limit)
            
            # For now, just log the action
            logger.info(f"Would update post {ab_test.post_id} with video {winner.video_url}")
            
        except Exception as e:
            logger.error(f"Error applying winning variant: {e}")
    
    def get_test_results(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed results for a test"""
        try:
            if test_id not in self.active_tests:
                return None
            
            ab_test = self.active_tests[test_id]
            
            results = {
                'test_id': test_id,
                'status': ab_test.status,
                'node_id': ab_test.node_id,
                'post_id': ab_test.post_id,
                'started_at': ab_test.started_at.isoformat() if ab_test.started_at else None,
                'completed_at': ab_test.completed_at.isoformat() if ab_test.completed_at else None,
                'winning_variant': ab_test.winning_variant,
                'confidence_level': ab_test.confidence_level,
                'variants': []
            }
            
            for variant in ab_test.variants:
                variant_data = {
                    'variant_id': variant.variant_id,
                    'description': variant.description,
                    'impressions': variant.impressions,
                    'clicks': variant.clicks,
                    'engagement_rate': variant.engagement_rate,
                    'conversion_rate': variant.conversion_rate,
                    'cost': variant.cost
                }
                results['variants'].append(variant_data)
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting test results: {e}")
            return None
    
    def _save_test(self, ab_test: ABTest):
        """Save test to disk"""
        try:
            test_file = self.tests_dir / f"{ab_test.test_id}.json"
            
            # Convert to serializable format
            test_data = {
                'test_id': ab_test.test_id,
                'node_id': ab_test.node_id,
                'post_id': ab_test.post_id,
                'original_video': ab_test.original_video,
                'test_duration_hours': ab_test.test_duration_hours,
                'budget_per_variant': ab_test.budget_per_variant,
                'target_audience': ab_test.target_audience,
                'status': ab_test.status,
                'started_at': ab_test.started_at.isoformat() if ab_test.started_at else None,
                'completed_at': ab_test.completed_at.isoformat() if ab_test.completed_at else None,
                'winning_variant': ab_test.winning_variant,
                'confidence_level': ab_test.confidence_level,
                'variants': []
            }
            
            for variant in ab_test.variants:
                variant_data = {
                    'variant_id': variant.variant_id,
                    'video_path': variant.video_path,
                    'video_url': variant.video_url,
                    'description': variant.description,
                    'created_at': variant.created_at.isoformat(),
                    'impressions': variant.impressions,
                    'clicks': variant.clicks,
                    'engagement_rate': variant.engagement_rate,
                    'conversion_rate': variant.conversion_rate,
                    'cost': variant.cost
                }
                test_data['variants'].append(variant_data)
            
            with open(test_file, 'w') as f:
                json.dump(test_data, f, indent=2)
            
        except Exception as e:
            logger.error(f"Error saving test: {e}")
    
    def _load_active_tests(self):
        """Load active tests from disk"""
        try:
            for test_file in self.tests_dir.glob("*.json"):
                with open(test_file) as f:
                    test_data = json.load(f)
                
                # Only load running tests
                if test_data.get('status') == 'running':
                    # Reconstruct ABTest object
                    # This would be more complex in real implementation
                    pass
            
        except Exception as e:
            logger.error(f"Error loading active tests: {e}")
