"""
Rate Limiting System for X Posting
Respects subscription tier limits and prevents API abuse
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class PostingSchedule:
    """Manages posting schedule and rate limiting"""
    
    def __init__(self, subscription_tier: str, rate_limits: Dict[str, int]):
        self.subscription_tier = subscription_tier
        self.rate_limits = rate_limits
        
        # Tracking
        self.posts_today = 0
        self.posts_this_hour = 0
        self.post_history = deque(maxlen=1000)  # Keep last 1000 posts
        
        # Scheduling
        self.scheduled_posts = []
        self.posting_queue = []
        
        # Storage
        self.data_file = Path("data/posting/rate_limit_data.json")
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        
        self._load_data()
        self._cleanup_old_data()
    
    def can_post_now(self) -> Tuple[bool, str, Optional[datetime]]:
        """Check if we can post right now"""
        self._update_counters()
        
        # Check daily limit
        if self.posts_today >= self.rate_limits['posts_per_day']:
            next_reset = self._get_next_day_reset()
            return False, f"Daily limit reached ({self.rate_limits['posts_per_day']} posts)", next_reset
        
        # Check hourly limit
        if self.posts_this_hour >= self.rate_limits['posts_per_hour']:
            next_reset = self._get_next_hour_reset()
            return False, f"Hourly limit reached ({self.rate_limits['posts_per_hour']} posts)", next_reset
        
        return True, "OK", None
    
    def get_next_available_slot(self) -> datetime:
        """Get the next available posting slot"""
        can_post, reason, next_reset = self.can_post_now()
        
        if can_post:
            return datetime.now()
        
        if next_reset:
            return next_reset
        
        # Fallback to next hour
        return datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    
    def schedule_posts(self, post_count: int, start_time: Optional[datetime] = None) -> List[datetime]:
        """Schedule multiple posts respecting rate limits"""
        if not start_time:
            start_time = datetime.now()
        
        scheduled_times = []
        current_time = start_time
        posts_scheduled_today = self.posts_today
        posts_scheduled_this_hour = self.posts_this_hour
        
        for i in range(post_count):
            # Find next available slot
            while True:
                # Check if we can post at current_time
                day_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
                hour_start = current_time.replace(minute=0, second=0, microsecond=0)
                
                # Count posts for this day and hour
                day_posts = self._count_posts_in_period(day_start, day_start + timedelta(days=1))
                hour_posts = self._count_posts_in_period(hour_start, hour_start + timedelta(hours=1))
                
                # Add scheduled posts
                day_posts += len([t for t in scheduled_times if day_start <= t < day_start + timedelta(days=1)])
                hour_posts += len([t for t in scheduled_times if hour_start <= t < hour_start + timedelta(hours=1)])
                
                # Check limits
                if (day_posts < self.rate_limits['posts_per_day'] and 
                    hour_posts < self.rate_limits['posts_per_hour']):
                    scheduled_times.append(current_time)
                    break
                
                # Move to next slot
                if hour_posts >= self.rate_limits['posts_per_hour']:
                    # Move to next hour
                    current_time = hour_start + timedelta(hours=1)
                elif day_posts >= self.rate_limits['posts_per_day']:
                    # Move to next day
                    current_time = day_start + timedelta(days=1)
                else:
                    # Small increment
                    current_time += timedelta(minutes=5)
            
            # Add small buffer between posts
            current_time += timedelta(minutes=2)
        
        return scheduled_times
    
    def record_post(self, post_id: str, timestamp: Optional[datetime] = None):
        """Record a successful post"""
        if not timestamp:
            timestamp = datetime.now()
        
        post_record = {
            'post_id': post_id,
            'timestamp': timestamp.isoformat(),
            'subscription_tier': self.subscription_tier
        }
        
        self.post_history.append(post_record)
        self._save_data()
        self._update_counters()
        
        logger.info(f"Recorded post {post_id} at {timestamp}")
    
    def get_posting_stats(self) -> Dict[str, Any]:
        """Get current posting statistics"""
        self._update_counters()
        
        # Calculate usage percentages
        daily_usage = (self.posts_today / self.rate_limits['posts_per_day']) * 100
        hourly_usage = (self.posts_this_hour / self.rate_limits['posts_per_hour']) * 100
        
        # Get recent posting rate
        recent_posts = self._get_posts_in_last_hours(24)
        posts_last_24h = len(recent_posts)
        
        return {
            'subscription_tier': self.subscription_tier,
            'rate_limits': self.rate_limits,
            'current_usage': {
                'posts_today': self.posts_today,
                'posts_this_hour': self.posts_this_hour,
                'posts_last_24h': posts_last_24h
            },
            'usage_percentages': {
                'daily': daily_usage,
                'hourly': hourly_usage
            },
            'next_resets': {
                'daily': self._get_next_day_reset().isoformat(),
                'hourly': self._get_next_hour_reset().isoformat()
            },
            'can_post_now': self.can_post_now()[0]
        }
    
    def estimate_story_posting_time(self, node_count: int) -> Dict[str, Any]:
        """Estimate how long it will take to post a complete story"""
        scheduled_times = self.schedule_posts(node_count)
        
        if not scheduled_times:
            return {
                'total_time': 0,
                'start_time': None,
                'end_time': None,
                'posts_per_day': 0
            }
        
        start_time = scheduled_times[0]
        end_time = scheduled_times[-1]
        total_time = (end_time - start_time).total_seconds()
        
        # Calculate average posts per day
        days = max(1, (end_time - start_time).days + 1)
        posts_per_day = node_count / days
        
        return {
            'total_time_seconds': total_time,
            'total_time_hours': total_time / 3600,
            'total_time_days': total_time / 86400,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'posts_per_day': posts_per_day,
            'scheduled_times': [t.isoformat() for t in scheduled_times]
        }
    
    def _update_counters(self):
        """Update post counters for today and this hour"""
        now = datetime.now()
        
        # Count posts today
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        self.posts_today = self._count_posts_in_period(today_start, now)
        
        # Count posts this hour
        hour_start = now.replace(minute=0, second=0, microsecond=0)
        self.posts_this_hour = self._count_posts_in_period(hour_start, now)
    
    def _count_posts_in_period(self, start_time: datetime, end_time: datetime) -> int:
        """Count posts in a specific time period"""
        count = 0
        for post in self.post_history:
            post_time = datetime.fromisoformat(post['timestamp'])
            if start_time <= post_time <= end_time:
                count += 1
        return count
    
    def _get_posts_in_last_hours(self, hours: int) -> List[Dict]:
        """Get posts from the last N hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        recent_posts = []
        
        for post in self.post_history:
            post_time = datetime.fromisoformat(post['timestamp'])
            if post_time >= cutoff:
                recent_posts.append(post)
        
        return recent_posts
    
    def _get_next_day_reset(self) -> datetime:
        """Get next daily reset time"""
        now = datetime.now()
        tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        return tomorrow
    
    def _get_next_hour_reset(self) -> datetime:
        """Get next hourly reset time"""
        now = datetime.now()
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        return next_hour
    
    def _cleanup_old_data(self):
        """Remove old post records to keep data manageable"""
        cutoff = datetime.now() - timedelta(days=7)  # Keep 7 days of history
        
        cleaned_history = deque(maxlen=1000)
        for post in self.post_history:
            post_time = datetime.fromisoformat(post['timestamp'])
            if post_time >= cutoff:
                cleaned_history.append(post)
        
        self.post_history = cleaned_history
        self._save_data()
    
    def _save_data(self):
        """Save rate limiting data"""
        try:
            data = {
                'subscription_tier': self.subscription_tier,
                'rate_limits': self.rate_limits,
                'post_history': list(self.post_history),
                'saved_at': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving rate limit data: {e}")
    
    def _load_data(self):
        """Load rate limiting data"""
        try:
            if not self.data_file.exists():
                return
            
            with open(self.data_file) as f:
                data = json.load(f)
            
            # Load post history
            history = data.get('post_history', [])
            self.post_history = deque(history, maxlen=1000)
            
            logger.info(f"Loaded {len(self.post_history)} post records")
            
        except Exception as e:
            logger.error(f"Error loading rate limit data: {e}")


class StoryPostingPlanner:
    """Plans story posting with rate limiting"""
    
    def __init__(self, rate_limiter: PostingSchedule):
        self.rate_limiter = rate_limiter
    
    def create_posting_plan(self, story_nodes: List[str], 
                          start_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Create a complete posting plan for a story"""
        if not start_time:
            start_time = datetime.now()
        
        node_count = len(story_nodes)
        
        # Get posting schedule
        scheduled_times = self.rate_limiter.schedule_posts(node_count, start_time)
        
        # Create posting plan
        posting_plan = []
        for i, (node_id, post_time) in enumerate(zip(story_nodes, scheduled_times)):
            posting_plan.append({
                'node_id': node_id,
                'post_order': i + 1,
                'scheduled_time': post_time.isoformat(),
                'estimated_delay': (post_time - start_time).total_seconds()
            })
        
        # Get timing estimates
        timing_info = self.rate_limiter.estimate_story_posting_time(node_count)
        
        return {
            'story_info': {
                'total_nodes': node_count,
                'start_time': start_time.isoformat(),
                'estimated_completion': timing_info['end_time']
            },
            'timing': timing_info,
            'posting_plan': posting_plan,
            'rate_limit_info': self.rate_limiter.get_posting_stats()
        }
    
    def optimize_posting_schedule(self, story_nodes: List[str], 
                                target_completion: Optional[datetime] = None) -> Dict[str, Any]:
        """Optimize posting schedule for target completion time"""
        node_count = len(story_nodes)
        
        if target_completion:
            # Calculate required posting rate
            time_available = (target_completion - datetime.now()).total_seconds()
            required_rate = node_count / (time_available / 3600)  # posts per hour
            
            # Check if achievable with current tier
            max_rate = self.rate_limiter.rate_limits['posts_per_hour']
            
            if required_rate > max_rate:
                # Suggest tier upgrade
                return {
                    'achievable': False,
                    'required_rate': required_rate,
                    'max_rate': max_rate,
                    'suggested_tier': self._suggest_tier_upgrade(required_rate),
                    'alternative_completion': None
                }
        
        # Create optimized plan
        plan = self.create_posting_plan(story_nodes)
        
        return {
            'achievable': True,
            'posting_plan': plan,
            'optimization_notes': self._get_optimization_notes(plan)
        }
    
    def _suggest_tier_upgrade(self, required_rate: float) -> str:
        """Suggest appropriate tier upgrade"""
        from .x_auth import XSubscriptionTier
        
        tiers = XSubscriptionTier.get_all_tiers()
        
        for tier_id, tier_data in tiers.items():
            if tier_data['posts_per_hour'] >= required_rate:
                return tier_id
        
        return 'premium_plus'  # Highest tier
    
    def _get_optimization_notes(self, plan: Dict[str, Any]) -> List[str]:
        """Get optimization notes for the posting plan"""
        notes = []
        
        timing = plan['timing']
        
        if timing['total_time_days'] > 7:
            notes.append("Story will take over a week to post completely")
        
        if timing['posts_per_day'] < 10:
            notes.append("Consider upgrading subscription for faster posting")
        
        rate_info = plan['rate_limit_info']
        if rate_info['usage_percentages']['daily'] > 80:
            notes.append("High daily usage - consider spreading posts over multiple days")
        
        return notes
