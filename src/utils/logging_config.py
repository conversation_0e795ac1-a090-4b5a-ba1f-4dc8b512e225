"""
Comprehensive Logging System
Configurable logging with multiple outputs, formatters, and verbosity levels
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
import traceback
from enum import IntEnum

from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install as install_rich_traceback


class LogLevel(IntEnum):
    """Custom log levels with verbosity control"""
    SILENT = 0      # No output
    CRITICAL = 50   # Only critical errors
    ERROR = 40      # Errors and critical
    WARNING = 30    # Warnings, errors, critical
    INFO = 20       # Info, warnings, errors, critical
    DEBUG = 10      # Debug, info, warnings, errors, critical
    TRACE = 5       # Everything including trace calls


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'TRACE': '\033[90m',     # Dark gray
    }
    RESET = '\033[0m'
    
    def format(self, record):
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry['extra'] = log_entry.get('extra', {})
                log_entry['extra'][key] = value
        
        return json.dumps(log_entry)


class PerformanceLogger:
    """Performance monitoring and logging"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.timers = {}
    
    def start_timer(self, name: str):
        """Start a performance timer"""
        self.timers[name] = datetime.now()
        self.logger.debug(f"⏱️ Started timer: {name}")
    
    def end_timer(self, name: str, log_level: int = logging.INFO):
        """End a performance timer and log duration"""
        if name in self.timers:
            duration = (datetime.now() - self.timers[name]).total_seconds()
            self.logger.log(log_level, f"⏱️ Timer {name}: {duration:.3f}s")
            del self.timers[name]
            return duration
        else:
            self.logger.warning(f"⚠️ Timer {name} not found")
            return None
    
    def log_memory_usage(self):
        """Log current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            self.logger.debug(f"💾 Memory usage: {memory_info.rss / 1024 / 1024:.1f} MB")
        except ImportError:
            self.logger.debug("💾 Memory logging requires psutil")


class LoggingManager:
    """Central logging management system"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logging_config = self.config.get('logging', {})
        
        # Configuration
        self.verbosity = self.logging_config.get('verbosity', LogLevel.INFO)
        self.log_to_file = self.logging_config.get('log_to_file', True)
        self.log_to_console = self.logging_config.get('log_to_console', True)
        self.log_format = self.logging_config.get('format', 'detailed')
        self.max_file_size = self.logging_config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
        self.backup_count = self.logging_config.get('backup_count', 5)
        
        # Paths
        self.log_dir = Path(self.logging_config.get('log_dir', 'logs'))
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Console setup
        self.console = Console()
        
        # Install rich traceback for better error display
        install_rich_traceback(show_locals=self.verbosity >= LogLevel.DEBUG)

        # Component loggers (initialize before setup_logging)
        self._component_loggers = {}

        # Initialize logging
        self._setup_logging()

        # Performance logger
        self.perf_logger = PerformanceLogger(self.get_logger('performance'))
    
    def _setup_logging(self):
        """Setup the logging configuration"""
        # Clear existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set root level based on verbosity
        if self.verbosity == LogLevel.SILENT:
            root_logger.setLevel(logging.CRITICAL + 1)  # Disable all logging
            return
        else:
            root_logger.setLevel(self.verbosity)
        
        # Add custom TRACE level
        logging.addLevelName(LogLevel.TRACE, 'TRACE')
        
        # Setup formatters
        formatters = self._create_formatters()
        
        # Setup handlers
        handlers = []
        
        if self.log_to_console and self.verbosity > LogLevel.SILENT:
            handlers.append(self._create_console_handler(formatters))
        
        if self.log_to_file and self.verbosity > LogLevel.SILENT:
            handlers.extend(self._create_file_handlers(formatters))
        
        # Add handlers to root logger
        for handler in handlers:
            root_logger.addHandler(handler)
        
        # Log initialization
        if self.verbosity > LogLevel.SILENT:
            logger = logging.getLogger(__name__)
            logger.info(f"🚀 Logging system initialized - Verbosity: {LogLevel(self.verbosity).name}")
    
    def _create_formatters(self) -> Dict[str, logging.Formatter]:
        """Create different formatters for different outputs"""
        formatters = {}
        
        if self.log_format == 'simple':
            formatters['console'] = logging.Formatter(
                '%(levelname)s: %(message)s'
            )
            formatters['file'] = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
        elif self.log_format == 'detailed':
            formatters['console'] = ColoredFormatter(
                '%(asctime)s | %(name)-20s | %(levelname)-8s | %(message)s',
                datefmt='%H:%M:%S'
            )
            formatters['file'] = logging.Formatter(
                '%(asctime)s | %(name)-20s | %(levelname)-8s | %(funcName)-15s:%(lineno)-4d | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        elif self.log_format == 'json':
            formatters['console'] = JSONFormatter()
            formatters['file'] = JSONFormatter()
        elif self.log_format == 'rich':
            # Rich format uses RichHandler for console, but still needs file formatter
            formatters['console'] = None  # Will use RichHandler instead
            formatters['file'] = logging.Formatter(
                '%(asctime)s | %(name)-20s | %(levelname)-8s | %(funcName)-15s:%(lineno)-4d | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

        return formatters
    
    def _create_console_handler(self, formatters: Dict[str, logging.Formatter]) -> logging.Handler:
        """Create console handler"""
        if self.log_format == 'rich':
            # Use Rich handler for beautiful console output
            handler = RichHandler(
                console=self.console,
                show_time=True,
                show_level=True,
                show_path=self.verbosity >= LogLevel.DEBUG,
                markup=True,
                rich_tracebacks=True,
                tracebacks_show_locals=self.verbosity >= LogLevel.DEBUG
            )
        else:
            handler = logging.StreamHandler(sys.stdout)
            handler.setFormatter(formatters['console'])
        
        handler.setLevel(self.verbosity)
        return handler
    
    def _create_file_handlers(self, formatters: Dict[str, logging.Formatter]) -> List[logging.Handler]:
        """Create file handlers"""
        handlers = []
        
        # Main log file (rotating)
        main_log = self.log_dir / 'cyoax.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        main_handler.setFormatter(formatters['file'])
        main_handler.setLevel(self.verbosity)
        handlers.append(main_handler)
        
        # Error log file (errors and above only)
        error_log = self.log_dir / 'errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(formatters['file'])
        error_handler.setLevel(logging.ERROR)
        handlers.append(error_handler)
        
        # Performance log file (if debug level)
        if self.verbosity >= LogLevel.DEBUG:
            perf_log = self.log_dir / 'performance.log'
            perf_handler = logging.handlers.RotatingFileHandler(
                perf_log,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            perf_handler.setFormatter(formatters['file'])
            perf_handler.addFilter(lambda record: record.name.endswith('performance'))
            handlers.append(perf_handler)
        
        return handlers
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger for a specific component"""
        if name not in self._component_loggers:
            logger = logging.getLogger(name)
            
            # Add trace method
            def trace(self, message, *args, **kwargs):
                if self.isEnabledFor(LogLevel.TRACE):
                    self._log(LogLevel.TRACE, message, args, **kwargs)
            
            logger.trace = trace.__get__(logger, logging.Logger)
            
            self._component_loggers[name] = logger
        
        return self._component_loggers[name]
    
    def set_verbosity(self, level: int):
        """Change verbosity level at runtime"""
        self.verbosity = level
        
        # Update all handlers
        root_logger = logging.getLogger()
        
        if level == LogLevel.SILENT:
            root_logger.setLevel(logging.CRITICAL + 1)
            for handler in root_logger.handlers:
                handler.setLevel(logging.CRITICAL + 1)
        else:
            root_logger.setLevel(level)
            for handler in root_logger.handlers:
                if not isinstance(handler, logging.handlers.RotatingFileHandler) or 'errors.log' not in str(handler.baseFilename):
                    handler.setLevel(level)
        
        logger = self.get_logger(__name__)
        if level > LogLevel.SILENT:
            logger.info(f"🔧 Verbosity changed to: {LogLevel(level).name}")
    
    def log_system_info(self):
        """Log system information"""
        if self.verbosity <= LogLevel.SILENT:
            return
        
        logger = self.get_logger('system')
        
        try:
            import platform
            import psutil
            
            logger.info("🖥️ System Information:")
            logger.info(f"  Platform: {platform.platform()}")
            logger.info(f"  Python: {platform.python_version()}")
            logger.info(f"  CPU: {platform.processor()}")
            logger.info(f"  Memory: {psutil.virtual_memory().total / 1024**3:.1f} GB")
            logger.info(f"  Disk: {psutil.disk_usage('/').total / 1024**3:.1f} GB")
            
        except ImportError:
            logger.info("🖥️ System info logging requires psutil")
        except Exception as e:
            logger.warning(f"⚠️ Could not log system info: {e}")
    
    def log_config(self, config: Dict[str, Any]):
        """Log configuration (sanitized)"""
        if self.verbosity <= LogLevel.INFO:
            return
        
        logger = self.get_logger('config')
        
        # Sanitize sensitive information
        sanitized_config = self._sanitize_config(config)
        
        logger.debug("⚙️ Configuration:")
        for key, value in sanitized_config.items():
            logger.debug(f"  {key}: {value}")
    
    def _sanitize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sensitive information from config for logging"""
        sensitive_keys = ['password', 'secret', 'key', 'token', 'api_key', 'client_secret']
        
        def sanitize_dict(d):
            if isinstance(d, dict):
                return {
                    k: '***REDACTED***' if any(sensitive in k.lower() for sensitive in sensitive_keys)
                    else sanitize_dict(v)
                    for k, v in d.items()
                }
            elif isinstance(d, list):
                return [sanitize_dict(item) for item in d]
            else:
                return d
        
        return sanitize_dict(config)
    
    def create_context_logger(self, context: str, **kwargs) -> logging.LoggerAdapter:
        """Create a logger with context information"""
        logger = self.get_logger(context)
        return logging.LoggerAdapter(logger, kwargs)
    
    def get_performance_logger(self) -> PerformanceLogger:
        """Get the performance logger"""
        return self.perf_logger


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def setup_logging(config: Dict[str, Any] = None) -> LoggingManager:
    """Setup global logging configuration"""
    global _logging_manager
    _logging_manager = LoggingManager(config)
    return _logging_manager


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    if _logging_manager is None:
        setup_logging()
    return _logging_manager.get_logger(name)


def set_verbosity(level: int):
    """Set global verbosity level"""
    if _logging_manager is None:
        setup_logging()
    _logging_manager.set_verbosity(level)


def get_performance_logger() -> PerformanceLogger:
    """Get the performance logger"""
    if _logging_manager is None:
        setup_logging()
    return _logging_manager.get_performance_logger()


# Convenience functions for common logging patterns
def log_function_entry(logger: logging.Logger, func_name: str, **kwargs):
    """Log function entry with parameters"""
    if kwargs:
        params = ', '.join(f"{k}={v}" for k, v in kwargs.items())
        logger.trace(f"🔵 Entering {func_name}({params})")
    else:
        logger.trace(f"🔵 Entering {func_name}()")


def log_function_exit(logger: logging.Logger, func_name: str, result=None):
    """Log function exit with result"""
    if result is not None:
        logger.trace(f"🔴 Exiting {func_name} -> {result}")
    else:
        logger.trace(f"🔴 Exiting {func_name}")


def log_api_call(logger: logging.Logger, method: str, url: str, status_code: int = None, duration: float = None):
    """Log API call details"""
    msg = f"🌐 {method} {url}"
    if status_code:
        msg += f" -> {status_code}"
    if duration:
        msg += f" ({duration:.3f}s)"
    logger.debug(msg)


def log_user_action(logger: logging.Logger, action: str, details: str = None):
    """Log user actions"""
    msg = f"👤 User action: {action}"
    if details:
        msg += f" - {details}"
    logger.info(msg)
