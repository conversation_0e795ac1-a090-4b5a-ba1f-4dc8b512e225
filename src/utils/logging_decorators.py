"""
Logging Decorators
Convenient decorators for automatic logging of functions, methods, and performance
"""

import functools
import time
import traceback
from typing import Any, Callable, Optional, Dict
import logging

from .logging_config import get_logger, log_function_entry, log_function_exit, log_api_call


def logged(logger_name: str = None, level: int = logging.DEBUG, 
          log_args: bool = True, log_result: bool = True, 
          log_duration: bool = True):
    """
    Decorator to automatically log function calls
    
    Args:
        logger_name: Name of logger to use (defaults to module name)
        level: Logging level for the messages
        log_args: Whether to log function arguments
        log_result: Whether to log function result
        log_duration: Whether to log execution duration
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get logger
            logger = get_logger(logger_name or func.__module__)
            
            # Log function entry
            if log_args and logger.isEnabledFor(level):
                # Sanitize arguments for logging
                safe_args = _sanitize_args(args, kwargs)
                log_function_entry(logger, func.__name__, **safe_args)
            elif logger.isEnabledFor(level):
                log_function_entry(logger, func.__name__)
            
            # Execute function with timing
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                # Log successful completion
                duration = time.time() - start_time
                if log_result and logger.isEnabledFor(level):
                    safe_result = _sanitize_value(result)
                    log_function_exit(logger, func.__name__, safe_result)
                elif logger.isEnabledFor(level):
                    log_function_exit(logger, func.__name__)
                
                if log_duration and logger.isEnabledFor(level):
                    logger.log(level, f"⏱️ {func.__name__} completed in {duration:.3f}s")
                
                return result
                
            except Exception as e:
                # Log exception
                duration = time.time() - start_time
                logger.error(f"❌ {func.__name__} failed after {duration:.3f}s: {e}")
                logger.debug(f"📍 Exception traceback:\n{traceback.format_exc()}")
                raise
        
        return wrapper
    return decorator


def performance_logged(logger_name: str = None, threshold: float = 1.0):
    """
    Decorator to log performance warnings for slow functions
    
    Args:
        logger_name: Name of logger to use
        threshold: Time threshold in seconds to trigger warning
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or f"{func.__module__}.performance")
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if duration > threshold:
                    logger.warning(f"🐌 Slow function: {func.__name__} took {duration:.3f}s (threshold: {threshold}s)")
                else:
                    logger.debug(f"⚡ {func.__name__} completed in {duration:.3f}s")
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"💥 {func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


def api_logged(logger_name: str = None, log_request: bool = True, log_response: bool = True):
    """
    Decorator for logging API calls
    
    Args:
        logger_name: Name of logger to use
        log_request: Whether to log request details
        log_response: Whether to log response details
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or f"{func.__module__}.api")
            
            # Extract common API parameters
            method = kwargs.get('method', 'GET')
            url = kwargs.get('url', args[0] if args else 'unknown')
            
            if log_request:
                logger.debug(f"🌐 API Request: {method} {url}")
                if 'data' in kwargs:
                    logger.trace(f"📤 Request data: {_sanitize_value(kwargs['data'])}")
                if 'params' in kwargs:
                    logger.trace(f"📋 Request params: {_sanitize_value(kwargs['params'])}")
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log response
                if hasattr(result, 'status_code'):
                    status_code = result.status_code
                    log_api_call(logger, method, url, status_code, duration)
                    
                    if log_response and logger.isEnabledFor(logging.DEBUG):
                        if hasattr(result, 'json'):
                            try:
                                response_data = result.json()
                                logger.trace(f"📥 Response data: {_sanitize_value(response_data)}")
                            except:
                                logger.trace(f"📥 Response text: {result.text[:200]}...")
                else:
                    log_api_call(logger, method, url, duration=duration)
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"🚫 API call failed: {method} {url} after {duration:.3f}s - {e}")
                raise
        
        return wrapper
    return decorator


def error_logged(logger_name: str = None, reraise: bool = True, default_return: Any = None):
    """
    Decorator to log and optionally handle errors
    
    Args:
        logger_name: Name of logger to use
        reraise: Whether to reraise the exception
        default_return: Default value to return if exception occurs and reraise=False
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"💥 Error in {func.__name__}: {e}")
                logger.debug(f"📍 Full traceback:\n{traceback.format_exc()}")
                
                if reraise:
                    raise
                else:
                    logger.warning(f"🔄 Returning default value for {func.__name__}: {default_return}")
                    return default_return
        
        return wrapper
    return decorator


def user_action_logged(action_name: str = None, logger_name: str = None):
    """
    Decorator to log user actions
    
    Args:
        action_name: Name of the action (defaults to function name)
        logger_name: Name of logger to use
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or f"{func.__module__}.user_actions")
            
            action = action_name or func.__name__
            
            # Extract user context if available
            user_context = ""
            if args and hasattr(args[0], '__class__'):
                user_context = f" [{args[0].__class__.__name__}]"
            
            logger.info(f"👤 User action{user_context}: {action}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"✅ User action completed: {action}")
                return result
            except Exception as e:
                logger.error(f"❌ User action failed: {action} - {e}")
                raise
        
        return wrapper
    return decorator


def cached_logged(cache_name: str = "default", logger_name: str = None):
    """
    Decorator to log cache hits and misses
    
    Args:
        cache_name: Name of the cache for logging
        logger_name: Name of logger to use
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or f"{func.__module__}.cache")
            
            # Create cache key
            cache_key = str(args) + str(sorted(kwargs.items()))
            
            if cache_key in cache:
                logger.debug(f"💾 Cache hit for {func.__name__} [{cache_name}]")
                return cache[cache_key]
            else:
                logger.debug(f"🔍 Cache miss for {func.__name__} [{cache_name}]")
                result = func(*args, **kwargs)
                cache[cache_key] = result
                logger.trace(f"💾 Cached result for {func.__name__} [{cache_name}]")
                return result
        
        # Add cache management methods
        wrapper.clear_cache = lambda: cache.clear()
        wrapper.cache_info = lambda: {"size": len(cache), "keys": list(cache.keys())}
        
        return wrapper
    return decorator


def retry_logged(max_attempts: int = 3, delay: float = 1.0, 
                backoff: float = 2.0, logger_name: str = None):
    """
    Decorator to add retry logic with logging
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries
        backoff: Backoff multiplier for delay
        logger_name: Name of logger to use
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    if attempt > 0:
                        logger.info(f"🔄 Retry attempt {attempt + 1}/{max_attempts} for {func.__name__}")
                    
                    result = func(*args, **kwargs)
                    
                    if attempt > 0:
                        logger.info(f"✅ {func.__name__} succeeded on attempt {attempt + 1}")
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    logger.warning(f"⚠️ {func.__name__} failed on attempt {attempt + 1}: {e}")
                    
                    if attempt < max_attempts - 1:
                        logger.debug(f"⏳ Waiting {current_delay:.1f}s before retry...")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"💥 {func.__name__} failed after {max_attempts} attempts")
            
            raise last_exception
        
        return wrapper
    return decorator


def _sanitize_args(args: tuple, kwargs: dict) -> dict:
    """Sanitize function arguments for logging"""
    safe_kwargs = {}
    
    # Add positional args
    for i, arg in enumerate(args):
        safe_kwargs[f'arg_{i}'] = _sanitize_value(arg)
    
    # Add keyword args
    for key, value in kwargs.items():
        safe_kwargs[key] = _sanitize_value(value)
    
    return safe_kwargs


def _sanitize_value(value: Any) -> Any:
    """Sanitize a value for logging (remove sensitive data, limit size)"""
    # Check for sensitive keys
    if isinstance(value, dict):
        sensitive_keys = ['password', 'secret', 'key', 'token', 'api_key', 'client_secret']
        return {
            k: '***REDACTED***' if any(sensitive in k.lower() for sensitive in sensitive_keys)
            else _sanitize_value(v)
            for k, v in value.items()
        }
    
    # Limit string length
    if isinstance(value, str) and len(value) > 200:
        return value[:200] + '...'
    
    # Limit list/tuple length
    if isinstance(value, (list, tuple)) and len(value) > 10:
        return list(value[:10]) + ['...']
    
    return value


# Context manager for logging blocks of code
class LoggedContext:
    """Context manager for logging code blocks"""
    
    def __init__(self, name: str, logger_name: str = None, level: int = logging.DEBUG):
        self.name = name
        self.logger = get_logger(logger_name or 'context')
        self.level = level
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.log(self.level, f"🔵 Starting: {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.log(self.level, f"✅ Completed: {self.name} ({duration:.3f}s)")
        else:
            self.logger.error(f"❌ Failed: {self.name} ({duration:.3f}s) - {exc_val}")
        
        return False  # Don't suppress exceptions
