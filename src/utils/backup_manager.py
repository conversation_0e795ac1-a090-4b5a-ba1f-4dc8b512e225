"""
Backup Manager - Automatic backup and recovery system
Handles story backups, version control, and data recovery
"""

import logging
import json
import shutil
import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import threading
import time

logger = logging.getLogger(__name__)


class BackupManager:
    """Manages automatic backups and recovery"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_config = config.get('backup', {})
        
        # Backup settings
        self.backup_dir = Path(self.backup_config.get('backup_dir', 'backups'))
        self.auto_backup_enabled = self.backup_config.get('auto_backup', True)
        self.backup_interval_minutes = self.backup_config.get('interval_minutes', 30)
        self.max_backups = self.backup_config.get('max_backups', 50)
        self.compress_backups = self.backup_config.get('compress', True)
        
        # Ensure backup directory exists
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Auto-backup thread
        self.backup_thread = None
        self.backup_running = False
        
        if self.auto_backup_enabled:
            self.start_auto_backup()
    
    def start_auto_backup(self):
        """Start automatic backup thread"""
        if self.backup_thread and self.backup_thread.is_alive():
            return
        
        self.backup_running = True
        self.backup_thread = threading.Thread(target=self._auto_backup_loop, daemon=True)
        self.backup_thread.start()
        logger.info(f"Auto-backup started (interval: {self.backup_interval_minutes} minutes)")
    
    def stop_auto_backup(self):
        """Stop automatic backup thread"""
        self.backup_running = False
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
        logger.info("Auto-backup stopped")
    
    def _auto_backup_loop(self):
        """Auto-backup loop running in background thread"""
        while self.backup_running:
            try:
                # Wait for interval
                for _ in range(self.backup_interval_minutes * 60):
                    if not self.backup_running:
                        return
                    time.sleep(1)
                
                # Perform backup
                if self.backup_running:
                    self.create_auto_backup()
                    
            except Exception as e:
                logger.error(f"Auto-backup error: {e}")
                time.sleep(60)  # Wait a minute before retrying
    
    def create_backup(self, name: str = None, description: str = "") -> Optional[str]:
        """Create a manual backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = name or f"manual_backup_{timestamp}"
            
            backup_path = self._create_backup_archive(backup_name, description, backup_type="manual")
            
            if backup_path:
                logger.info(f"Manual backup created: {backup_path}")
                self._cleanup_old_backups()
                return str(backup_path)
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating manual backup: {e}")
            return None
    
    def create_auto_backup(self) -> Optional[str]:
        """Create an automatic backup"""
        try:
            # Check if there are changes since last backup
            if not self._has_changes_since_last_backup():
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"auto_backup_{timestamp}"
            
            backup_path = self._create_backup_archive(backup_name, "Automatic backup", backup_type="auto")
            
            if backup_path:
                logger.debug(f"Auto-backup created: {backup_path}")
                self._cleanup_old_backups()
                return str(backup_path)
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating auto-backup: {e}")
            return None
    
    def _create_backup_archive(self, name: str, description: str, backup_type: str) -> Optional[Path]:
        """Create backup archive"""
        try:
            timestamp = datetime.now().isoformat()
            
            if self.compress_backups:
                backup_file = self.backup_dir / f"{name}.zip"
                
                with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # Add metadata
                    metadata = {
                        'name': name,
                        'description': description,
                        'type': backup_type,
                        'timestamp': timestamp,
                        'version': '1.0'
                    }
                    zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))
                    
                    # Add data files
                    self._add_files_to_zip(zipf)
                    
            else:
                backup_dir = self.backup_dir / name
                backup_dir.mkdir(exist_ok=True)
                
                # Create metadata
                metadata = {
                    'name': name,
                    'description': description,
                    'type': backup_type,
                    'timestamp': timestamp,
                    'version': '1.0'
                }
                
                with open(backup_dir / 'backup_metadata.json', 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                # Copy files
                self._copy_files_to_dir(backup_dir)
                backup_file = backup_dir
            
            return backup_file
            
        except Exception as e:
            logger.error(f"Error creating backup archive: {e}")
            return None
    
    def _add_files_to_zip(self, zipf: zipfile.ZipFile):
        """Add files to zip archive"""
        # Backup directories and files
        backup_paths = [
            'data',
            'config.json',
            'logs',
            '.env'
        ]
        
        for path_str in backup_paths:
            path = Path(path_str)
            if path.exists():
                if path.is_file():
                    zipf.write(path, path.name)
                elif path.is_dir():
                    for file_path in path.rglob('*'):
                        if file_path.is_file():
                            arcname = str(file_path.relative_to('.'))
                            zipf.write(file_path, arcname)
    
    def _copy_files_to_dir(self, backup_dir: Path):
        """Copy files to backup directory"""
        backup_paths = [
            'data',
            'config.json',
            'logs',
            '.env'
        ]
        
        for path_str in backup_paths:
            path = Path(path_str)
            if path.exists():
                dest_path = backup_dir / path.name
                if path.is_file():
                    shutil.copy2(path, dest_path)
                elif path.is_dir():
                    shutil.copytree(path, dest_path, dirs_exist_ok=True)
    
    def _has_changes_since_last_backup(self) -> bool:
        """Check if there are changes since last backup"""
        try:
            # Get last backup time
            last_backup = self.get_latest_backup()
            if not last_backup:
                return True
            
            last_backup_time = datetime.fromisoformat(last_backup['timestamp'])
            
            # Check modification times of key files
            check_paths = ['data', 'config.json']
            
            for path_str in check_paths:
                path = Path(path_str)
                if path.exists():
                    if path.is_file():
                        mod_time = datetime.fromtimestamp(path.stat().st_mtime)
                        if mod_time > last_backup_time:
                            return True
                    elif path.is_dir():
                        for file_path in path.rglob('*'):
                            if file_path.is_file():
                                mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                                if mod_time > last_backup_time:
                                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking for changes: {e}")
            return True  # Assume changes if we can't check
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """List all available backups"""
        backups = []
        
        try:
            for backup_path in self.backup_dir.iterdir():
                if backup_path.is_file() and backup_path.suffix == '.zip':
                    # Compressed backup
                    try:
                        with zipfile.ZipFile(backup_path, 'r') as zipf:
                            metadata_content = zipf.read('backup_metadata.json')
                            metadata = json.loads(metadata_content)
                            metadata['path'] = str(backup_path)
                            metadata['size'] = backup_path.stat().st_size
                            backups.append(metadata)
                    except Exception as e:
                        logger.warning(f"Could not read backup metadata from {backup_path}: {e}")
                
                elif backup_path.is_dir():
                    # Uncompressed backup
                    metadata_file = backup_path / 'backup_metadata.json'
                    if metadata_file.exists():
                        try:
                            with open(metadata_file) as f:
                                metadata = json.load(f)
                                metadata['path'] = str(backup_path)
                                metadata['size'] = sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file())
                                backups.append(metadata)
                        except Exception as e:
                            logger.warning(f"Could not read backup metadata from {metadata_file}: {e}")
            
            # Sort by timestamp (newest first)
            backups.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
        
        return backups
    
    def get_latest_backup(self) -> Optional[Dict[str, Any]]:
        """Get the latest backup"""
        backups = self.list_backups()
        return backups[0] if backups else None
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore from a backup"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                logger.error(f"Backup not found: {backup_path}")
                return False
            
            # Create restore point before restoring
            restore_point = self.create_backup("pre_restore", "Backup before restore")
            if restore_point:
                logger.info(f"Created restore point: {restore_point}")
            
            if backup_path.suffix == '.zip':
                # Restore from compressed backup
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall('.')
            else:
                # Restore from uncompressed backup
                for item in backup_path.iterdir():
                    if item.name == 'backup_metadata.json':
                        continue
                    
                    dest = Path(item.name)
                    if dest.exists():
                        if dest.is_file():
                            dest.unlink()
                        elif dest.is_dir():
                            shutil.rmtree(dest)
                    
                    if item.is_file():
                        shutil.copy2(item, dest)
                    elif item.is_dir():
                        shutil.copytree(item, dest)
            
            logger.info(f"Successfully restored from backup: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring backup: {e}")
            return False
    
    def delete_backup(self, backup_path: str) -> bool:
        """Delete a backup"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                logger.error(f"Backup not found: {backup_path}")
                return False
            
            if backup_path.is_file():
                backup_path.unlink()
            elif backup_path.is_dir():
                shutil.rmtree(backup_path)
            
            logger.info(f"Deleted backup: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting backup: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Clean up old backups to stay within limits"""
        try:
            backups = self.list_backups()
            
            # Separate auto and manual backups
            auto_backups = [b for b in backups if b.get('type') == 'auto']
            manual_backups = [b for b in backups if b.get('type') == 'manual']
            
            # Keep more manual backups than auto backups
            max_auto = max(10, self.max_backups // 2)
            max_manual = self.max_backups - max_auto
            
            # Delete excess auto backups
            if len(auto_backups) > max_auto:
                for backup in auto_backups[max_auto:]:
                    self.delete_backup(backup['path'])
            
            # Delete excess manual backups
            if len(manual_backups) > max_manual:
                for backup in manual_backups[max_manual:]:
                    self.delete_backup(backup['path'])
            
            # Also delete backups older than 30 days (auto backups only)
            cutoff_date = datetime.now() - timedelta(days=30)
            for backup in auto_backups:
                try:
                    backup_date = datetime.fromisoformat(backup['timestamp'])
                    if backup_date < cutoff_date:
                        self.delete_backup(backup['path'])
                except Exception:
                    continue
                    
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
    
    def get_backup_stats(self) -> Dict[str, Any]:
        """Get backup statistics"""
        try:
            backups = self.list_backups()
            
            total_size = sum(backup.get('size', 0) for backup in backups)
            auto_count = len([b for b in backups if b.get('type') == 'auto'])
            manual_count = len([b for b in backups if b.get('type') == 'manual'])
            
            latest_backup = backups[0] if backups else None
            
            return {
                'total_backups': len(backups),
                'auto_backups': auto_count,
                'manual_backups': manual_count,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'latest_backup': latest_backup,
                'auto_backup_enabled': self.auto_backup_enabled,
                'backup_interval_minutes': self.backup_interval_minutes
            }
            
        except Exception as e:
            logger.error(f"Error getting backup stats: {e}")
            return {
                'total_backups': 0,
                'auto_backups': 0,
                'manual_backups': 0,
                'total_size_mb': 0,
                'latest_backup': None,
                'auto_backup_enabled': self.auto_backup_enabled,
                'backup_interval_minutes': self.backup_interval_minutes
            }
