"""
System Health Checker - Monitors all system components and dependencies
Provides health status, diagnostics, and recovery suggestions
"""

import logging
import json
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import psutil
import time

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """Individual health check result"""
    name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = None
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}
        if self.suggestions is None:
            self.suggestions = []


class SystemHealthChecker:
    """Comprehensive system health monitoring"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.checks = {}
        
    def run_all_checks(self) -> Dict[str, HealthCheck]:
        """Run all health checks"""
        self.checks = {}
        
        # Core system checks
        self.checks['python'] = self._check_python()
        self.checks['dependencies'] = self._check_dependencies()
        self.checks['config'] = self._check_configuration()
        self.checks['storage'] = self._check_storage()
        
        # AI service checks
        self.checks['lmstudio'] = self._check_lmstudio()
        self.checks['comfyui'] = self._check_comfyui()
        
        # External service checks
        self.checks['x_api'] = self._check_x_api()
        
        # Performance checks
        self.checks['memory'] = self._check_memory()
        self.checks['disk_space'] = self._check_disk_space()
        
        return self.checks
    
    def get_overall_status(self) -> HealthStatus:
        """Get overall system health status"""
        if not self.checks:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in self.checks.values()]
        
        if HealthStatus.ERROR in statuses:
            return HealthStatus.ERROR
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary"""
        overall_status = self.get_overall_status()
        
        summary = {
            'overall_status': overall_status.value,
            'timestamp': time.time(),
            'checks': {
                name: {
                    'status': check.status.value,
                    'message': check.message,
                    'details': check.details,
                    'suggestions': check.suggestions
                }
                for name, check in self.checks.items()
            },
            'recommendations': self._get_recommendations()
        }
        
        return summary
    
    def _check_python(self) -> HealthCheck:
        """Check Python installation and version"""
        try:
            import sys
            version = sys.version_info
            
            if version.major == 3 and version.minor >= 10:
                return HealthCheck(
                    name="Python",
                    status=HealthStatus.HEALTHY,
                    message=f"Python {version.major}.{version.minor}.{version.micro}",
                    details={'version': f"{version.major}.{version.minor}.{version.micro}"}
                )
            else:
                return HealthCheck(
                    name="Python",
                    status=HealthStatus.WARNING,
                    message=f"Python {version.major}.{version.minor} (recommend 3.10+)",
                    suggestions=["Upgrade to Python 3.10 or higher for best compatibility"]
                )
                
        except Exception as e:
            return HealthCheck(
                name="Python",
                status=HealthStatus.ERROR,
                message=f"Python check failed: {e}",
                suggestions=["Ensure Python is properly installed"]
            )
    
    def _check_dependencies(self) -> HealthCheck:
        """Check required Python dependencies"""
        try:
            # Map package names to their import names
            required_packages = {
                'PyQt6': 'PyQt6.QtWidgets',
                'requests': 'requests',
                'networkx': 'networkx',
                'psutil': 'psutil',
                'rich': 'rich',
                'typer': 'typer',
                'lmstudio': 'lmstudio'
            }

            missing_packages = []
            installed_packages = {}

            for package_name, import_name in required_packages.items():
                try:
                    module = __import__(import_name.split('.')[0])
                    # Try to get version from the main module
                    version = getattr(module, '__version__', 'unknown')
                    installed_packages[package_name] = version
                except ImportError:
                    missing_packages.append(package_name)
            
            if missing_packages:
                return HealthCheck(
                    name="Dependencies",
                    status=HealthStatus.ERROR,
                    message=f"Missing packages: {', '.join(missing_packages)}",
                    details={'missing': missing_packages, 'installed': installed_packages},
                    suggestions=[
                        f"Install missing packages via pixi: pixi add {' '.join(missing_packages).lower()}",
                        f"Or via pip: pip install {' '.join(missing_packages).lower()}"
                    ]
                )
            else:
                return HealthCheck(
                    name="Dependencies",
                    status=HealthStatus.HEALTHY,
                    message=f"All {len(required_packages)} required packages installed",
                    details={'installed': installed_packages}
                )
                
        except Exception as e:
            return HealthCheck(
                name="Dependencies",
                status=HealthStatus.ERROR,
                message=f"Dependency check failed: {e}",
                suggestions=["Check Python package installation"]
            )
    
    def _check_configuration(self) -> HealthCheck:
        """Check configuration files"""
        try:
            config_path = Path("config.json")
            env_path = Path(".env")
            
            issues = []
            details = {}
            
            # Check config.json
            if not config_path.exists():
                issues.append("config.json not found")
            else:
                try:
                    with open(config_path) as f:
                        config_data = json.load(f)
                    details['config_keys'] = list(config_data.keys())
                except json.JSONDecodeError:
                    issues.append("config.json is invalid JSON")
            
            # Check .env file
            if not env_path.exists():
                issues.append(".env file not found (optional)")
                details['env_status'] = 'missing'
            else:
                details['env_status'] = 'present'
            
            if issues:
                return HealthCheck(
                    name="Configuration",
                    status=HealthStatus.WARNING if ".env" in str(issues) else HealthStatus.ERROR,
                    message=f"Configuration issues: {', '.join(issues)}",
                    details=details,
                    suggestions=["Create missing configuration files", "Check configuration format"]
                )
            else:
                return HealthCheck(
                    name="Configuration",
                    status=HealthStatus.HEALTHY,
                    message="Configuration files present and valid",
                    details=details
                )
                
        except Exception as e:
            return HealthCheck(
                name="Configuration",
                status=HealthStatus.ERROR,
                message=f"Configuration check failed: {e}",
                suggestions=["Check file permissions and format"]
            )
    
    def _check_storage(self) -> HealthCheck:
        """Check storage directories and permissions"""
        try:
            required_dirs = ['data', 'logs', 'videos', 'workflows']
            issues = []
            details = {}
            
            for dir_name in required_dirs:
                dir_path = Path(dir_name)
                if not dir_path.exists():
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        details[dir_name] = 'created'
                    except Exception:
                        issues.append(f"Cannot create {dir_name} directory")
                        details[dir_name] = 'failed'
                else:
                    details[dir_name] = 'exists'
            
            if issues:
                return HealthCheck(
                    name="Storage",
                    status=HealthStatus.ERROR,
                    message=f"Storage issues: {', '.join(issues)}",
                    details=details,
                    suggestions=["Check directory permissions", "Ensure sufficient disk space"]
                )
            else:
                return HealthCheck(
                    name="Storage",
                    status=HealthStatus.HEALTHY,
                    message="All required directories available",
                    details=details
                )
                
        except Exception as e:
            return HealthCheck(
                name="Storage",
                status=HealthStatus.ERROR,
                message=f"Storage check failed: {e}",
                suggestions=["Check file system permissions"]
            )
    
    def _check_lmstudio(self) -> HealthCheck:
        """Check LM Studio service"""
        try:
            from .lmstudio_client import LMStudioClient
            client = LMStudioClient()
            test_result = client.test_connection()

            if test_result['status'] == 'success':
                details = test_result.get('details', {})
                models = details.get('available_models', [])
                return HealthCheck(
                    name="LM Studio",
                    status=HealthStatus.HEALTHY,
                    message=f"LM Studio running with {len(models)} models",
                    details={
                        "models": models,
                        "current_model": details.get('current_model'),
                        "response_time": details.get('response_time'),
                        "url": client.base_url
                    }
                )
            elif test_result['status'] == 'warning':
                return HealthCheck(
                    name="LM Studio",
                    status=HealthStatus.WARNING,
                    message=test_result['message'],
                    suggestions=test_result.get('suggestions', [])
                )
            else:
                return HealthCheck(
                    name="LM Studio",
                    status=HealthStatus.ERROR,
                    message=test_result['message'],
                    suggestions=test_result.get('suggestions', [])
                )

        except Exception as e:
            return HealthCheck(
                name="LM Studio",
                status=HealthStatus.ERROR,
                message=f"LM Studio check failed: {e}",
                suggestions=[
                    "Install LM Studio from https://lmstudio.ai",
                    "Start LM Studio application",
                    "Load a model in LM Studio"
                ]
            )
    
    def _check_comfyui(self) -> HealthCheck:
        """Check ComfyUI service"""
        try:
            comfyui_url = self.config.get('comfyui', {}).get('base_url', 'http://127.0.0.1:8188')
            
            try:
                response = requests.get(f"{comfyui_url}/system_stats", timeout=5)
                if response.status_code == 200:
                    stats = response.json()
                    return HealthCheck(
                        name="ComfyUI",
                        status=HealthStatus.HEALTHY,
                        message="ComfyUI running and responsive",
                        details={'url': comfyui_url, 'stats': stats}
                    )
                else:
                    return HealthCheck(
                        name="ComfyUI",
                        status=HealthStatus.WARNING,
                        message="ComfyUI responding but API issues",
                        suggestions=["Check ComfyUI service status"]
                    )
            except requests.RequestException:
                return HealthCheck(
                    name="ComfyUI",
                    status=HealthStatus.WARNING,
                    message="ComfyUI not responding (optional)",
                    details={'url': comfyui_url},
                    suggestions=[
                        "Install ComfyUI for video generation",
                        "Start ComfyUI: python main.py",
                        "Video generation will use fallback mode"
                    ]
                )
                
        except Exception as e:
            return HealthCheck(
                name="ComfyUI",
                status=HealthStatus.WARNING,
                message=f"ComfyUI check failed: {e} (optional)",
                suggestions=["ComfyUI is optional for basic functionality"]
            )
    
    def _check_x_api(self) -> HealthCheck:
        """Check X (Twitter) API credentials"""
        try:
            # Check for environment variables
            import os
            
            required_vars = ['X_API_KEY', 'X_API_SECRET', 'X_ACCESS_TOKEN', 'X_ACCESS_TOKEN_SECRET']
            missing_vars = [var for var in required_vars if not os.getenv(var)]
            
            if missing_vars:
                return HealthCheck(
                    name="X API",
                    status=HealthStatus.WARNING,
                    message=f"Missing X API credentials (optional)",
                    details={'missing_vars': missing_vars},
                    suggestions=[
                        "Set up X API credentials in .env file",
                        "Get credentials from developer.twitter.com",
                        "X posting will be disabled without credentials"
                    ]
                )
            else:
                return HealthCheck(
                    name="X API",
                    status=HealthStatus.HEALTHY,
                    message="X API credentials configured",
                    details={'configured_vars': required_vars}
                )
                
        except Exception as e:
            return HealthCheck(
                name="X API",
                status=HealthStatus.WARNING,
                message=f"X API check failed: {e} (optional)",
                suggestions=["X API is optional for basic functionality"]
            )
    
    def _check_memory(self) -> HealthCheck:
        """Check system memory usage"""
        try:
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            usage_percent = memory.percent
            
            if memory_gb < 8:
                status = HealthStatus.WARNING
                message = f"Low system memory: {memory_gb:.1f}GB"
                suggestions = ["Consider upgrading to 16GB+ RAM for better performance"]
            elif usage_percent > 90:
                status = HealthStatus.WARNING
                message = f"High memory usage: {usage_percent:.1f}%"
                suggestions = ["Close unnecessary applications", "Restart system if needed"]
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory: {memory_gb:.1f}GB total, {usage_percent:.1f}% used"
                suggestions = []
            
            return HealthCheck(
                name="Memory",
                status=status,
                message=message,
                details={
                    'total_gb': round(memory_gb, 1),
                    'usage_percent': round(usage_percent, 1),
                    'available_gb': round(memory.available / (1024**3), 1)
                },
                suggestions=suggestions
            )
            
        except Exception as e:
            return HealthCheck(
                name="Memory",
                status=HealthStatus.ERROR,
                message=f"Memory check failed: {e}",
                suggestions=["Check system monitoring tools"]
            )
    
    def _check_disk_space(self) -> HealthCheck:
        """Check available disk space"""
        try:
            disk = psutil.disk_usage('.')
            free_gb = disk.free / (1024**3)
            total_gb = disk.total / (1024**3)
            usage_percent = (disk.used / disk.total) * 100
            
            if free_gb < 5:
                status = HealthStatus.ERROR
                message = f"Critical: Only {free_gb:.1f}GB free space"
                suggestions = ["Free up disk space immediately", "Delete unnecessary files"]
            elif free_gb < 20:
                status = HealthStatus.WARNING
                message = f"Low disk space: {free_gb:.1f}GB free"
                suggestions = ["Clean up old files", "Consider expanding storage"]
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk space: {free_gb:.1f}GB free of {total_gb:.1f}GB"
                suggestions = []
            
            return HealthCheck(
                name="Disk Space",
                status=status,
                message=message,
                details={
                    'free_gb': round(free_gb, 1),
                    'total_gb': round(total_gb, 1),
                    'usage_percent': round(usage_percent, 1)
                },
                suggestions=suggestions
            )
            
        except Exception as e:
            return HealthCheck(
                name="Disk Space",
                status=HealthStatus.ERROR,
                message=f"Disk space check failed: {e}",
                suggestions=["Check file system status"]
            )
    
    def _get_recommendations(self) -> List[str]:
        """Get overall system recommendations"""
        recommendations = []
        
        # Collect all suggestions
        all_suggestions = []
        for check in self.checks.values():
            all_suggestions.extend(check.suggestions)
        
        # Add general recommendations based on status
        overall_status = self.get_overall_status()
        
        if overall_status == HealthStatus.ERROR:
            recommendations.append("🚨 Critical issues detected - address errors before proceeding")
        elif overall_status == HealthStatus.WARNING:
            recommendations.append("⚠️ Some issues detected - consider addressing warnings")
        else:
            recommendations.append("✅ System is healthy and ready for use")
        
        # Add specific suggestions
        recommendations.extend(all_suggestions[:5])  # Limit to top 5
        
        return recommendations
