"""
Ollama Client - Interface for local LLM communication
Handles story generation, prompt crafting, and text processing
"""

import json
import requests
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class OllamaResponse:
    """Response from Ollama API"""
    text: str
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = None


class OllamaClient:
    """Client for communicating with local Ollama server"""
    
    def __init__(self, base_url: str = "http://localhost:11434", 
                 model: str = "llama3.1", timeout: int = 60):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.timeout = timeout
        self.session = requests.Session()
        
    def is_available(self) -> bool:
        """Check if Ollama server is available"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama server not available: {e}")
            return False
    
    def generate_text(self, prompt: str, max_tokens: int = 1000, 
                     temperature: float = 0.7, retry_count: int = 3) -> OllamaResponse:
        """Generate text using Ollama"""
        for attempt in range(retry_count):
            try:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "num_predict": max_tokens,
                        "temperature": temperature,
                        "top_p": 0.9,
                        "stop": ["</story>", "<END>"]
                    }
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return OllamaResponse(
                        text=result.get("response", "").strip(),
                        success=True,
                        metadata={
                            "model": result.get("model"),
                            "total_duration": result.get("total_duration"),
                            "load_duration": result.get("load_duration")
                        }
                    )
                else:
                    logger.warning(f"Ollama API error (attempt {attempt + 1}): {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error generating text (attempt {attempt + 1}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        return OllamaResponse(
            text="The adventure ends. The End.",
            success=False,
            error="Failed to generate text after retries"
        )
    
    def generate_story_node(self, context: str, inventory: Dict[str, int], 
                          player_class: str, node_type: str = "story",
                          spicy_rating: bool = False) -> OllamaResponse:
        """Generate a single story node with choices"""
        
        inventory_text = ", ".join([f"{k}: {v}" for k, v in inventory.items()])
        spicy_context = " Include romantic or intimate elements." if spicy_rating else ""
        
        prompt = f"""
You are writing a Choose Your Own Adventure story node. 

Context: {context}
Player Class: {player_class}
Current Inventory: {inventory_text}
Node Type: {node_type}
{spicy_context}

Write a story segment of 50-100 words that:
1. Continues the narrative naturally
2. Considers the player's class abilities ({player_class})
3. References inventory items when relevant
4. Ends with 2-5 meaningful choices
5. Each choice should be 10-20 words

Format your response as JSON:
{{
    "text": "The story text here...",
    "choices": [
        {{
            "text": "Choice 1 text",
            "inventory_requirements": {{"gold": 50}},
            "inventory_changes": {{"gold": -50, "sword": 1}},
            "class_requirements": ["Mage"],
            "is_premium": false,
            "is_spicy": false
        }}
    ],
    "is_ending": false,
    "ending_type": null,
    "rating": "safe"
}}

Ensure choices reflect class abilities and inventory state.
"""
        
        response = self.generate_text(prompt, max_tokens=800)
        
        if response.success:
            try:
                # Try to parse JSON from response
                json_start = response.text.find('{')
                json_end = response.text.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_text = response.text[json_start:json_end]
                    parsed_data = json.loads(json_text)
                    response.metadata = response.metadata or {}
                    response.metadata['parsed_json'] = parsed_data
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from story node response: {e}")
                response.success = False
                response.error = "Invalid JSON response"
        
        return response
    
    def generate_storyline_from_text(self, source_text: str, title: str,
                                   initial_inventory: Dict[str, int],
                                   player_class: str) -> OllamaResponse:
        """Generate a complete storyline from source text"""
        
        inventory_text = ", ".join([f"{k}: {v}" for k, v in initial_inventory.items()])
        
        prompt = f"""
Convert this story into a Choose Your Own Adventure format:

Title: {title}
Source Text: {source_text}

Player Class: {player_class}
Starting Inventory: {inventory_text}

Create a storyline with:
- 5-15 nodes following the original story arc
- Each node 50-100 words
- 2-5 choices per non-ending node
- At least 2 endings (1 death, 1 success)
- Choices that consider inventory and class abilities
- Inventory changes that make sense (buying, finding, using items)

Format as JSON array of nodes:
[
    {{
        "id": "node_0",
        "text": "Story text...",
        "is_entry": true,
        "is_ending": false,
        "choices": [
            {{
                "id": "choice_0_1",
                "text": "Choice text",
                "target_node_id": "node_1",
                "inventory_requirements": {{}},
                "inventory_changes": {{}},
                "class_requirements": [],
                "is_premium": false,
                "is_spicy": false
            }}
        ],
        "rating": "safe"
    }}
]

Ensure the storyline follows the original narrative while adding meaningful choices.
"""
        
        return self.generate_text(prompt, max_tokens=2000)
    
    def generate_intermediary_nodes(self, storyline1_summary: str, 
                                  storyline2_summary: str,
                                  connection_context: str) -> OllamaResponse:
        """Generate intermediary nodes to connect storylines"""
        
        prompt = f"""
Create intermediary nodes to connect these two storylines:

Storyline 1: {storyline1_summary}
Storyline 2: {storyline2_summary}
Connection Context: {connection_context}

Generate 2-4 intermediary nodes that:
1. Provide smooth narrative transition
2. Offer meaningful choices
3. Can funnel to either storyline
4. Include side mission opportunities
5. Consider inventory and class systems

Format as JSON array of nodes with full choice structures.
Each node should be 50-100 words with 2-5 choices.
"""
        
        return self.generate_text(prompt, max_tokens=1500)
    
    def generate_video_prompt(self, node_text: str, choice_text: str,
                            player_class: str, is_spicy: bool = False) -> OllamaResponse:
        """Generate visual prompt for video generation"""
        
        spicy_context = " Include romantic or sensual elements." if is_spicy else ""
        
        prompt = f"""
Create a visual prompt for AI video generation based on this story content:

Story Text: {node_text}
Choice: {choice_text}
Character Class: {player_class}
{spicy_context}

Generate a detailed visual prompt (50-100 words) that:
1. Describes the scene visually
2. Includes character appearance based on class
3. Captures the mood and atmosphere
4. Specifies camera angle and lighting
5. Mentions any relevant props or environment

Keep it cinematic and engaging. Focus on visual elements only.
"""
        
        return self.generate_text(prompt, max_tokens=200)
    
    def generate_audio_script(self, node_text: str, is_spicy: bool = False) -> OllamaResponse:
        """Generate audio script for TTS"""
        
        prompt = f"""
Convert this story text into a natural-sounding audio script for text-to-speech:

Story Text: {node_text}

Create a script that:
1. Flows naturally when spoken
2. Includes appropriate pauses [PAUSE]
3. Emphasizes dramatic moments [EMPHASIS]
4. Is 30-60 seconds when spoken
5. Maintains the story's tone

Return only the audio script text with timing markers.
"""
        
        return self.generate_text(prompt, max_tokens=300)
    
    def analyze_content_rating(self, text: str) -> OllamaResponse:
        """Analyze content for spicy rating"""
        
        prompt = f"""
Analyze this text for content rating:

Text: {text}

Determine if this content should be rated as "spicy" based on:
- Romantic content
- Intimate situations
- Suggestive themes
- Adult content

Respond with JSON:
{{
    "rating": "safe" or "spicy",
    "confidence": 0.0-1.0,
    "reasons": ["reason1", "reason2"]
}}
"""
        
        return self.generate_text(prompt, max_tokens=200)

    def extract_characters_from_text(self, source_text: str, title: str) -> OllamaResponse:
        """Extract characters from source text"""
        try:
            prompt = f"""
Analyze this story text and extract all the main characters. For each character, provide:
- name
- role (protagonist, antagonist, ally, neutral, mentor, love_interest, comic_relief, background)
- appearance (physical description)
- age (age range like "young adult", "middle-aged", etc.)
- hair_color
- eye_color
- clothing (typical attire)
- distinctive_features
- voice_description
- accent (if any)
- speech_patterns (list of speech quirks)
- vocabulary_style (formal, casual, archaic, etc.)
- traits (personality traits list)
- motivations (what drives them)
- fears (what they're afraid of)
- goals (what they want to achieve)
- moral_alignment (good, evil, neutral, chaotic, lawful, etc.)
- background (brief background story)
- quirks (unique behaviors or habits)
- importance (1-5, where 5 is most important)

Story Title: {title}

Story Text:
{source_text}

Return the characters as a JSON array. Example format:
[
  {{
    "name": "Character Name",
    "role": "protagonist",
    "appearance": "Physical description",
    "age": "young adult",
    "hair_color": "brown",
    "eye_color": "blue",
    "clothing": "simple peasant clothes",
    "distinctive_features": "scar on left cheek",
    "voice_description": "clear and confident",
    "accent": "slight rural accent",
    "speech_patterns": ["uses old sayings", "speaks slowly when thinking"],
    "vocabulary_style": "casual",
    "traits": ["brave", "curious", "stubborn"],
    "motivations": ["save the kingdom", "find their father"],
    "fears": ["heights", "losing loved ones"],
    "goals": ["become a knight", "restore family honor"],
    "moral_alignment": "good",
    "background": "Grew up on a farm, lost parents in war",
    "quirks": ["always fidgets with a coin", "hums when nervous"],
    "importance": 5
  }}
]

Focus on characters that actually appear in the story and have speaking roles or significant impact on the plot.
"""

            response = self.generate_text(prompt, max_tokens=2000)
            return response

        except Exception as e:
            logger.error(f"Error extracting characters: {e}")
            return OllamaResponse(success=False, error=str(e))

    def generate_character_dialogue(self, character_description: str, context: str,
                                  emotional_state: str = "neutral") -> OllamaResponse:
        """Generate character-specific dialogue"""
        try:
            prompt = f"""
Generate dialogue for this character in the given context:

Character Description:
{character_description}

Current Context: {context}
Emotional State: {emotional_state}

Generate 1-3 lines of dialogue that:
1. Matches their personality and speech patterns
2. Reflects their current emotional state
3. Is appropriate for the context
4. Stays true to their established character traits

Return only the dialogue, without quotes or character names.
"""

            response = self.generate_text(prompt, max_tokens=200)
            return response

        except Exception as e:
            logger.error(f"Error generating character dialogue: {e}")
            return OllamaResponse(success=False, error=str(e))

    def suggest_character_actions(self, character_description: str, context: str) -> OllamaResponse:
        """Suggest actions a character might take"""
        try:
            prompt = f"""
Based on this character's personality and the current situation, suggest 3-5 actions they might take:

Character Description:
{character_description}

Current Situation: {context}

Consider their:
- Personality traits and motivations
- Typical behavior patterns
- Goals and fears
- Moral alignment
- Current emotional state

List actions as bullet points that would be consistent with their character.
Focus on actions that would advance the story or create interesting choices.
"""

            response = self.generate_text(prompt, max_tokens=300)
            return response

        except Exception as e:
            logger.error(f"Error suggesting character actions: {e}")
            return OllamaResponse(success=False, error=str(e))

    def analyze_character_relationships(self, characters: List[str], context: str) -> OllamaResponse:
        """Analyze relationships between characters"""
        try:
            char_list = "\n".join([f"- {char}" for char in characters])

            prompt = f"""
Analyze the relationships between these characters in the given context:

Characters:
{char_list}

Context: {context}

For each pair of characters, describe their relationship:
- How they know each other
- Their current relationship status (friends, enemies, strangers, etc.)
- Any history between them
- How they might interact in this context

Return as a structured analysis of character relationships.
"""

            response = self.generate_text(prompt, max_tokens=500)
            return response

        except Exception as e:
            logger.error(f"Error analyzing character relationships: {e}")
            return OllamaResponse(success=False, error=str(e))
