"""
Enhanced Text-to-Speech System with Voice Profiles
Supports character voices and voice cloning integration
"""

import logging
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
import threading
import queue

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False

try:
    import torch
    import torchaudio
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QComboBox, QLabel, QSlider

logger = logging.getLogger(__name__)


@dataclass
class VoiceProfile:
    """Voice profile configuration"""
    id: str
    name: str
    character_id: Optional[str] = None
    engine: str = "pyttsx3"  # "pyttsx3", "tortoise", "custom"
    voice_id: Optional[str] = None
    rate: int = 200
    volume: float = 0.9
    pitch: float = 1.0
    emotion: str = "neutral"
    sample_path: Optional[str] = None
    model_path: Optional[str] = None
    enabled: bool = True


class TTSEngine:
    """Base TTS engine interface"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_available = False
    
    def speak(self, text: str, voice_profile: VoiceProfile) -> bool:
        """Speak text with given voice profile"""
        raise NotImplementedError
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get list of available voices"""
        raise NotImplementedError
    
    def stop(self):
        """Stop current speech"""
        raise NotImplementedError


class Pyttsx3Engine(TTSEngine):
    """Pyttsx3 TTS engine"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.engine = None
        
        if PYTTSX3_AVAILABLE:
            try:
                self.engine = pyttsx3.init()
                self.is_available = True
                logger.info("Pyttsx3 TTS engine initialized")
            except Exception as e:
                logger.error(f"Failed to initialize pyttsx3: {e}")
    
    def speak(self, text: str, voice_profile: VoiceProfile) -> bool:
        """Speak text with voice profile"""
        if not self.engine:
            return False
        
        try:
            # Set voice properties
            if voice_profile.voice_id:
                voices = self.engine.getProperty('voices')
                for voice in voices:
                    if voice.id == voice_profile.voice_id:
                        self.engine.setProperty('voice', voice.id)
                        break
            
            self.engine.setProperty('rate', voice_profile.rate)
            self.engine.setProperty('volume', voice_profile.volume)
            
            # Speak the text
            self.engine.say(text)
            self.engine.runAndWait()
            
            return True
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return False
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get available voices"""
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            return [
                {
                    "id": voice.id,
                    "name": voice.name,
                    "language": getattr(voice, 'languages', ['unknown'])[0] if hasattr(voice, 'languages') else 'unknown'
                }
                for voice in voices
            ]
        except Exception as e:
            logger.error(f"Error getting voices: {e}")
            return []
    
    def stop(self):
        """Stop current speech"""
        if self.engine:
            try:
                self.engine.stop()
            except Exception as e:
                logger.error(f"Error stopping TTS: {e}")


class TTSWorker(QThread):
    """Worker thread for TTS operations"""
    
    speech_started = pyqtSignal()
    speech_finished = pyqtSignal()
    speech_error = pyqtSignal(str)
    
    def __init__(self, engine: TTSEngine, text: str, voice_profile: VoiceProfile):
        super().__init__()
        self.engine = engine
        self.text = text
        self.voice_profile = voice_profile
    
    def run(self):
        """Run TTS in thread"""
        try:
            self.speech_started.emit()
            success = self.engine.speak(self.text, self.voice_profile)
            
            if success:
                self.speech_finished.emit()
            else:
                self.speech_error.emit("TTS failed")
                
        except Exception as e:
            self.speech_error.emit(str(e))


class VoiceProfileManager:
    """Manages voice profiles"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.profiles: Dict[str, VoiceProfile] = {}
        self.profiles_file = Path("data/voice_profiles.json")
        
        self._load_profiles()
        self._create_default_profiles()
        
        logger.info(f"Voice profile manager initialized with {len(self.profiles)} profiles")
    
    def _load_profiles(self):
        """Load voice profiles from file"""
        if self.profiles_file.exists():
            try:
                with open(self.profiles_file, 'r') as f:
                    data = json.load(f)
                
                for profile_data in data.get('profiles', []):
                    profile = VoiceProfile(**profile_data)
                    self.profiles[profile.id] = profile
                    
                logger.info(f"Loaded {len(self.profiles)} voice profiles")
                
            except Exception as e:
                logger.error(f"Error loading voice profiles: {e}")
    
    def _create_default_profiles(self):
        """Create default voice profiles"""
        if not self.profiles:
            default_profiles = [
                VoiceProfile(
                    id="narrator",
                    name="Narrator",
                    rate=180,
                    volume=0.8,
                    emotion="neutral"
                ),
                VoiceProfile(
                    id="character_male",
                    name="Male Character",
                    rate=200,
                    volume=0.9,
                    emotion="friendly"
                ),
                VoiceProfile(
                    id="character_female", 
                    name="Female Character",
                    rate=220,
                    volume=0.9,
                    emotion="friendly"
                ),
                VoiceProfile(
                    id="assistant",
                    name="AI Assistant",
                    rate=190,
                    volume=0.8,
                    emotion="helpful"
                )
            ]
            
            for profile in default_profiles:
                self.profiles[profile.id] = profile
            
            self.save_profiles()
    
    def save_profiles(self):
        """Save voice profiles to file"""
        try:
            self.profiles_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                "profiles": [asdict(profile) for profile in self.profiles.values()]
            }
            
            with open(self.profiles_file, 'w') as f:
                json.dump(data, f, indent=2)
                
            logger.info("Voice profiles saved")
            
        except Exception as e:
            logger.error(f"Error saving voice profiles: {e}")
    
    def add_profile(self, profile: VoiceProfile):
        """Add a voice profile"""
        self.profiles[profile.id] = profile
        self.save_profiles()
    
    def remove_profile(self, profile_id: str):
        """Remove a voice profile"""
        if profile_id in self.profiles:
            del self.profiles[profile_id]
            self.save_profiles()
    
    def get_profile(self, profile_id: str) -> Optional[VoiceProfile]:
        """Get a voice profile by ID"""
        return self.profiles.get(profile_id)
    
    def get_profiles_for_character(self, character_id: str) -> List[VoiceProfile]:
        """Get voice profiles for a character"""
        return [
            profile for profile in self.profiles.values()
            if profile.character_id == character_id
        ]
    
    def get_all_profiles(self) -> List[VoiceProfile]:
        """Get all voice profiles"""
        return list(self.profiles.values())


class TTSManager:
    """Main TTS manager with voice profile support"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.voice_manager = VoiceProfileManager(config)
        self.engines: Dict[str, TTSEngine] = {}
        self.current_worker: Optional[TTSWorker] = None
        
        self._initialize_engines()
        
        logger.info("TTS manager initialized")
    
    def _initialize_engines(self):
        """Initialize TTS engines"""
        # Initialize pyttsx3 engine
        pyttsx3_engine = Pyttsx3Engine(self.config)
        if pyttsx3_engine.is_available:
            self.engines["pyttsx3"] = pyttsx3_engine
        
        # Could add more engines here (Tortoise TTS, etc.)
        
        logger.info(f"Initialized {len(self.engines)} TTS engines")
    
    def speak(self, text: str, voice_profile_id: str = "narrator") -> bool:
        """Speak text with specified voice profile"""
        profile = self.voice_manager.get_profile(voice_profile_id)
        if not profile:
            logger.error(f"Voice profile not found: {voice_profile_id}")
            return False
        
        engine = self.engines.get(profile.engine)
        if not engine:
            logger.error(f"TTS engine not available: {profile.engine}")
            return False
        
        # Stop current speech
        self.stop()
        
        # Start new speech in worker thread
        self.current_worker = TTSWorker(engine, text, profile)
        self.current_worker.start()
        
        return True
    
    def stop(self):
        """Stop current speech"""
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()
            self.current_worker.wait()
        
        # Stop all engines
        for engine in self.engines.values():
            engine.stop()
    
    def get_available_voices(self, engine_name: str = "pyttsx3") -> List[Dict[str, str]]:
        """Get available voices for engine"""
        engine = self.engines.get(engine_name)
        if engine:
            return engine.get_available_voices()
        return []
    
    def create_tts_widget(self, parent: QWidget = None) -> 'TTSWidget':
        """Create a TTS control widget"""
        return TTSWidget(self, parent)


class TTSWidget(QWidget):
    """TTS control widget"""
    
    def __init__(self, tts_manager: TTSManager, parent: QWidget = None):
        super().__init__(parent)
        self.tts_manager = tts_manager
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the widget UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Voice profile selector
        self.voice_combo = QComboBox()
        self.voice_combo.setToolTip("Select voice profile")
        self._update_voice_list()
        layout.addWidget(self.voice_combo)
        
        # Speak button
        self.speak_button = QPushButton("🔊")
        self.speak_button.setFixedSize(30, 30)
        self.speak_button.setToolTip("Speak text")
        self.speak_button.clicked.connect(self._on_speak_clicked)
        layout.addWidget(self.speak_button)
        
        # Stop button
        self.stop_button = QPushButton("⏹")
        self.stop_button.setFixedSize(30, 30)
        self.stop_button.setToolTip("Stop speech")
        self.stop_button.clicked.connect(self.tts_manager.stop)
        layout.addWidget(self.stop_button)
    
    def _update_voice_list(self):
        """Update voice profile list"""
        self.voice_combo.clear()
        
        profiles = self.tts_manager.voice_manager.get_all_profiles()
        for profile in profiles:
            if profile.enabled:
                self.voice_combo.addItem(profile.name, profile.id)
    
    def _on_speak_clicked(self):
        """Handle speak button click"""
        # This would need to be connected to a text source
        # For now, just a placeholder
        profile_id = self.voice_combo.currentData()
        if profile_id:
            self.tts_manager.speak("Hello, this is a test.", profile_id)
    
    def speak_text(self, text: str):
        """Speak the given text with selected voice"""
        profile_id = self.voice_combo.currentData()
        if profile_id and text.strip():
            self.tts_manager.speak(text, profile_id)
    
    def set_voice_profile(self, profile_id: str):
        """Set the selected voice profile"""
        for i in range(self.voice_combo.count()):
            if self.voice_combo.itemData(i) == profile_id:
                self.voice_combo.setCurrentIndex(i)
                break
