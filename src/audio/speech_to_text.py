"""
Speech-to-Text Integration
Provides real-time speech recognition for input fields
"""

import logging
import threading
import queue
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

try:
    import speech_recognition as sr
    import pyaudio
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False

from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import Q<PERSON>ush<PERSON>utton, QWidget, QHBoxLayout, QLabel

logger = logging.getLogger(__name__)


@dataclass
class STTConfig:
    """Speech-to-text configuration"""
    language: str = "en-US"
    timeout: float = 1.0
    phrase_timeout: float = 0.3
    energy_threshold: int = 300
    dynamic_energy_threshold: bool = True
    pause_threshold: float = 0.8


class SpeechRecognitionWorker(QThread):
    """Worker thread for speech recognition"""
    
    text_recognized = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    listening_started = pyqtSignal()
    listening_stopped = pyqtSignal()
    
    def __init__(self, config: STTConfig):
        super().__init__()
        self.config = config
        self.recognizer = None
        self.microphone = None
        self.is_listening = False
        self.audio_queue = queue.Queue()
        
        if SPEECH_RECOGNITION_AVAILABLE:
            self._initialize_recognizer()
    
    def _initialize_recognizer(self):
        """Initialize the speech recognizer"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            # Configure recognizer
            self.recognizer.energy_threshold = self.config.energy_threshold
            self.recognizer.dynamic_energy_threshold = self.config.dynamic_energy_threshold
            self.recognizer.pause_threshold = self.config.pause_threshold
            
            logger.info("Speech recognizer initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize speech recognizer: {e}")
            self.recognizer = None
            self.microphone = None
    
    def start_listening(self):
        """Start listening for speech"""
        if not self.recognizer or not self.microphone:
            self.error_occurred.emit("Speech recognition not available")
            return
        
        self.is_listening = True
        self.start()
    
    def stop_listening(self):
        """Stop listening for speech"""
        self.is_listening = False
        self.wait()
    
    def run(self):
        """Main recognition loop"""
        if not self.recognizer or not self.microphone:
            return
        
        self.listening_started.emit()
        
        try:
            with self.microphone as source:
                while self.is_listening:
                    try:
                        # Listen for audio with timeout
                        audio = self.recognizer.listen(
                            source, 
                            timeout=self.config.timeout,
                            phrase_time_limit=5.0
                        )
                        
                        if not self.is_listening:
                            break
                        
                        # Recognize speech
                        text = self.recognizer.recognize_google(
                            audio, 
                            language=self.config.language
                        )
                        
                        if text.strip():
                            self.text_recognized.emit(text)
                            
                    except sr.WaitTimeoutError:
                        # Normal timeout, continue listening
                        continue
                    except sr.UnknownValueError:
                        # Could not understand audio
                        continue
                    except sr.RequestError as e:
                        self.error_occurred.emit(f"Recognition service error: {e}")
                        break
                    except Exception as e:
                        logger.error(f"Speech recognition error: {e}")
                        continue
                        
        except Exception as e:
            self.error_occurred.emit(f"Microphone error: {e}")
        finally:
            self.listening_stopped.emit()


class STTButton(QPushButton):
    """Speech-to-text button widget"""
    
    text_recognized = pyqtSignal(str)
    
    def __init__(self, config: STTConfig = None, parent: QWidget = None):
        super().__init__("🎤", parent)
        
        self.config = config or STTConfig()
        self.worker = None
        self.is_recording = False
        
        self.setToolTip("Click and hold to record speech")
        self.setFixedSize(30, 30)
        self.setCheckable(True)
        
        # Style the button
        self.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: none;
                border-radius: 15px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:checked {
                background-color: #f44336;
                animation: pulse 1s infinite;
            }
        """)
        
        self.clicked.connect(self._toggle_recording)
        
        if not SPEECH_RECOGNITION_AVAILABLE:
            self.setEnabled(False)
            self.setToolTip("Speech recognition not available - install speech_recognition and pyaudio")
    
    def _toggle_recording(self):
        """Toggle speech recording"""
        if not self.is_recording:
            self._start_recording()
        else:
            self._stop_recording()
    
    def _start_recording(self):
        """Start speech recording"""
        if not SPEECH_RECOGNITION_AVAILABLE:
            return
        
        try:
            self.worker = SpeechRecognitionWorker(self.config)
            self.worker.text_recognized.connect(self._on_text_recognized)
            self.worker.error_occurred.connect(self._on_error)
            self.worker.listening_started.connect(self._on_listening_started)
            self.worker.listening_stopped.connect(self._on_listening_stopped)
            
            self.worker.start_listening()
            
        except Exception as e:
            logger.error(f"Failed to start recording: {e}")
            self._on_error(str(e))
    
    def _stop_recording(self):
        """Stop speech recording"""
        if self.worker:
            self.worker.stop_listening()
            self.worker = None
    
    def _on_text_recognized(self, text: str):
        """Handle recognized text"""
        self.text_recognized.emit(text)
        logger.info(f"Speech recognized: {text}")
    
    def _on_error(self, error: str):
        """Handle recognition error"""
        logger.error(f"Speech recognition error: {error}")
        self._stop_recording()
        self.setChecked(False)
        self.is_recording = False
    
    def _on_listening_started(self):
        """Handle listening started"""
        self.is_recording = True
        self.setChecked(True)
        self.setText("🔴")
        self.setToolTip("Recording... Click to stop")
    
    def _on_listening_stopped(self):
        """Handle listening stopped"""
        self.is_recording = False
        self.setChecked(False)
        self.setText("🎤")
        self.setToolTip("Click and hold to record speech")


class STTWidget(QWidget):
    """Complete speech-to-text widget with status"""
    
    text_recognized = pyqtSignal(str)
    
    def __init__(self, config: STTConfig = None, parent: QWidget = None):
        super().__init__(parent)
        
        self.config = config or STTConfig()
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the widget UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # STT button
        self.stt_button = STTButton(self.config)
        self.stt_button.text_recognized.connect(self.text_recognized.emit)
        layout.addWidget(self.stt_button)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #666; font-size: 10px;")
        layout.addWidget(self.status_label)
        
        # Connect signals for status updates
        if self.stt_button.worker:
            self.stt_button.worker.listening_started.connect(
                lambda: self.status_label.setText("Listening...")
            )
            self.stt_button.worker.listening_stopped.connect(
                lambda: self.status_label.setText("Ready")
            )
            self.stt_button.worker.error_occurred.connect(
                lambda error: self.status_label.setText(f"Error: {error}")
            )


class STTManager:
    """Global speech-to-text manager"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = STTConfig(
            language=config.get('stt', {}).get('language', 'en-US'),
            timeout=config.get('stt', {}).get('timeout', 1.0),
            energy_threshold=config.get('stt', {}).get('energy_threshold', 300)
        )
        
        self.is_available = SPEECH_RECOGNITION_AVAILABLE
        
        if not self.is_available:
            logger.warning("Speech recognition not available - install speech_recognition and pyaudio")
        else:
            logger.info("STT manager initialized")
    
    def create_stt_button(self, parent: QWidget = None) -> STTButton:
        """Create a new STT button"""
        return STTButton(self.config, parent)
    
    def create_stt_widget(self, parent: QWidget = None) -> STTWidget:
        """Create a new STT widget"""
        return STTWidget(self.config, parent)
    
    def test_microphone(self) -> bool:
        """Test if microphone is available"""
        if not self.is_available:
            return False
        
        try:
            recognizer = sr.Recognizer()
            microphone = sr.Microphone()
            
            with microphone as source:
                recognizer.adjust_for_ambient_noise(source, duration=0.5)
            
            return True
        except Exception as e:
            logger.error(f"Microphone test failed: {e}")
            return False
