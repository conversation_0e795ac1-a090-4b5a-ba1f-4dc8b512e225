"""
Google Drive Authentication and API Management
Handles OAuth 2.0 authentication and credential persistence for Google Drive
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import threading
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

logger = logging.getLogger(__name__)

class GoogleDriveCallbackHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Google Drive OAuth callback"""
    
    def do_GET(self):
        """Handle GET request for OAuth callback"""
        try:
            # Parse the callback URL
            parsed_url = urlparse(self.path)
            query_params = parse_qs(parsed_url.query)
            
            # Extract authorization code and state
            auth_code = query_params.get('code', [None])[0]
            state = query_params.get('state', [None])[0]
            error = query_params.get('error', [None])[0]
            
            if error:
                # OAuth error
                self.send_response(400)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                
                error_html = f"""
                <html>
                <head><title>Google Drive Authentication Error</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #f44336;">❌ Google Drive Authentication Failed</h1>
                    <p>Error: {error}</p>
                    <p>Please close this window and try again.</p>
                </body>
                </html>
                """
                self.wfile.write(error_html.encode())
                
                # Store error for the main thread
                self.server.auth_result = {'error': error}
                
            elif auth_code:
                # Successful callback
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                
                success_html = """
                <html>
                <head><title>Google Drive Authentication Successful</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #4CAF50;">✅ Google Drive Authentication Successful!</h1>
                    <p>You have successfully authenticated with Google Drive.</p>
                    <p>You can now close this window and return to the application.</p>
                    <script>
                        setTimeout(function() {
                            window.close();
                        }, 3000);
                    </script>
                </body>
                </html>
                """
                self.wfile.write(success_html.encode())
                
                # Store auth data for the main thread
                self.server.auth_result = {
                    'code': auth_code,
                    'state': state
                }
                
            else:
                # Missing parameters
                self.send_response(400)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                
                error_html = """
                <html>
                <head><title>Google Drive Authentication Error</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #f44336;">❌ Authentication Error</h1>
                    <p>Missing required parameters in callback URL.</p>
                    <p>Please close this window and try again.</p>
                </body>
                </html>
                """
                self.wfile.write(error_html.encode())
                
                self.server.auth_result = {'error': 'Missing parameters'}
                
        except Exception as e:
            logger.error(f"Error in Google Drive OAuth callback handler: {e}")
            self.send_response(500)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            error_html = f"""
            <html>
            <head><title>Server Error</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1 style="color: #f44336;">❌ Server Error</h1>
                <p>An error occurred while processing the authentication callback.</p>
                <p>Error: {str(e)}</p>
                <p>Please close this window and try again.</p>
            </body>
            </html>
            """
            self.wfile.write(error_html.encode())
            
            self.server.auth_result = {'error': str(e)}
    
    def log_message(self, format, *args):
        """Override to suppress default logging"""
        pass


class GoogleDriveCallbackServer:
    """OAuth callback server for Google Drive authentication"""
    
    def __init__(self, port=8081):
        self.port = port
        self.server = None
        self.server_thread = None
        self.auth_result = None
    
    def start(self):
        """Start the callback server"""
        try:
            # Find available port
            for port in range(self.port, self.port + 10):
                try:
                    self.server = HTTPServer(('localhost', port), GoogleDriveCallbackHandler)
                    self.server.auth_result = None
                    self.port = port
                    break
                except OSError:
                    continue
            
            if not self.server:
                raise Exception("Could not find available port for Google Drive OAuth callback server")
            
            # Start server in background thread
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            
            logger.info(f"Google Drive OAuth callback server started on port {self.port}")
            return f"http://localhost:{self.port}/callback"
            
        except Exception as e:
            logger.error(f"Failed to start Google Drive OAuth callback server: {e}")
            raise
    
    def _run_server(self):
        """Run the server (called in background thread)"""
        try:
            self.server.serve_forever()
        except Exception as e:
            logger.error(f"Google Drive OAuth callback server error: {e}")
    
    def wait_for_callback(self, timeout=300):
        """Wait for OAuth callback with timeout"""
        import time
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.server and hasattr(self.server, 'auth_result') and self.server.auth_result:
                result = self.server.auth_result
                self.stop()
                return result
            time.sleep(0.5)
        
        self.stop()
        return {'error': 'Timeout waiting for authentication'}
    
    def stop(self):
        """Stop the callback server"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.server = None
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=1)
            self.server_thread = None
        
        logger.info("Google Drive OAuth callback server stopped")


class GoogleDriveAuthenticator:
    """Google Drive OAuth 2.0 authenticator with credential persistence"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.credentials_file = Path("data/credentials/google_drive_credentials.json")
        self.credentials_file.parent.mkdir(parents=True, exist_ok=True)
        
        # OAuth 2.0 configuration
        self.client_id = config.get('google_drive', {}).get('client_id', '')
        self.client_secret = config.get('google_drive', {}).get('client_secret', '')
        self.redirect_uri = config.get('google_drive', {}).get('redirect_uri', 'http://localhost:8081/callback')
        
        # Scopes for Google Drive access
        self.scopes = [
            'https://www.googleapis.com/auth/drive.file',  # Create and edit files
            'https://www.googleapis.com/auth/drive.appdata',  # App-specific folder
            'https://www.googleapis.com/auth/userinfo.profile'  # User profile info
        ]
        
        self.access_token = None
        self.refresh_token = None
        self.user_info = None
        
        # Load saved credentials
        self._load_credentials()
    
    def _load_credentials(self):
        """Load saved credentials from file"""
        try:
            if self.credentials_file.exists():
                with open(self.credentials_file, 'r') as f:
                    creds = json.load(f)
                    self.access_token = creds.get('access_token')
                    self.refresh_token = creds.get('refresh_token')
                    self.user_info = creds.get('user_info')
                    logger.info("Loaded saved Google Drive credentials")
        except Exception as e:
            logger.warning(f"Could not load Google Drive credentials: {e}")
    
    def _save_credentials(self):
        """Save credentials to file"""
        try:
            creds = {
                'access_token': self.access_token,
                'refresh_token': self.refresh_token,
                'user_info': self.user_info
            }
            with open(self.credentials_file, 'w') as f:
                json.dump(creds, f, indent=2)
            logger.info("Saved Google Drive credentials")
        except Exception as e:
            logger.error(f"Could not save Google Drive credentials: {e}")
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return bool(self.access_token)
    
    def get_auth_url(self) -> tuple[str, str]:
        """Generate OAuth 2.0 authorization URL"""
        import secrets
        import urllib.parse
        
        # Generate state parameter for security
        state = secrets.token_urlsafe(32)
        
        # Build authorization URL
        params = {
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': ' '.join(self.scopes),
            'response_type': 'code',
            'state': state,
            'access_type': 'offline',  # Get refresh token
            'prompt': 'consent'  # Force consent screen to get refresh token
        }
        
        auth_url = 'https://accounts.google.com/o/oauth2/v2/auth?' + urllib.parse.urlencode(params)
        return auth_url, state
    
    def authenticate_with_code(self, code: str, state: str) -> bool:
        """Complete authentication with authorization code"""
        try:
            # Exchange code for tokens (mock implementation)
            # In real implementation, you'd make HTTP request to Google's token endpoint
            logger.info("Exchanging authorization code for tokens...")
            
            # Mock successful authentication
            self.access_token = f"mock_access_token_{code[:10]}"
            self.refresh_token = f"mock_refresh_token_{code[:10]}"
            self.user_info = {
                'id': 'mock_user_id',
                'name': 'Mock User',
                'email': '<EMAIL>',
                'picture': ''
            }
            
            # Save credentials
            self._save_credentials()
            
            logger.info("Google Drive authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Google Drive authentication failed: {e}")
            return False
    
    def logout(self):
        """Logout and clear credentials"""
        self.access_token = None
        self.refresh_token = None
        self.user_info = None
        
        # Remove credentials file
        try:
            if self.credentials_file.exists():
                self.credentials_file.unlink()
            logger.info("Google Drive logout successful")
        except Exception as e:
            logger.warning(f"Could not remove credentials file: {e}")
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user information"""
        return self.user_info
    
    def refresh_access_token(self) -> bool:
        """Refresh access token using refresh token"""
        try:
            if not self.refresh_token:
                return False
            
            # Mock token refresh
            logger.info("Refreshing Google Drive access token...")
            self.access_token = f"refreshed_{self.access_token}"
            self._save_credentials()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to refresh Google Drive access token: {e}")
            return False
