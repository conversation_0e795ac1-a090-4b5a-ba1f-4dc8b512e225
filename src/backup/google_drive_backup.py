"""
Google Drive Backup System
Provides selective backup of project components to Google Drive
"""

import logging
import json
import os
import io
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime
import zipfile
import hashlib

try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaIoBaseDownload, MediaFileUpload
    import pickle
    GOOGLE_API_AVAILABLE = True
except ImportError:
    GOOGLE_API_AVAILABLE = False

from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, 
    QPushButton, QProgressBar, QGroupBox, QListWidget, 
    QListWidgetItem, QMessageBox, QFileDialog
)

logger = logging.getLogger(__name__)


@dataclass
class BackupItem:
    """Represents an item that can be backed up"""
    id: str
    name: str
    description: str
    path: str
    category: str
    size_mb: float = 0.0
    enabled: bool = True
    last_backup: Optional[str] = None


@dataclass
class BackupConfig:
    """Backup configuration"""
    project_name: str
    backup_items: List[str]  # Item IDs to backup
    compression: bool = True
    incremental: bool = True
    folder_structure: str = "organized"  # "organized" or "flat"
    max_versions: int = 5


class GoogleDriveClient:
    """Google Drive API client"""
    
    SCOPES = ['https://www.googleapis.com/auth/drive.file']
    
    def __init__(self, credentials_path: str = "credentials.json"):
        self.credentials_path = credentials_path
        self.token_path = "data/drive_token.pickle"
        self.service = None
        self.is_authenticated = False
        
        if GOOGLE_API_AVAILABLE:
            self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Drive"""
        creds = None
        
        # Load existing token
        if os.path.exists(self.token_path):
            try:
                with open(self.token_path, 'rb') as token:
                    creds = pickle.load(token)
            except Exception as e:
                logger.error(f"Error loading token: {e}")
        
        # If no valid credentials, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                except Exception as e:
                    logger.error(f"Error refreshing token: {e}")
                    creds = None
            
            if not creds:
                if not os.path.exists(self.credentials_path):
                    logger.error(f"Credentials file not found: {self.credentials_path}")
                    return
                
                try:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_path, self.SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                except Exception as e:
                    logger.error(f"Authentication failed: {e}")
                    return
            
            # Save credentials
            try:
                os.makedirs(os.path.dirname(self.token_path), exist_ok=True)
                with open(self.token_path, 'wb') as token:
                    pickle.dump(creds, token)
            except Exception as e:
                logger.error(f"Error saving token: {e}")
        
        try:
            self.service = build('drive', 'v3', credentials=creds)
            self.is_authenticated = True
            logger.info("Google Drive authentication successful")
        except Exception as e:
            logger.error(f"Error building Drive service: {e}")
    
    def create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Create a folder in Google Drive"""
        if not self.service:
            return None
        
        try:
            folder_metadata = {
                'name': name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_id:
                folder_metadata['parents'] = [parent_id]
            
            folder = self.service.files().create(body=folder_metadata).execute()
            return folder.get('id')
            
        except Exception as e:
            logger.error(f"Error creating folder: {e}")
            return None
    
    def upload_file(self, file_path: str, name: str = None, parent_id: str = None) -> Optional[str]:
        """Upload a file to Google Drive"""
        if not self.service:
            return None
        
        try:
            file_name = name or os.path.basename(file_path)
            
            file_metadata = {'name': file_name}
            if parent_id:
                file_metadata['parents'] = [parent_id]
            
            media = MediaFileUpload(file_path, resumable=True)
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media
            ).execute()
            
            return file.get('id')
            
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return None
    
    def find_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Find a folder by name"""
        if not self.service:
            return None
        
        try:
            query = f"name='{name}' and mimeType='application/vnd.google-apps.folder'"
            if parent_id:
                query += f" and '{parent_id}' in parents"
            
            results = self.service.files().list(q=query).execute()
            items = results.get('files', [])
            
            return items[0]['id'] if items else None
            
        except Exception as e:
            logger.error(f"Error finding folder: {e}")
            return None
    
    def list_files(self, parent_id: str = None) -> List[Dict[str, Any]]:
        """List files in a folder"""
        if not self.service:
            return []
        
        try:
            query = "trashed=false"
            if parent_id:
                query += f" and '{parent_id}' in parents"
            
            results = self.service.files().list(q=query).execute()
            return results.get('files', [])
            
        except Exception as e:
            logger.error(f"Error listing files: {e}")
            return []


class BackupWorker(QThread):
    """Worker thread for backup operations"""
    
    progress_updated = pyqtSignal(int, str)
    backup_completed = pyqtSignal(dict)
    backup_failed = pyqtSignal(str)
    
    def __init__(self, backup_manager, config: BackupConfig, items: List[BackupItem]):
        super().__init__()
        self.backup_manager = backup_manager
        self.config = config
        self.items = items
    
    def run(self):
        """Run backup process"""
        try:
            self.progress_updated.emit(0, "Initializing backup...")
            
            # Create backup folder structure
            project_folder_id = self._ensure_project_folder()
            if not project_folder_id:
                self.backup_failed.emit("Failed to create project folder")
                return
            
            # Create timestamped backup folder
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_folder_name = f"backup_{timestamp}"
            backup_folder_id = self.backup_manager.drive_client.create_folder(
                backup_folder_name, project_folder_id
            )
            
            if not backup_folder_id:
                self.backup_failed.emit("Failed to create backup folder")
                return
            
            # Backup each item
            total_items = len(self.items)
            results = {"uploaded": [], "failed": [], "skipped": []}
            
            for i, item in enumerate(self.items):
                progress = int((i / total_items) * 90)
                self.progress_updated.emit(progress, f"Backing up {item.name}...")
                
                try:
                    if self._backup_item(item, backup_folder_id):
                        results["uploaded"].append(item.name)
                    else:
                        results["failed"].append(item.name)
                except Exception as e:
                    logger.error(f"Error backing up {item.name}: {e}")
                    results["failed"].append(item.name)
            
            self.progress_updated.emit(100, "Backup completed")
            self.backup_completed.emit(results)
            
        except Exception as e:
            self.backup_failed.emit(str(e))
    
    def _ensure_project_folder(self) -> Optional[str]:
        """Ensure project folder exists"""
        # Find or create CYOA Backups folder
        cyoa_folder_id = self.backup_manager.drive_client.find_folder("CYOA Backups")
        if not cyoa_folder_id:
            cyoa_folder_id = self.backup_manager.drive_client.create_folder("CYOA Backups")
        
        if not cyoa_folder_id:
            return None
        
        # Find or create project folder
        project_folder_id = self.backup_manager.drive_client.find_folder(
            self.config.project_name, cyoa_folder_id
        )
        if not project_folder_id:
            project_folder_id = self.backup_manager.drive_client.create_folder(
                self.config.project_name, cyoa_folder_id
            )
        
        return project_folder_id
    
    def _backup_item(self, item: BackupItem, parent_folder_id: str) -> bool:
        """Backup a single item"""
        item_path = Path(item.path)
        
        if not item_path.exists():
            logger.warning(f"Backup item not found: {item.path}")
            return False
        
        if item_path.is_file():
            # Upload single file
            file_id = self.backup_manager.drive_client.upload_file(
                str(item_path), item_path.name, parent_folder_id
            )
            return file_id is not None
        
        elif item_path.is_dir():
            # Create zip archive for directory
            zip_path = self._create_zip_archive(item_path, item.name)
            if zip_path:
                file_id = self.backup_manager.drive_client.upload_file(
                    zip_path, f"{item.name}.zip", parent_folder_id
                )
                os.remove(zip_path)  # Clean up temp file
                return file_id is not None
        
        return False
    
    def _create_zip_archive(self, source_path: Path, archive_name: str) -> Optional[str]:
        """Create a zip archive of a directory"""
        try:
            temp_dir = Path("temp")
            temp_dir.mkdir(exist_ok=True)
            
            zip_path = temp_dir / f"{archive_name}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in source_path.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(source_path)
                        zipf.write(file_path, arcname)
            
            return str(zip_path)
            
        except Exception as e:
            logger.error(f"Error creating zip archive: {e}")
            return None


class BackupManager:
    """Main backup manager"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.drive_client = GoogleDriveClient()
        self.backup_items = self._discover_backup_items()
        
        logger.info(f"Backup manager initialized with {len(self.backup_items)} items")
    
    def _discover_backup_items(self) -> List[BackupItem]:
        """Discover available backup items"""
        items = []
        
        # Story data
        if Path("data/storylines").exists():
            items.append(BackupItem(
                id="storylines",
                name="Story Lines",
                description="All saved story line files",
                path="data/storylines",
                category="stories",
                size_mb=self._get_folder_size("data/storylines")
            ))
        
        # Character data
        if Path("data/characters").exists():
            items.append(BackupItem(
                id="characters",
                name="Character Data",
                description="Character profiles and voice samples",
                path="data/characters",
                category="characters",
                size_mb=self._get_folder_size("data/characters")
            ))
        
        # Voice profiles
        if Path("data/voice_profiles.json").exists():
            items.append(BackupItem(
                id="voice_profiles",
                name="Voice Profiles",
                description="Text-to-speech voice configurations",
                path="data/voice_profiles.json",
                category="settings",
                size_mb=self._get_file_size("data/voice_profiles.json")
            ))
        
        # Configuration
        if Path("config.json").exists():
            items.append(BackupItem(
                id="config",
                name="App Configuration",
                description="Application settings and preferences",
                path="config.json",
                category="settings",
                size_mb=self._get_file_size("config.json")
            ))
        
        # Analytics data
        if Path("data/analytics").exists():
            items.append(BackupItem(
                id="analytics",
                name="Analytics Data",
                description="Story performance and engagement data",
                path="data/analytics",
                category="analytics",
                size_mb=self._get_folder_size("data/analytics")
            ))
        
        # Videos (optional - can be large)
        if Path("videos").exists():
            items.append(BackupItem(
                id="videos",
                name="Generated Videos",
                description="All generated video files (large)",
                path="videos",
                category="media",
                size_mb=self._get_folder_size("videos"),
                enabled=False  # Disabled by default due to size
            ))
        
        return items
    
    def _get_folder_size(self, path: str) -> float:
        """Get folder size in MB"""
        try:
            total_size = sum(
                f.stat().st_size for f in Path(path).rglob('*') if f.is_file()
            )
            return total_size / (1024 * 1024)
        except Exception:
            return 0.0
    
    def _get_file_size(self, path: str) -> float:
        """Get file size in MB"""
        try:
            return Path(path).stat().st_size / (1024 * 1024)
        except Exception:
            return 0.0
    
    def create_backup(self, config: BackupConfig) -> QThread:
        """Create a backup with given configuration"""
        selected_items = [
            item for item in self.backup_items 
            if item.id in config.backup_items
        ]
        
        worker = BackupWorker(self, config, selected_items)
        return worker
    
    def is_available(self) -> bool:
        """Check if Google Drive backup is available"""
        return GOOGLE_API_AVAILABLE and self.drive_client.is_authenticated
