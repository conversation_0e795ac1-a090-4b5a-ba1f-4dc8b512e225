#!/usr/bin/env python3
"""
Minimal test to verify LM Studio integration
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    print("🧪 Minimal LM Studio Integration Test")
    print("=" * 40)
    
    # Test 1: Import LMStudioClient
    print("1. Testing LMStudioClient import...")
    try:
        from utils.lmstudio_client import LMStudioClient, LMStudioResponse
        print("   ✅ LMStudioClient imported successfully")
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return 1
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return 1
    
    # Test 2: Create client instance
    print("2. Testing client instantiation...")
    try:
        config = {
            "lmstudio": {
                "base_url": "http://localhost:1234",
                "default_model": "local-model",
                "timeout": 120
            }
        }
        client = LMStudioClient(config)
        print("   ✅ Client created successfully")
    except Exception as e:
        print(f"   ❌ Client creation failed: {e}")
        return 1
    
    # Test 3: Test availability check (should work even if LM Studio isn't running)
    print("3. Testing availability check...")
    try:
        is_available = client.is_available()
        if is_available:
            print("   ✅ LM Studio is running and available")
        else:
            print("   ⚠️  LM Studio not running (this is OK for testing)")
        print("   ✅ Availability check method works")
    except Exception as e:
        print(f"   ❌ Availability check failed: {e}")
        return 1
    
    # Test 4: Test response object
    print("4. Testing response object...")
    try:
        response = LMStudioResponse(success=True, text="Test response")
        assert response.success == True
        assert response.text == "Test response"
        print("   ✅ Response object works correctly")
    except Exception as e:
        print(f"   ❌ Response object test failed: {e}")
        return 1
    
    print("\n🎉 All tests passed!")
    print("\nLM Studio integration is properly configured.")
    print("\nTo test with actual LM Studio:")
    print("1. Start LM Studio")
    print("2. Load a model")
    print("3. Start the local server")
    print("4. Run: python test_lmstudio_integration.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
