{"workflow_name": "CYOA Video Generation", "description": "Generate videos for CYOA story nodes with lip-sync audio", "version": "1.0", "nodes": {"1": {"class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "stable-diffusion-v1-5.safetensors"}}, "2": {"class_type": "CLIPTextEncode", "inputs": {"text": "PROMPT_PLACEHOLDER", "clip": ["1", 1]}}, "3": {"class_type": "CLIPTextEncode", "inputs": {"text": "blurry, low quality, distorted", "clip": ["1", 1]}}, "4": {"class_type": "EmptyLatentImage", "inputs": {"width": 512, "height": 512, "batch_size": 1}}, "5": {"class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["1", 0], "positive": ["2", 0], "negative": ["3", 0], "latent_image": ["4", 0]}}, "6": {"class_type": "VAEDecode", "inputs": {"samples": ["5", 0], "vae": ["1", 2]}}, "7": {"class_type": "SaveImage", "inputs": {"filename_prefix": "cyoa_frame", "images": ["6", 0]}}}, "parameters": {"prompt": {"type": "string", "description": "Visual description for the scene", "placeholder": "PROMPT_PLACEHOLDER"}, "width": {"type": "integer", "default": 512, "min": 256, "max": 1024}, "height": {"type": "integer", "default": 512, "min": 256, "max": 1024}, "steps": {"type": "integer", "default": 20, "min": 10, "max": 50}, "cfg": {"type": "float", "default": 7.0, "min": 1.0, "max": 20.0}}}