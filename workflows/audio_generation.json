{"workflow_name": "CYOA Audio Generation", "description": "Generate TTS audio for CYOA story nodes", "version": "1.0", "nodes": {"1": {"class_type": "TextToSpeech", "inputs": {"text": "TEXT_PLACEHOLDER", "voice": "default", "speed": 1.0, "pitch": 1.0}}, "2": {"class_type": "AudioProcessor", "inputs": {"audio": ["1", 0], "normalize": true, "noise_reduction": true}}, "3": {"class_type": "SaveAudio", "inputs": {"filename_prefix": "cyoa_audio", "format": "wav", "audio": ["2", 0]}}}, "parameters": {"text": {"type": "string", "description": "Text to convert to speech", "placeholder": "TEXT_PLACEHOLDER"}, "voice": {"type": "string", "default": "default", "options": ["default", "male", "female", "narrator"]}, "speed": {"type": "float", "default": 1.0, "min": 0.5, "max": 2.0}, "pitch": {"type": "float", "default": 1.0, "min": 0.5, "max": 2.0}}}