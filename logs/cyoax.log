2025-05-28 07:20:34 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:21:10 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:21:24 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:29:28 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:32:04 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:32:15 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:32:16 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:32:16 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:32:16 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:32:16 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:32:16 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:32:16 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:32:16 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:32:16 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:32:16 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:32:16 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:32:16 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:32:16 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:32:16 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:32:16 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:32:16 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:32:16 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:32:16 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'Horizontal'
2025-05-28 07:33:50 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:33:50 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:33:50 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:33:50 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:33:50 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:33:50 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:33:50 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:33:50 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:33:50 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:33:50 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:33:50 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:33:50 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:33:50 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:33:50 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:33:50 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:33:50 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:33:50 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:33:51 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:35:43 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:35:44 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:35:44 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:35:44 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:35:44 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:35:44 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:35:44 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:35:44 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:35:44 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:35:44 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:35:44 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:35:44 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:35:44 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:35:44 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:35:44 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:35:44 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:35:44 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:35:44 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:37:32 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:37:32 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:37:32 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:37:32 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:37:32 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:37:32 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:37:32 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:37:32 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:37:32 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:37:32 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:37:32 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:37:32 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:37:32 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:37:32 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:37:32 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:37:32 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:37:32 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:37:33 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:38:51 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:38:51 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:38:51 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:38:51 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:38:51 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:38:51 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:38:51 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:38:51 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:38:51 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:38:51 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:38:51 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:38:51 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:38:51 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:38:51 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:38:51 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:38:51 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:38:51 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:38:52 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:40:06 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:40:07 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:40:07 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:40:07 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:40:07 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:40:07 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:40:07 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:40:07 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:40:07 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:40:07 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:40:07 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:40:07 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:40:07 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:40:07 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:40:07 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:40:07 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:40:07 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:40:07 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:40:07 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QGraphicsView' has no attribute 'RubberBandDrag'
2025-05-28 07:41:33 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:41:33 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:41:33 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:41:33 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:41:33 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:41:33 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:41:33 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:41:33 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:41:33 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:41:33 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:41:33 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:41:33 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:41:34 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:41:34 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:41:34 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:41:34 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:41:34 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:41:34 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:41:34 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:41:34 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:41:34 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFrame' has no attribute 'StyledPanel'
2025-05-28 07:44:16 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:44:16 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:44:16 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:44:16 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:44:16 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:44:16 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:44:16 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:44:16 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:44:16 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:44:16 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:44:16 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:44:16 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:44:16 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:44:16 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:44:16 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:44:16 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:44:16 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:44:17 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:44:17 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:44:17 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:44:17 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'AlignCenter'
2025-05-28 07:47:39 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:47:39 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:47:39 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:47:39 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:47:39 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:47:39 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:47:39 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:47:39 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:47:39 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:47:39 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:47:39 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:47:39 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:47:39 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:47:39 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:47:39 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:47:39 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:47:39 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:47:40 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:47:40 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:47:40 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:47:40 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'PointingHandCursor'
2025-05-28 07:48:53 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:48:53 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:48:53 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:48:53 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:48:53 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:48:53 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:48:53 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:48:53 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:48:53 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:48:53 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:48:53 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:48:53 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:48:53 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:48:53 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:48:53 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:48:53 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:48:53 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:48:54 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:48:54 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:48:54 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:48:54 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QLineEdit' has no attribute 'Password'
2025-05-28 07:50:05 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:50:06 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:50:06 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:50:06 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:50:06 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:50:06 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:50:06 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:50:06 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:50:06 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:50:06 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:50:06 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:50:06 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:50:06 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:50:06 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:50:06 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:50:06 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:50:06 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:50:06 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:50:06 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:50:06 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:50:06 | gui.main_window      | INFO     | __init__       :61   | Main window initialized
2025-05-28 07:50:06 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 07:50:06 | main.app             | ERROR    | run_gui        :267  | Error running GUI: 'QApplication' object has no attribute 'exec_'
2025-05-28 07:56:55 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:56:56 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:56:56 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 07:56:56 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 07:56:56 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 07:56:56 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 07:56:56 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 07:56:56 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 07:56:56 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 07:56:56 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 07:56:56 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 07:56:56 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 07:56:56 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 07:56:56 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 07:56:56 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 07:56:56 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 07:56:56 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 07:56:56 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 07:56:56 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 07:56:56 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 07:56:57 | gui.main_window      | INFO     | __init__       :61   | Main window initialized
2025-05-28 07:56:57 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 07:57:11 | gui.main_window      | INFO     | closeEvent     :678  | Main window closed
2025-05-28 08:04:51 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:04:52 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:04:52 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:04:52 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:04:52 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:04:52 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:04:52 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:04:52 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:04:52 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:04:52 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:04:52 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:04:52 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:04:52 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:04:52 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:04:52 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:04:52 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:04:52 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:04:53 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:04:53 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 08:04:53 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:04:53 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:04:53 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:04:53 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:04:53 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:04:53 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:04:53 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:04:53 | audio.text_to_speech | INFO     | save_profiles  :248  | Voice profiles saved
2025-05-28 08:04:53 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:04:58 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:04:58 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:04:58 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:04:58 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:04:58 | gui.chat_tab         | INFO     | __init__       :87   | Chat tab initialized
2025-05-28 08:04:58 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:04:58 | gui.main_window      | INFO     | __init__       :63   | Main window initialized
2025-05-28 08:04:58 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:05:22 | gui.main_window      | INFO     | closeEvent     :726  | Main window closed
2025-05-28 08:11:36 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:11:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:11:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:11:36 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:11:36 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:11:36 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:11:36 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:11:36 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:11:36 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:11:36 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:11:36 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:11:36 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:11:36 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:11:36 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:11:36 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:11:36 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:11:36 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:11:37 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:11:37 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 08:11:37 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:11:37 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:11:37 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:11:37 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:11:37 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:11:37 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:11:37 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:11:37 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:11:37 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:11:39 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:11:39 | gui.chat_assistant   | INFO     | __init__       :85   | Compact chat widget initialized
2025-05-28 08:11:39 | gui.chat_assistant   | INFO     | __init__       :515  | Chat assistant sidebar initialized
2025-05-28 08:11:39 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:11:39 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:11:39 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:11:39 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:11:39 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:11:39 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:11:39 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:11:39 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:11:39 | gui.chat_assistant   | INFO     | __init__       :85   | Compact chat widget initialized
2025-05-28 08:11:39 | gui.chat_assistant   | INFO     | __init__       :629  | Chat assistant floating window initialized
2025-05-28 08:11:39 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:11:39 | gui.main_window      | INFO     | __init__       :63   | Main window initialized
2025-05-28 08:11:39 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:15:18 | gui.main_window      | INFO     | closeEvent     :806  | Main window closed
2025-05-28 08:22:56 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:22:56 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:22:56 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:22:56 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:22:56 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:22:56 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:22:56 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:22:56 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:22:56 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:22:56 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:22:56 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:22:56 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:22:57 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:22:57 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:22:57 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:22:57 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:22:57 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:22:57 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:22:57 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 08:22:57 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:22:57 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:22:57 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:22:57 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:22:57 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:22:57 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:22:57 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:22:57 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:22:57 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:22:59 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:22:59 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:22:59 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 08:22:59 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:22:59 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:22:59 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:22:59 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:22:59 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:22:59 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:22:59 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:22:59 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:22:59 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:22:59 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 08:22:59 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:22:59 | gui.main_window      | INFO     | __init__       :63   | Main window initialized
2025-05-28 08:23:00 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:24:35 | gui.main_window      | ERROR    | show_story_wizard:380  | Error showing story wizard: type object 'QWizard' has no attribute 'ModernStyle'
2025-05-28 08:41:28 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:41:29 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:41:29 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:41:29 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:41:29 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:41:29 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:41:29 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:41:29 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:41:29 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:41:29 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:41:29 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:41:29 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:41:29 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:41:29 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:41:29 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:41:29 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:41:29 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:41:30 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:41:30 | gui.graph_viewer     | INFO     | __init__       :36   | Graph viewer tab initialized
2025-05-28 08:41:30 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:41:30 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:41:30 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:41:30 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:41:30 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:41:30 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:41:30 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:41:30 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:41:30 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:41:33 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:41:33 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:41:33 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 08:41:33 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:41:33 | ai.rag_system        | INFO     | _load_app_knowledge:129  | Loaded 10 app knowledge documents
2025-05-28 08:41:33 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:41:33 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:41:33 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:41:33 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:41:33 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:41:33 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:41:33 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:41:33 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 08:41:33 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:41:33 | gui.main_window      | INFO     | __init__       :64   | Main window initialized
2025-05-28 08:41:33 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:42:22 | gui.main_window      | ERROR    | show_story_wizard:384  | Error showing story wizard: type object 'QWizard' has no attribute 'HaveHelpButton'
2025-05-28 08:48:33 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:48:33 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:48:33 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:48:33 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:48:33 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:48:33 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:48:33 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:48:33 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:48:33 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:48:33 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:48:33 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:48:33 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:48:33 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:48:33 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:48:33 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:48:33 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:48:33 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:48:34 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:48:34 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 08:48:34 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:48:34 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:48:34 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:48:34 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:48:34 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:48:34 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:48:34 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:48:34 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:48:34 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:48:36 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:48:36 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:48:36 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 08:48:36 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:48:36 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:48:36 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:48:36 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:48:36 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:48:36 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:48:36 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:48:36 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:48:36 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:48:36 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 08:48:36 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:48:37 | gui.main_window      | INFO     | __init__       :64   | Main window initialized
2025-05-28 08:48:37 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:48:48 | gui.main_window      | ERROR    | show_story_wizard:384  | Error showing story wizard: registerField(self, name: Optional[str], widget: Optional[QWidget], property: Optional[str] = None, changedSignal: PYQT_SIGNAL = None): argument 2 has unexpected type 'QButtonGroup'
2025-05-28 08:49:48 | story.character_randomizer | INFO     | __init__       :112  | Character randomizer initialized
2025-05-28 08:49:48 | story.item_system    | INFO     | _load_default_items:223  | Loaded 10 default items
2025-05-28 08:53:04 | social.x_auth        | INFO     | get_auth_url   :145  | Generated X OAuth authorization URL
2025-05-28 08:53:05 | gui.auth_tab         | ERROR    | _start_login   :461  | Error starting login: type object 'QDialogButtonBox' has no attribute 'Ok'
2025-05-28 08:56:07 | gui.main_window      | INFO     | closeEvent     :812  | Main window closed
2025-05-28 08:57:41 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:57:41 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:57:41 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:57:41 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:57:41 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:57:41 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:57:41 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:57:41 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:57:41 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:57:41 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:57:41 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:57:41 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:57:41 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:57:41 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:57:41 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:57:41 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:57:41 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:57:42 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:57:42 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 08:57:42 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:57:42 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:57:42 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:57:42 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:57:42 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:57:42 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:57:42 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:57:42 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:57:42 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:57:45 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:57:45 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:57:45 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 08:57:45 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:57:45 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:57:45 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:57:45 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:57:45 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:57:45 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:57:45 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:57:45 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:57:45 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:57:45 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 08:57:45 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:57:45 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 08:57:45 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:58:23 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 08:58:29 | gui.main_window      | ERROR    | new_story      :471  | Error creating new story: StoryNode.__init__() got an unexpected keyword argument 'title'
2025-05-28 08:59:24 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:59:25 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:59:25 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 08:59:25 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 08:59:25 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 08:59:25 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 08:59:25 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 08:59:25 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 08:59:25 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 08:59:25 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 08:59:25 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 08:59:25 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 08:59:26 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 08:59:26 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 08:59:26 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 08:59:26 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 08:59:26 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 08:59:27 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 08:59:27 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 08:59:27 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 08:59:27 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:59:27 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:59:27 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:59:27 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:59:27 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:59:27 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:59:27 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:59:27 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:59:30 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:59:30 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:59:30 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:59:30 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:59:30 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:59:30 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 08:59:30 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 08:59:31 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 08:59:31 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 08:59:31 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 08:59:31 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 08:59:31 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 08:59:31 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 08:59:31 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 08:59:31 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 08:59:31 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 08:59:31 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 08:59:31 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 08:59:31 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 08:59:31 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 08:59:31 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 08:59:31 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 08:59:31 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 08:59:39 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 08:59:44 | gui.main_window      | ERROR    | new_story      :471  | Error creating new story: StoryNode.__init__() got an unexpected keyword argument 'title'
2025-05-28 09:00:14 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:15 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:15 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:15 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:00:15 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:00:15 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:00:15 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:00:15 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:00:15 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:00:15 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:00:15 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:00:15 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:00:15 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:00:15 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:00:15 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:00:15 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:00:15 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:00:16 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 09:00:16 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:00:16 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:00:16 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:00:16 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:00:16 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:00:16 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:00:16 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:00:16 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:00:16 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:00:16 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:00:19 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:00:19 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:00:19 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:00:19 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:00:19 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:00:19 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:00:19 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:00:19 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:00:19 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:00:19 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:00:19 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:00:19 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:00:19 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:00:19 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:00:19 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:00:19 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:00:30 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 09:00:34 | story.character_randomizer | INFO     | __init__       :112  | Character randomizer initialized
2025-05-28 09:00:34 | story.item_system    | INFO     | _load_default_items:223  | Loaded 10 default items
2025-05-28 09:00:54 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:55 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:55 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:00:55 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:00:55 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:00:55 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:00:55 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:00:55 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:00:55 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:00:55 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:00:55 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:00:55 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:00:55 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:00:55 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:00:55 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:00:55 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:00:55 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:00:55 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 09:00:55 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:00:56 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:00:56 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:00:56 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:00:56 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:00:56 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:00:56 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:00:56 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:00:56 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:00:56 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:00:59 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:00:59 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:00:59 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:00:59 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:00:59 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:01:00 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:01:00 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:01:00 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:01:00 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:01:00 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:01:00 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:01:00 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:01:00 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:01:00 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:01:00 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:01:00 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:01:00 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:01:00 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:01:00 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:01:00 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:01:00 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:01:00 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:01:00 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:01:41 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:01:41 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:01:41 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:01:41 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:01:41 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:01:41 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:01:41 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:01:41 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:01:41 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:01:41 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:01:41 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:01:41 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:01:41 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:01:41 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:01:41 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:01:41 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:01:41 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:01:42 | gui.story_editor     | INFO     | __init__       :199  | Story editor tab initialized
2025-05-28 09:01:42 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:01:42 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:01:42 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:01:42 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:01:42 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:01:42 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:01:42 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:01:42 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:01:42 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:01:42 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:01:46 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:01:46 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:01:46 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:01:46 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:01:46 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:01:46 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:01:46 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:01:46 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:01:46 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:01:46 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:01:46 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:01:46 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:01:46 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:01:46 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:01:46 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:01:46 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:01:51 | story.character_randomizer | INFO     | __init__       :112  | Character randomizer initialized
2025-05-28 09:01:51 | story.item_system    | INFO     | _load_default_items:223  | Loaded 10 default items
2025-05-28 09:01:55 | gui.main_window      | INFO     | closeEvent     :832  | Main window closed
2025-05-28 09:04:02 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:08:11 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:08:12 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:08:12 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:08:12 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:08:12 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:08:12 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:08:12 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:08:12 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:08:12 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:08:12 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:08:12 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:08:12 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:08:12 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:08:12 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:08:12 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:08:12 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:08:12 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:08:13 | gui.story_editor     | INFO     | __init__       :342  | Story editor tab initialized
2025-05-28 09:08:13 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:08:13 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:08:13 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:08:13 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:08:13 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:08:13 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:08:13 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:08:13 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:08:13 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:08:13 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:08:16 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:08:16 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:08:16 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:08:16 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:08:16 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:08:16 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:08:16 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:08:16 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:08:16 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:08:16 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:08:16 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:08:16 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:08:16 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:08:16 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:08:16 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:08:17 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:08:37 | story.character_randomizer | INFO     | __init__       :112  | Character randomizer initialized
2025-05-28 09:08:37 | story.item_system    | INFO     | _load_default_items:223  | Loaded 10 default items
2025-05-28 09:08:50 | gui.main_window      | INFO     | closeEvent     :832  | Main window closed
2025-05-28 09:12:04 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:12:05 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:12:05 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:12:05 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:12:05 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:12:05 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:12:05 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:12:05 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:12:05 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:12:05 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:12:05 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:12:05 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:12:05 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:12:05 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:12:05 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:12:05 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:12:05 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:12:06 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:12:06 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:12:07 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:12:07 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:12:07 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:12:07 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:12:07 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:12:07 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:12:07 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:12:07 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:12:07 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:12:10 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:12:10 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:12:10 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:12:10 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:12:10 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:12:10 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:12:10 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:12:10 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:12:10 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:12:10 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:12:10 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:12:10 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:12:10 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:12:10 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:12:10 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:12:10 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:13:15 | social.x_auth        | INFO     | get_auth_url   :145  | Generated X OAuth authorization URL
2025-05-28 09:19:04 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:19:06 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:19:06 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:19:06 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:19:06 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:19:06 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:19:06 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:19:06 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:19:06 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:19:06 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:19:06 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:19:06 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:19:06 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:19:07 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:19:07 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:19:07 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:19:07 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:19:07 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:19:07 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:19:08 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:19:08 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:19:08 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:19:08 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:19:08 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:19:08 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:19:08 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:19:08 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:19:08 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:19:12 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:19:12 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:19:12 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:19:12 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:19:12 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:19:12 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:19:12 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:19:12 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:19:12 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:19:12 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:19:12 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:19:12 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:19:12 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:19:12 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:19:12 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:19:12 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:19:34 | gui.auth_tab         | ERROR    | _start_login   :461  | Error starting login: type object 'QDialog' has no attribute 'Accepted'
2025-05-28 09:19:37 | gui.main_window      | INFO     | closeEvent     :832  | Main window closed
2025-05-28 09:19:40 | gui.auth_tab         | INFO     | start          :170  | OAuth callback server started on port 8080
2025-05-28 09:19:40 | social.x_auth        | INFO     | get_auth_url   :145  | Generated X OAuth authorization URL
2025-05-28 09:20:15 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 09:24:40 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 09:33:02 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:33:03 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:33:03 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:33:03 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:33:03 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:33:03 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:33:03 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:33:03 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:33:03 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:33:03 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:33:03 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:33:03 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:33:03 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:33:03 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:33:03 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:33:03 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:33:03 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:33:04 | gui.main_window      | ERROR    | _update_service_menu_states:911  | Error updating service menu states: 'MainWindow' object has no attribute 'x_authenticator'
2025-05-28 09:33:04 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 09:33:04 | gui.main_window      | INFO     | _init_service_managers:78   | Service managers initialized
2025-05-28 09:33:04 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:33:04 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:33:04 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:33:04 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:33:04 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:33:04 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:33:04 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:33:04 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:33:04 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:33:04 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:33:04 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:33:08 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:33:08 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:33:08 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:33:08 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:33:08 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:33:08 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:33:08 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:33:08 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:33:08 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:33:08 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:33:08 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:33:08 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:33:08 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:33:08 | gui.help_system      | INFO     | __init__       :464  | Help tab initialized
2025-05-28 09:33:08 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:33:08 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:33:14 | gui.main_window      | ERROR    | _update_service_menu_states:911  | Error updating service menu states: 'BackupManager' object has no attribute 'is_authenticated'
2025-05-28 09:33:20 | gui.main_window      | ERROR    | toggle_drive_authentication:953  | Error toggling Google Drive authentication: 'BackupManager' object has no attribute 'is_authenticated'
2025-05-28 09:35:38 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:35:39 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:35:39 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:35:39 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:35:39 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:35:39 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:35:39 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:35:39 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:35:39 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:35:39 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:35:39 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:35:39 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:35:39 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:35:39 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:35:39 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:35:39 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:35:39 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:35:39 | gui.main_window      | WARNING  | _init_service_managers:83   | Google Drive backup manager not available: cannot import name 'GoogleDriveBackupManager' from 'backup.google_drive_backup' (/Users/<USER>/Documents/augment-projects/cyoax/src/backup/google_drive_backup.py)
2025-05-28 09:35:39 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:35:39 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:35:39 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:35:40 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:35:40 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:35:40 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:35:40 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:35:40 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:35:40 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:35:40 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:35:40 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:35:40 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:35:42 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:35:42 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:35:42 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:35:42 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:35:42 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:35:42 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:35:42 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:35:42 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:35:42 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:35:42 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:35:42 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:35:42 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:35:43 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:35:43 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:35:43 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:35:43 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:40:23 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:40:23 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:40:23 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:40:23 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:40:23 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:40:23 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:40:23 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:40:23 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:40:23 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:40:23 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:40:23 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:40:23 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:40:23 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:40:23 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:40:23 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:40:23 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:40:23 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:40:23 | gui.main_window      | WARNING  | _init_service_managers:83   | Google Drive backup manager not available: cannot import name 'GoogleDriveBackupManager' from 'backup.google_drive_backup' (/Users/<USER>/Documents/augment-projects/cyoax/src/backup/google_drive_backup.py)
2025-05-28 09:40:23 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:40:24 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:40:24 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:40:24 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:40:24 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:40:24 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:40:24 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:40:24 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:40:24 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:40:24 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:40:24 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:40:24 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:40:26 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:40:26 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:40:26 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:40:26 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:40:26 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:40:26 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:40:26 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:40:26 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:40:26 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:40:26 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:40:26 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:40:26 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:40:26 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:40:26 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:40:26 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:40:26 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:41:36 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:41:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:41:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:41:36 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:41:36 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:41:36 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:41:36 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:41:36 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:41:36 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:41:36 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:41:36 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:41:36 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:41:36 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:41:36 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:41:36 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:41:36 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:41:36 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:41:36 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 09:41:36 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:41:37 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:41:37 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:41:37 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:41:37 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:41:37 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:41:37 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:41:37 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:41:37 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:41:37 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:41:37 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:41:37 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:41:39 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:41:39 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:41:39 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:41:40 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:41:40 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:41:40 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:41:40 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:41:40 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:41:40 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:41:40 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:41:40 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:41:40 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:41:40 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:41:40 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:41:40 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:41:40 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:41:40 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:41:40 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:41:40 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:41:40 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:41:40 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:41:40 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:41:40 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:46:58 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:46:58 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:46:58 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:46:58 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:46:58 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:46:58 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:46:58 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:46:58 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:46:58 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:46:58 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:46:58 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:46:58 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:46:58 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:46:58 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:46:58 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:46:58 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:46:58 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:46:58 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 09:46:58 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:46:59 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:46:59 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:46:59 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:46:59 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:46:59 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:46:59 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:46:59 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:46:59 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:46:59 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:46:59 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:46:59 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:47:01 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:47:01 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:47:01 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:47:01 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:47:01 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:47:01 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:47:01 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:47:01 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:47:01 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:47:01 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:47:01 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:47:01 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:47:01 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:47:01 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:47:02 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:47:02 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:47:10 | gui.main_window      | ERROR    | _perform_x_authentication:1105 | X authentication failed: cannot import name 'XCallbackServer' from 'social.x_auth' (/Users/<USER>/Documents/augment-projects/cyoax/src/social/x_auth.py)
2025-05-28 09:47:56 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:47:57 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:47:57 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:47:57 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:47:57 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:47:57 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:47:57 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:47:57 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:47:57 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:47:57 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:47:57 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:47:57 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:47:57 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:47:57 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:47:57 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:47:57 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:47:57 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:47:57 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 09:47:57 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:47:57 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:47:57 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:47:57 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:47:57 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:47:57 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:47:57 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:47:57 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:47:57 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:47:57 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:47:57 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:47:57 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:47:59 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:47:59 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:47:59 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:47:59 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:47:59 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:47:59 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:47:59 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:47:59 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:47:59 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:47:59 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:47:59 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:47:59 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:47:59 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:47:59 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:47:59 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:47:59 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:48:30 | gui.auth_tab         | INFO     | start          :170  | OAuth callback server started on port 8080
2025-05-28 09:48:30 | social.x_auth        | INFO     | get_auth_url   :151  | Generated X OAuth authorization URL
2025-05-28 09:53:47 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:53:48 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:53:48 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 09:53:48 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 09:53:48 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 09:53:48 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 09:53:48 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 09:53:48 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 09:53:48 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 09:53:48 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 09:53:48 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 09:53:48 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 09:53:48 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 09:53:48 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 09:53:48 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 09:53:48 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 09:53:48 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 09:53:48 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 09:53:48 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 09:53:48 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 09:53:48 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 09:53:48 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 09:53:48 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:53:48 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:53:48 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:53:48 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:53:48 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:53:48 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:53:48 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:53:48 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:53:51 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:53:51 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:53:51 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 09:53:51 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 09:53:51 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 09:53:51 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 09:53:51 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 09:53:51 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 09:53:51 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 09:53:51 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 09:53:51 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 09:53:51 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 09:53:51 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 09:53:51 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 09:53:51 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 09:53:51 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 09:53:56 | gui.auth_tab         | INFO     | start          :170  | OAuth callback server started on port 8080
2025-05-28 09:53:56 | social.x_auth        | INFO     | get_auth_url   :151  | Generated X OAuth authorization URL
2025-05-28 09:54:45 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 09:54:48 | gui.auth_tab         | INFO     | start          :170  | OAuth callback server started on port 8080
2025-05-28 09:54:48 | social.x_auth        | INFO     | get_auth_url   :151  | Generated X OAuth authorization URL
2025-05-28 09:57:48 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 09:57:57 | gui.auth_tab         | INFO     | start          :170  | OAuth callback server started on port 8080
2025-05-28 09:57:57 | social.x_auth        | INFO     | get_auth_url   :151  | Generated X OAuth authorization URL
2025-05-28 09:58:56 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 09:58:56 | gui.auth_tab         | INFO     | stop           :210  | OAuth callback server stopped
2025-05-28 10:00:43 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:00:43 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:00:43 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:00:44 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 10:00:44 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 10:00:44 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 10:00:44 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 10:00:44 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 10:00:44 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 10:00:44 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 10:00:44 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 10:00:44 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 10:00:44 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 10:00:44 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 10:00:44 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 10:00:44 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 10:00:44 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 10:00:44 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 10:00:44 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 10:00:44 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 10:00:44 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 10:00:44 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 10:00:44 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:00:44 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:00:44 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:00:44 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:00:44 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:00:44 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:00:44 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:00:44 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:00:47 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:00:47 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:00:47 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 10:00:47 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:00:47 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:00:47 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:00:47 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:00:47 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:00:47 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:00:47 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:00:47 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:00:47 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:00:47 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 10:00:47 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 10:00:47 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 10:00:47 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 10:00:51 | gui.auth_tab         | INFO     | start          :183  | OAuth callback server started on port 8080
2025-05-28 10:00:51 | social.x_auth        | INFO     | get_auth_url   :152  | Generated X OAuth authorization URL
2025-05-28 10:01:27 | gui.auth_tab         | INFO     | stop           :223  | OAuth callback server stopped
2025-05-28 10:01:27 | gui.auth_tab         | INFO     | stop           :223  | OAuth callback server stopped
2025-05-28 10:01:30 | gui.auth_tab         | INFO     | start          :183  | OAuth callback server started on port 8080
2025-05-28 10:01:31 | social.x_auth        | INFO     | get_auth_url   :152  | Generated X OAuth authorization URL
2025-05-28 10:03:13 | gui.auth_tab         | INFO     | stop           :223  | OAuth callback server stopped
2025-05-28 10:03:13 | gui.auth_tab         | INFO     | stop           :223  | OAuth callback server stopped
2025-05-28 10:03:32 | gui.auth_tab         | INFO     | start          :183  | OAuth callback server started on port 8080
2025-05-28 10:03:32 | social.x_auth        | INFO     | get_auth_url   :152  | Generated X OAuth authorization URL
2025-05-28 10:08:50 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:08:51 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:08:51 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:08:51 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 10:08:51 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 10:08:51 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 10:08:51 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 10:08:51 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 10:08:51 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 10:08:51 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 10:08:51 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 10:08:51 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 10:08:51 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 10:08:51 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 10:08:51 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 10:08:51 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 10:08:51 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 10:08:51 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 10:08:51 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 10:08:51 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 10:08:51 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 10:08:51 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 10:08:52 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:08:52 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:08:52 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:08:52 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:08:52 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:08:52 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:08:52 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:08:52 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:08:53 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:08:53 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:08:53 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:08:53 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:08:53 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:08:53 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 10:08:53 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:08:53 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:08:53 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:08:53 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:08:53 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:08:53 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:08:53 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:08:53 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:08:54 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:08:54 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:08:54 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:08:54 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:08:54 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:08:54 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 10:08:54 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 10:08:54 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 10:08:54 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 10:20:35 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:20:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:20:36 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:20:36 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 10:20:36 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 10:20:36 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 10:20:36 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 10:20:36 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 10:20:36 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 10:20:36 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 10:20:36 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 10:20:36 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 10:20:36 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 10:20:36 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 10:20:36 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 10:20:36 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 10:20:36 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 10:20:36 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 10:20:36 | gui.main_window      | INFO     | _init_service_managers:86   | Service managers initialized
2025-05-28 10:20:36 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 10:20:36 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 10:20:36 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 10:20:36 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:20:36 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:20:36 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:20:36 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:20:36 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:20:36 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:20:36 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:20:36 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:20:39 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:20:39 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:20:39 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 10:20:39 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:20:39 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:20:39 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:20:39 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:20:39 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:20:39 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:20:39 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:20:39 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:20:39 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:20:39 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 10:20:39 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 10:20:39 | gui.main_window      | INFO     | __init__       :65   | Main window initialized
2025-05-28 10:20:39 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 10:26:21 | story.character_randomizer | INFO     | __init__       :112  | Character randomizer initialized
2025-05-28 10:26:21 | story.item_system    | INFO     | _load_default_items:223  | Loaded 10 default items
2025-05-28 10:32:34 | gui.graph_viewer     | INFO     | auto_layout    :246  | Auto layout requested
2025-05-28 10:37:51 | gui.chat_assistant_new | INFO     | show_sidebar   :522  | Chat sidebar shown
2025-05-28 10:38:23 | gui.chat_assistant_new | INFO     | hide_sidebar   :532  | Chat sidebar hidden
2025-05-28 10:49:43 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:49:44 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:49:44 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 10:49:44 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 10:49:44 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 10:49:44 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 10:49:44 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 10:49:44 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 10:49:44 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 10:49:44 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 10:49:44 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 10:49:44 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 10:49:44 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 10:49:44 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 10:49:44 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 10:49:44 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 10:49:44 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 10:49:44 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 10:49:44 | gui.main_window      | INFO     | _init_service_managers:89   | Service managers initialized
2025-05-28 10:49:44 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 10:49:45 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 10:49:45 | gui.media_generation_tab | INFO     | __init__       :125  | Media generation tab initialized
2025-05-28 10:49:45 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 10:49:45 | gui.settings_tab     | INFO     | __init__       :70   | Settings tab initialized
2025-05-28 10:49:45 | gui.logs_tab         | INFO     | __init__       :115  | Logs tab initialized
2025-05-28 10:49:45 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:49:45 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:49:45 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:49:45 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:49:45 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:49:45 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:49:45 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:49:45 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:49:47 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:49:47 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:49:47 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 10:49:47 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 10:49:47 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 10:49:47 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 10:49:47 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 10:49:47 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 10:49:47 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 10:49:47 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 10:49:47 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 10:49:47 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 10:49:47 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 10:49:47 | gui.help_system      | INFO     | __init__       :651  | Help tab initialized
2025-05-28 10:49:47 | gui.main_window      | INFO     | __init__       :68   | Main window initialized
2025-05-28 10:49:47 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 10:49:47 | gui.settings_tab     | INFO     | _on_models_discovered:419  | Discovered 3 LM Studio models, 0 ComfyUI models
2025-05-28 11:02:18 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:02:18 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:02:18 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:02:18 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 11:02:18 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 11:02:18 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 11:02:18 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 11:02:18 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 11:02:18 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 11:02:18 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 11:02:18 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 11:02:18 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 11:02:18 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 11:02:18 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 11:02:18 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 11:02:18 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 11:02:18 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 11:02:18 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 11:02:18 | gui.main_window      | INFO     | _init_service_managers:89   | Service managers initialized
2025-05-28 11:02:19 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 11:02:19 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 11:02:19 | gui.media_generation_tab | INFO     | __init__       :125  | Media generation tab initialized
2025-05-28 11:02:19 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 11:02:19 | gui.quiz_tab         | INFO     | _load_templates:361  | Loaded 2 quiz templates
2025-05-28 11:02:19 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 11:02:19 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 11:02:19 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 11:02:19 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 11:02:19 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 11:02:19 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 11:02:19 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 11:02:19 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 11:02:21 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 11:02:21 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 11:02:21 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 11:02:21 | ai.rag_system        | WARNING  | _initialize_model:52   | SentenceTransformers not available - RAG system disabled
2025-05-28 11:02:21 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 11:02:21 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 11:02:21 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 11:02:21 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 11:02:21 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 11:02:21 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 11:02:21 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 11:02:21 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 11:02:21 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 11:02:21 | gui.main_window      | INFO     | __init__       :68   | Main window initialized
2025-05-28 11:02:21 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 11:02:53 | gui.chat_assistant_new | INFO     | show_sidebar   :522  | Chat sidebar shown
2025-05-28 11:14:47 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:14:48 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:14:48 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:14:48 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 11:14:48 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 11:14:48 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 11:14:48 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 11:14:48 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 11:14:48 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 11:14:48 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 11:14:48 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 11:14:48 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 11:14:48 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 11:14:48 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 11:14:48 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 11:14:48 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 11:14:48 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 11:14:49 | backup.google_drive_backup | ERROR    | _authenticate  :99   | Credentials file not found: credentials.json
2025-05-28 11:14:49 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 11:14:49 | gui.main_window      | INFO     | _init_service_managers:89   | Service managers initialized
2025-05-28 11:14:49 | gui.story_editor     | INFO     | __init__       :344  | Story editor tab initialized
2025-05-28 11:14:49 | gui.graph_viewer     | INFO     | __init__       :37   | Graph viewer tab initialized
2025-05-28 11:14:49 | gui.media_generation_tab | INFO     | __init__       :125  | Media generation tab initialized
2025-05-28 11:14:49 | gui.x_manager        | INFO     | __init__       :140  | X manager tab initialized
2025-05-28 11:14:49 | gui.quiz_tab         | INFO     | _load_templates:361  | Loaded 2 quiz templates
2025-05-28 11:14:49 | sentence_transformers.SentenceTransformer | INFO     | __init__       :211  | Use pytorch device_name: mps
2025-05-28 11:14:49 | sentence_transformers.SentenceTransformer | INFO     | __init__       :219  | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 11:15:12 | ai.rag_system        | INFO     | _initialize_model:59   | Loaded RAG model: all-MiniLM-L6-v2
2025-05-28 11:15:14 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 11:15:14 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 11:15:14 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 11:15:14 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 11:15:14 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 11:15:14 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 11:15:14 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 11:15:16 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 11:15:16 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 11:15:16 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 11:15:16 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 11:15:16 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 11:15:16 | gui.chat_assistant_new | INFO     | __init__       :476  | Chat assistant sidebar initialized
2025-05-28 11:15:16 | sentence_transformers.SentenceTransformer | INFO     | __init__       :211  | Use pytorch device_name: mps
2025-05-28 11:15:16 | sentence_transformers.SentenceTransformer | INFO     | __init__       :219  | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 11:15:18 | ai.rag_system        | INFO     | _initialize_model:59   | Loaded RAG model: all-MiniLM-L6-v2
2025-05-28 11:15:18 | ai.rag_system        | INFO     | _load_app_knowledge:134  | Loaded 11 app knowledge documents
2025-05-28 11:15:18 | ai.rag_system        | INFO     | __init__       :47   | RAG system initialized
2025-05-28 11:15:18 | ai.chat_agent        | INFO     | __init__       :32   | App controller initialized
2025-05-28 11:15:18 | ai.chat_agent        | INFO     | __init__       :71   | Chat agent initialized
2025-05-28 11:15:18 | audio.speech_to_text | WARNING  | __init__       :290  | Speech recognition not available - install speech_recognition and pyaudio
2025-05-28 11:15:18 | audio.text_to_speech | INFO     | _load_profiles :192  | Loaded 4 voice profiles
2025-05-28 11:15:18 | audio.text_to_speech | INFO     | __init__       :179  | Voice profile manager initialized with 4 profiles
2025-05-28 11:15:18 | audio.text_to_speech | INFO     | __init__       :81   | Pyttsx3 TTS engine initialized
2025-05-28 11:15:18 | audio.text_to_speech | INFO     | _initialize_engines:302  | Initialized 1 TTS engines
2025-05-28 11:15:18 | audio.text_to_speech | INFO     | __init__       :291  | TTS manager initialized
2025-05-28 11:15:18 | ai.chat_agent        | INFO     | register_app_actions:105  | Registered 7 app actions
2025-05-28 11:15:18 | gui.chat_assistant_new | INFO     | __init__       :82   | Chat assistant widget initialized
2025-05-28 11:15:18 | gui.chat_assistant_new | INFO     | __init__       :560  | Chat assistant floating window initialized
2025-05-28 11:15:18 | gui.main_window      | INFO     | __init__       :68   | Main window initialized
2025-05-28 11:15:18 | main.app             | INFO     | run_gui        :263  | ✅ GUI application started
2025-05-28 11:24:45 | gui.chat_assistant_new | INFO     | show_sidebar   :522  | Chat sidebar shown
2025-05-28 11:27:31 | gui.chat_assistant_new | INFO     | hide_sidebar   :532  | Chat sidebar hidden
2025-05-28 11:29:17 | gui.graph_viewer     | INFO     | auto_layout    :246  | Auto layout requested
2025-05-28 11:31:13 | gui.main_window      | INFO     | closeEvent     :989  | Main window closed
2025-05-28 11:32:54 | utils.logging_config | [32mINFO[0m | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:32:54 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:32:54 | utils.logging_config | INFO     | _setup_logging :192  | 🚀 Logging system initialized - Verbosity: INFO
2025-05-28 11:32:54 | system               | INFO     | log_system_info:338  | 🖥️ System Information:
2025-05-28 11:32:54 | system               | INFO     | log_system_info:339  |   Platform: macOS-15.0-arm64-arm-64bit
2025-05-28 11:32:54 | system               | INFO     | log_system_info:340  |   Python: 3.11.12
2025-05-28 11:32:54 | system               | INFO     | log_system_info:341  |   CPU: arm
2025-05-28 11:32:54 | system               | INFO     | log_system_info:342  |   Memory: 16.0 GB
2025-05-28 11:32:54 | system               | INFO     | log_system_info:343  |   Disk: 926.4 GB
2025-05-28 11:32:54 | main.config          | INFO     | load_config    :64   | Configuration loaded successfully
2025-05-28 11:32:54 | main.env             | WARNING  | load_environment:100  | .env file not found, using system environment variables
2025-05-28 11:32:54 | main.setup           | INFO     | create_directories:122  | Created necessary directories
2025-05-28 11:32:54 | main.dependencies    | INFO     | check_dependencies:138  | LM Studio connection verified
2025-05-28 11:32:54 | main.dependencies    | WARNING  | check_dependencies:154  | ComfyUI server not available (optional)
2025-05-28 11:32:54 | main.app             | INFO     | _initialize_components:239  | ✅ Core components initialized
2025-05-28 11:32:54 | main.app             | INFO     | initialize     :214  | 🚀 CYOA Automation System initialized successfully
2025-05-28 11:32:54 | main.app             | INFO     | run_gui        :245  | 🎨 Starting GUI application...
2025-05-28 11:32:54 | backup.google_drive_backup | ERROR    | _authenticate  :99   | Credentials file not found: credentials.json
2025-05-28 11:32:54 | backup.google_drive_backup | INFO     | __init__       :344  | Backup manager initialized with 4 items
2025-05-28 11:32:54 | gui.main_window      | INFO     | _init_service_managers:89   | Service managers initialized
