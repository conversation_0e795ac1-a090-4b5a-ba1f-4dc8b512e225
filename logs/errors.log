2025-05-28 07:32:16 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'Horizontal'
2025-05-28 07:33:51 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:35:44 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:37:33 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:38:52 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFont' has no attribute 'Bold'
2025-05-28 07:40:07 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QGraphicsView' has no attribute 'RubberBandDrag'
2025-05-28 07:41:34 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QFrame' has no attribute 'StyledPanel'
2025-05-28 07:44:17 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'AlignCenter'
2025-05-28 07:47:40 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'Qt' has no attribute 'PointingHandCursor'
2025-05-28 07:48:54 | main.app             | ERROR    | run_gui        :267  | Error running GUI: type object 'QLineEdit' has no attribute 'Password'
2025-05-28 07:50:06 | main.app             | ERROR    | run_gui        :267  | Error running GUI: 'QApplication' object has no attribute 'exec_'
2025-05-28 08:24:35 | gui.main_window      | ERROR    | show_story_wizard:380  | Error showing story wizard: type object 'QWizard' has no attribute 'ModernStyle'
2025-05-28 08:42:22 | gui.main_window      | ERROR    | show_story_wizard:384  | Error showing story wizard: type object 'QWizard' has no attribute 'HaveHelpButton'
2025-05-28 08:48:48 | gui.main_window      | ERROR    | show_story_wizard:384  | Error showing story wizard: registerField(self, name: Optional[str], widget: Optional[QWidget], property: Optional[str] = None, changedSignal: PYQT_SIGNAL = None): argument 2 has unexpected type 'QButtonGroup'
2025-05-28 08:53:05 | gui.auth_tab         | ERROR    | _start_login   :461  | Error starting login: type object 'QDialogButtonBox' has no attribute 'Ok'
2025-05-28 08:58:23 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 08:58:29 | gui.main_window      | ERROR    | new_story      :471  | Error creating new story: StoryNode.__init__() got an unexpected keyword argument 'title'
2025-05-28 08:59:39 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 08:59:44 | gui.main_window      | ERROR    | new_story      :471  | Error creating new story: StoryNode.__init__() got an unexpected keyword argument 'title'
2025-05-28 09:00:30 | gui.main_window      | ERROR    | show_story_wizard:385  | Error showing story wizard: 'StoryCreationWizard' object has no attribute 'Accepted'
2025-05-28 09:19:34 | gui.auth_tab         | ERROR    | _start_login   :461  | Error starting login: type object 'QDialog' has no attribute 'Accepted'
2025-05-28 09:33:04 | gui.main_window      | ERROR    | _update_service_menu_states:911  | Error updating service menu states: 'MainWindow' object has no attribute 'x_authenticator'
2025-05-28 09:33:14 | gui.main_window      | ERROR    | _update_service_menu_states:911  | Error updating service menu states: 'BackupManager' object has no attribute 'is_authenticated'
2025-05-28 09:33:20 | gui.main_window      | ERROR    | toggle_drive_authentication:953  | Error toggling Google Drive authentication: 'BackupManager' object has no attribute 'is_authenticated'
2025-05-28 09:47:10 | gui.main_window      | ERROR    | _perform_x_authentication:1105 | X authentication failed: cannot import name 'XCallbackServer' from 'social.x_auth' (/Users/<USER>/Documents/augment-projects/cyoax/src/social/x_auth.py)
