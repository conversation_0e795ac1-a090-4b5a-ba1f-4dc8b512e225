# 🚀 Getting Started with CYOA Automation System

Welcome to the Choose Your Own Adventure Automation System! This guide will walk you through creating your first interactive story and posting it to X (Twitter).

## 📋 Prerequisites

Before you begin, make sure you have:

- **Pixi** package manager installed
- **Ollama** (will be set up automatically)
- **ComfyUI** (optional, for video generation)
- **X API credentials** (optional, for posting)

All Python dependencies are managed automatically by Pixi!

## 🎯 Quick Start (2 Minutes)

### 1. One-Command Setup
```bash
# Install Pixi (if needed)
curl -fsSL https://pixi.sh/install.sh | bash

# Setup everything automatically
pixi run first-run
```

### 2. Create Your First Story
```bash
# Launch the beautiful CLI wizard
pixi run wizard
```

Follow the prompts:
1. **Story Type**: Choose "Import from existing text"
2. **Basic Info**: Enter a title like "My First Adventure"
3. **Source Content**: Paste any story text or use the sample
4. **Watch the Magic**: AI extracts characters and creates paths

### 3. Explore Your Story
```bash
# Launch the full GUI to see your creation
pixi run run
```

- **Story Editor Tab**: View and edit story nodes
- **Characters Tab**: See extracted characters with personalities
- **Graph Viewer Tab**: Visualize story structure
- **X Manager Tab**: Generate media and post to X

### 4. Check System Health (Optional)
```bash
# Verify everything is working perfectly
pixi run health
```

## 📖 Detailed Walkthrough

### Story Creation Options

#### Option 1: Import Existing Text
Perfect for adapting books, stories, or scripts:

1. **Prepare Your Text**: Copy text from a book, story, or script
2. **Use the Wizard**: Select "Import from existing text"
3. **Paste Content**: Add your text in the Source Content page
4. **AI Processing**: The system will:
   - Extract characters and their traits
   - Identify key plot points
   - Create branching story paths
   - Generate interactive choices

#### Option 2: Generate from Theme
Create entirely new stories:

1. **Choose Theme**: Select "Generate new story from theme/prompt"
2. **Enter Prompt**: Example: "A magical academy where students learn forbidden spells"
3. **AI Creation**: The system generates a complete story from scratch

#### Option 3: Use Templates
Start with proven story structures:

1. **Select Template**: Choose from Fantasy Adventure, Mystery, Sci-Fi, etc.
2. **Customize**: Modify characters, settings, and plot points
3. **Generate**: Create a story based on the template

### Understanding Story Components

#### **Story Nodes**
- Each node is a story segment with text and choices
- **Entry Points**: Where readers can start the story
- **Story Nodes**: Main narrative segments
- **Ending Nodes**: Conclusion points (success, death, etc.)

#### **Characters**
- **Protagonist**: The player character (you)
- **Antagonist**: Main villain or opposing force
- **Allies**: Helpful characters
- **Mentors**: Wise guides
- **Background**: Minor characters

#### **Player Classes**
- **Mage**: Magic-focused, intelligent, scholarly
- **Ranger**: Nature-focused, skilled, independent
- **Charmer**: Social-focused, persuasive, charismatic

### Media Generation

#### **Video Generation**
- Creates visual content for each story node
- Uses AI to generate appropriate scenes
- Adds choice overlays for interactivity
- Requires ComfyUI for best results

#### **Audio Generation**
- Text-to-speech narration for each node
- Character-specific voices
- Background music and effects
- Lip-sync data for video integration

### X (Twitter) Integration

#### **Posting Strategy 1: Link Navigation** (Current)
- Posts videos with choice links above
- Links point to subsequent story posts
- Works with standard X API
- Immediate implementation

#### **Posting Strategy 2: Video Ads** (Future)
- Videos with clickable overlays
- Direct navigation between story nodes
- Requires X Ads API access
- Higher engagement potential

### Content Monetization

#### **Subscription Tiers**
- **Free**: Basic story access
- **Premium ($4.99/month)**: Extended content, early access
- **Spicy ($9.99/month)**: Adult content, exclusive stories

#### **Content Gating**
- Premium content requires subscription
- Spicy content has content warnings
- Free users see teasers with subscription prompts

## 🛠️ Troubleshooting

### Common Issues

#### "Ollama not available"
```bash
# Install Ollama
curl -fsSL https://ollama.com/install.sh | sh

# Start Ollama
ollama serve

# Download a model
ollama pull llama3.1
```

#### "ComfyUI not responding"
```bash
# Navigate to ComfyUI directory
cd ComfyUI

# Start ComfyUI
python main.py
```

#### "Story generation failed"
- Check that Ollama is running
- Verify you have a model downloaded
- Try with shorter source text
- Check the generation log for specific errors

#### "No video generated"
- ComfyUI is optional - stories work without it
- Check ComfyUI is running at http://127.0.0.1:8188
- Verify ComfyUI models are downloaded
- Enable fallback generation in settings

### Getting Help

1. **System Health Check**: Tools → System Health Check
2. **Check Logs**: Look in the `logs/` directory
3. **Validate Story**: Tools → Validate Story
4. **Review Configuration**: Check `config.json` settings

## 🎨 Customization

### Story Settings
Edit `config.json` to customize:

```json
{
  "story_generation": {
    "num_entry_points": 3,
    "max_nodes": 50,
    "min_deaths": 3,
    "min_successes": 2
  }
}
```

### Media Settings
```json
{
  "media_generation": {
    "video_quality": "720p",
    "video_duration_seconds": 10,
    "generate_audio": true
  }
}
```

### Character Settings
```json
{
  "character_system": {
    "auto_extract_characters": true,
    "track_character_consistency": true,
    "max_characters_per_story": 20
  }
}
```

## 🚀 Advanced Features

### Batch Processing
- Generate multiple stories from a list
- Bulk media generation
- Scheduled posting

### Analytics
- Track story performance
- Monitor engagement metrics
- A/B test different approaches

### Templates
- Create custom story templates
- Share templates with others
- Build template libraries

## 📈 Best Practices

### Story Creation
1. **Start Small**: Begin with 10-15 nodes
2. **Clear Choices**: Make options distinct and meaningful
3. **Character Consistency**: Use the character system
4. **Multiple Paths**: Ensure multiple routes to success
5. **Engaging Hooks**: Start with compelling scenarios

### Social Media
1. **Consistent Posting**: Regular schedule builds audience
2. **Engage Readers**: Respond to comments and feedback
3. **Tease Content**: Preview upcoming stories
4. **Cross-Promote**: Share on multiple platforms
5. **Analytics**: Monitor what works best

### Monetization
1. **Value First**: Provide free content to build audience
2. **Premium Quality**: Make paid content significantly better
3. **Clear Benefits**: Explain subscription advantages
4. **Fair Pricing**: Research competitor pricing
5. **Retention**: Focus on keeping subscribers happy

## 🎯 Next Steps

Once you've created your first story:

1. **Experiment**: Try different genres and styles
2. **Optimize**: Use analytics to improve performance
3. **Scale**: Create multiple stories and series
4. **Monetize**: Set up subscription tiers
5. **Community**: Build an audience around your stories

## 🆘 Support

If you need help:

1. **Check Documentation**: Review all markdown files
2. **Run Health Check**: Use the built-in diagnostic tool
3. **Check Logs**: Look for error messages in logs/
4. **Test Components**: Use the test scripts in scripts/
5. **Start Simple**: Begin with basic features and add complexity

## 🎉 Success!

You're now ready to create engaging Choose Your Own Adventure stories and share them with the world! The system handles the technical complexity while you focus on creating amazing content.

**Happy storytelling!** 📚✨
