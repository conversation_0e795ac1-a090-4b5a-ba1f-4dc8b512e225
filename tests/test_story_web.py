"""
Tests for story web functionality
"""

import pytest
import json
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from story.story_web import StoryWeb, StoryNode, Choice, NodeType, EndingType


class TestStoryWeb:
    """Test cases for StoryWeb class"""
    
    def test_create_empty_web(self):
        """Test creating an empty story web"""
        config = {"max_nodes": 100}
        web = StoryWeb(config)
        
        assert len(web.nodes) == 0
        assert len(web.entry_points) == 0
        assert len(web.endings) == 0
        assert web.config == config
    
    def test_add_node(self):
        """Test adding nodes to the web"""
        web = StoryWeb()
        
        node = StoryNode(
            id="test_node",
            text="Test node text",
            node_type=NodeType.STORY,
            is_entry=True
        )
        
        result = web.add_node(node)
        
        assert result is True
        assert "test_node" in web.nodes
        assert "test_node" in web.entry_points
        assert web.nodes["test_node"].text == "Test node text"
    
    def test_add_choice(self):
        """Test adding choices between nodes"""
        web = StoryWeb()
        
        # Create two nodes
        node1 = StoryNode(id="node1", text="First node", node_type=NodeType.STORY)
        node2 = StoryNode(id="node2", text="Second node", node_type=NodeType.STORY)
        
        web.add_node(node1)
        web.add_node(node2)
        
        # Create choice
        choice = Choice(
            id="choice1",
            text="Go to second node",
            target_node_id="node2"
        )
        
        result = web.add_choice("node1", choice)
        
        assert result is True
        assert len(web.nodes["node1"].choices) == 1
        assert web.nodes["node1"].choices[0].target_node_id == "node2"
    
    def test_validate_structure(self):
        """Test story web validation"""
        web = StoryWeb({"num_entry_points": 1, "min_endings": 1})
        
        # Create valid structure
        entry_node = StoryNode(
            id="entry",
            text="Start",
            node_type=NodeType.ENTRY,
            is_entry=True
        )
        
        ending_node = StoryNode(
            id="ending",
            text="The End",
            node_type=NodeType.ENDING,
            is_ending=True,
            ending_type=EndingType.SUCCESS
        )
        
        web.add_node(entry_node)
        web.add_node(ending_node)
        
        choice = Choice(id="c1", text="Continue", target_node_id="ending")
        web.add_choice("entry", choice)
        
        is_valid, errors = web.validate_structure()
        
        assert is_valid is True
        assert len(errors) == 0
    
    def test_serialization(self):
        """Test saving and loading story web"""
        web = StoryWeb()
        
        node = StoryNode(
            id="test",
            text="Test node",
            node_type=NodeType.STORY,
            rating="safe"
        )
        
        web.add_node(node)
        
        # Convert to dict
        data = web.to_dict()
        
        # Create new web from dict
        new_web = StoryWeb.from_dict(data)
        
        assert len(new_web.nodes) == 1
        assert "test" in new_web.nodes
        assert new_web.nodes["test"].text == "Test node"
        assert new_web.nodes["test"].rating == "safe"


class TestStoryNode:
    """Test cases for StoryNode class"""
    
    def test_create_node(self):
        """Test creating a story node"""
        node = StoryNode(
            id="test_node",
            text="This is a test node",
            node_type=NodeType.STORY,
            is_entry=False,
            is_ending=False,
            rating="safe"
        )
        
        assert node.id == "test_node"
        assert node.text == "This is a test node"
        assert node.node_type == NodeType.STORY
        assert node.is_entry is False
        assert node.is_ending is False
        assert node.rating == "safe"
        assert len(node.choices) == 0
    
    def test_ending_node(self):
        """Test creating an ending node"""
        node = StoryNode(
            id="death_ending",
            text="You died. The End.",
            node_type=NodeType.ENDING,
            is_ending=True,
            ending_type=EndingType.DEATH,
            score=75.5
        )
        
        assert node.is_ending is True
        assert node.ending_type == EndingType.DEATH
        assert node.score == 75.5


class TestChoice:
    """Test cases for Choice class"""
    
    def test_create_choice(self):
        """Test creating a choice"""
        choice = Choice(
            id="choice1",
            text="Take the left path",
            target_node_id="left_node",
            inventory_requirements={"gold": 10},
            inventory_changes={"gold": -10, "key": 1},
            class_requirements=["Mage"],
            is_premium=False,
            is_spicy=False
        )
        
        assert choice.id == "choice1"
        assert choice.text == "Take the left path"
        assert choice.target_node_id == "left_node"
        assert choice.inventory_requirements["gold"] == 10
        assert choice.inventory_changes["gold"] == -10
        assert choice.inventory_changes["key"] == 1
        assert "Mage" in choice.class_requirements
        assert choice.is_premium is False
        assert choice.is_spicy is False


if __name__ == "__main__":
    pytest.main([__file__])
